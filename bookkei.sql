-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Servidor: 127.0.0.1
-- Tiempo de generación: 29-05-2025 a las 02:44:31
-- Versión del servidor: 10.4.32-MariaDB
-- Versión de PHP: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Base de datos: `bookkei`
--

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `availability_blocks`
--

CREATE TABLE `availability_blocks` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_branch_id` bigint(20) UNSIGNED DEFAULT NULL,
  `resource_id` bigint(20) UNSIGNED DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `block_type` enum('maintenance','holiday','private_event','staff_break','other') NOT NULL,
  `affects_all_resources` tinyint(1) NOT NULL DEFAULT 0,
  `affected_services` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`affected_services`)),
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `recurrence_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`recurrence_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `bookings`
--

CREATE TABLE `bookings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_number` varchar(255) NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_branch_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `total_duration_minutes` int(11) NOT NULL,
  `participant_count` int(11) NOT NULL DEFAULT 1,
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL,
  `deposit_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `paid_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `status` enum('pending','confirmed','in_progress','completed','cancelled','no_show') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','partial','paid','refunded') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `special_requests` text DEFAULT NULL,
  `internal_notes` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `recurring_group_id` bigint(20) UNSIGNED DEFAULT NULL,
  `recurring_sequence` int(11) DEFAULT NULL,
  `recurrence_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`recurrence_data`)),
  `cancelled_at` datetime DEFAULT NULL,
  `cancelled_by` varchar(255) DEFAULT NULL,
  `cancellation_reason` text DEFAULT NULL,
  `checked_in_at` datetime DEFAULT NULL,
  `checked_out_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `bookings`
--

INSERT INTO `bookings` (`id`, `booking_number`, `business_id`, `business_branch_id`, `customer_id`, `customer_name`, `customer_email`, `customer_phone`, `start_datetime`, `end_datetime`, `total_duration_minutes`, `participant_count`, `subtotal`, `tax_amount`, `discount_amount`, `total_amount`, `deposit_amount`, `paid_amount`, `status`, `payment_status`, `notes`, `special_requests`, `internal_notes`, `metadata`, `is_recurring`, `recurring_group_id`, `recurring_sequence`, `recurrence_data`, `cancelled_at`, `cancelled_by`, `cancellation_reason`, `checked_in_at`, `checked_out_at`, `created_at`, `updated_at`) VALUES
(1, 'BK2505232781', 1, NULL, 1, 'Super Admin', '<EMAIL>', '+1234567890', '2025-05-24 15:10:52', '2025-05-24 16:10:52', 60, 1, 100.00, 0.00, 0.00, 100.00, 0.00, 0.00, 'pending', 'pending', 'Test booking created for testing views', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-23 19:10:52', '2025-05-23 19:10:52'),
(2, 'BK2505238002', 2, NULL, NULL, 'Juan manuel', '<EMAIL>', '8095864796', '2025-05-29 17:00:00', '2025-05-29 18:00:00', 60, 1, 20.00, 0.00, 0.00, 20.00, 0.00, 0.00, 'pending', 'pending', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-24 01:49:55', '2025-05-24 01:49:55'),
(3, 'BK2505257771', 5, NULL, NULL, 'Yonathan De la cruz 03-032193', '<EMAIL>', '8292856674', '2025-05-28 12:03:00', '2025-05-28 13:43:00', 100, 1, 16.96, 0.00, 0.00, 16.96, 0.00, 16.96, 'confirmed', 'paid', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-25 20:04:24', '2025-05-25 20:04:24'),
(4, 'BK2505256520', 5, NULL, NULL, 'Yonathan de la cruz', '<EMAIL>', '8292856674', '2025-05-27 12:11:00', '2025-05-27 14:11:00', 120, 1, 23.75, 0.00, 0.00, 23.75, 0.00, 23.75, 'confirmed', 'paid', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-25 20:12:05', '2025-05-25 20:12:05'),
(5, 'BK2505253367', 5, NULL, NULL, 'Yonathan de la cruz', '<EMAIL>', '8292856674', '2025-05-29 12:12:00', '2025-05-29 13:12:00', 60, 1, 10.18, 0.00, 0.00, 10.18, 0.00, 0.00, 'completed', 'pending', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 00:16:42', '2025-05-29 00:17:05', '2025-05-25 20:12:48', '2025-05-29 04:17:05'),
(6, 'BK2505257574', 5, NULL, 12, 'Yonathan De la cruz 03-032193', '<EMAIL>', '8292856674', '2025-05-30 12:21:00', '2025-05-30 13:21:00', 60, 1, 10.18, 0.00, 0.00, 10.18, 0.00, 0.00, 'confirmed', 'partial', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-25 20:21:21', '2025-05-25 20:21:21'),
(7, 'BK2505264786', 5, NULL, NULL, 'Adriana', '<EMAIL>', '', '2025-05-27 22:28:00', '2025-05-27 23:08:00', 40, 1, 5.09, 0.00, 0.00, 5.09, 0.00, 0.00, 'pending', 'pending', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-26 05:35:06', '2025-05-26 05:35:06'),
(8, 'BK2505260921', 5, NULL, NULL, 'Adriana', '<EMAIL>', '', '2025-05-27 22:28:00', '2025-05-27 23:08:00', 40, 1, 5.09, 0.00, 0.00, 5.09, 0.00, 0.00, 'completed', 'pending', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 09:55:10', '2025-05-27 09:56:11', '2025-05-26 05:35:51', '2025-05-27 13:56:11'),
(9, 'BK2505263803', 5, NULL, NULL, 'Adriana', '<EMAIL>', '', '2025-05-27 22:28:00', '2025-05-27 23:08:00', 40, 1, 5.09, 0.00, 0.00, 5.09, 0.00, 0.00, 'pending', 'pending', 'test', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-26 05:40:08', '2025-05-26 05:40:08'),
(10, 'BK2505269350', 5, NULL, 17, 'Raul mendez', '<EMAIL>', '8292854569', '2025-05-28 16:58:00', '2025-05-28 18:58:00', 120, 1, 23.75, 0.00, 0.00, 23.75, 0.00, 0.00, 'confirmed', 'pending', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 00:59:33', '2025-05-27 00:59:33'),
(11, 'BK2505288635', 5, NULL, NULL, 'Test Customer', '<EMAIL>', '123-456-7890', '2025-05-31 11:00:00', '2025-05-31 12:00:00', 60, 1, 10.18, 0.00, 0.00, 10.18, 0.00, 0.00, 'confirmed', 'pending', 'Test booking from debug page', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-28 18:09:24', '2025-05-28 18:09:24'),
(12, 'BK2505288565', 5, NULL, NULL, 'Tatiana Perez', '<EMAIL>', '8472856674', '2025-05-29 10:00:00', '2025-05-29 11:40:00', 100, 1, 20.36, 0.00, 0.00, 20.36, 0.00, 0.00, 'no_show', 'pending', 'probando', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 00:16:48', NULL, '2025-05-28 18:10:41', '2025-05-29 04:17:00'),
(13, 'BK2505284637', 5, NULL, NULL, 'Yonathan de la cruz', '<EMAIL>', '8292856674', '2025-05-29 15:00:00', '2025-05-29 16:15:00', 75, 1, 11.87, 0.00, 0.00, 11.87, 0.00, 0.00, 'completed', 'pending', 'probando', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 00:16:51', '2025-05-29 00:17:08', '2025-05-28 18:13:49', '2025-05-29 04:17:08'),
(14, 'BK2505284234', 5, NULL, NULL, 'Tatiana Perez', '<EMAIL>', '8292856674', '2025-05-30 14:00:00', '2025-05-30 14:40:00', 40, 1, 5.09, 0.00, 0.00, 5.09, 0.00, 0.00, 'confirmed', 'pending', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-28 18:23:54', '2025-05-28 18:23:54'),
(15, 'BK2505294962', 6, NULL, NULL, 'Test Customer', '<EMAIL>', '123-456-7890', '2025-05-29 14:00:00', '2025-05-29 14:50:00', 50, 1, 25.01, 0.00, 0.00, 25.01, 0.00, 0.00, 'confirmed', 'pending', 'Test booking from debug page', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 04:19:05', '2025-05-29 04:19:05'),
(16, 'BK2505296063', 6, NULL, NULL, 'Test Customer', '<EMAIL>', '123-456-7890', '2025-05-29 09:00:00', '2025-05-29 09:50:00', 50, 1, 25.01, 0.00, 0.00, 25.01, 0.00, 0.00, 'confirmed', 'pending', 'Test booking from debug page', NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 04:19:32', '2025-05-29 04:19:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `booking_payments`
--

CREATE TABLE `booking_payments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `payment_reference` varchar(255) NOT NULL,
  `payment_type` enum('deposit','partial','full','refund') NOT NULL,
  `payment_method` enum('cash','card','bank_transfer','online','other') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `processed_at` datetime DEFAULT NULL,
  `gateway` varchar(255) DEFAULT NULL,
  `gateway_transaction_id` varchar(255) DEFAULT NULL,
  `gateway_response` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`gateway_response`)),
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `booking_reminders`
--

CREATE TABLE `booking_reminders` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `reminder_type` enum('email','sms','push','call') NOT NULL,
  `hours_before` int(11) NOT NULL,
  `scheduled_at` datetime NOT NULL,
  `sent_at` datetime DEFAULT NULL,
  `status` enum('pending','sent','failed','cancelled') NOT NULL DEFAULT 'pending',
  `message` text DEFAULT NULL,
  `delivery_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`delivery_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `booking_services`
--

CREATE TABLE `booking_services` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `unit_price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `duration_minutes` int(11) NOT NULL,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `service_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`service_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `booking_services`
--

INSERT INTO `booking_services` (`id`, `booking_id`, `service_id`, `quantity`, `unit_price`, `total_price`, `duration_minutes`, `start_datetime`, `end_datetime`, `service_data`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, 100.00, 100.00, 60, '2025-05-24 15:10:52', '2025-05-24 16:10:52', NULL, '2025-05-23 19:10:52', '2025-05-23 19:10:52'),
(2, 2, 4, 1, 20.00, 20.00, 60, '2025-05-29 17:00:00', '2025-05-29 18:00:00', '{\"id\":4,\"business_id\":2,\"service_category_id\":null,\"name\":\"JKZ NETWORK\",\"slug\":\"jkz-network\",\"description\":\"recetas\",\"short_description\":\"recetas\",\"duration_minutes\":60,\"base_price\":\"20.00\",\"deposit_amount\":\"10.00\",\"deposit_required\":true,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":null,\"min_advance_booking_hours\":null,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":0,\"is_active\":true,\"created_at\":\"2025-05-23T16:27:09.000000Z\",\"updated_at\":\"2025-05-23T16:27:09.000000Z\"}', '2025-05-24 01:49:55', '2025-05-24 01:49:55'),
(3, 3, 9, 1, 16.96, 16.96, 100, '2025-05-28 12:03:00', '2025-05-28 13:43:00', NULL, '2025-05-25 20:04:24', '2025-05-25 20:04:24'),
(4, 4, 11, 1, 23.75, 23.75, 120, '2025-05-27 12:11:00', '2025-05-27 14:11:00', NULL, '2025-05-25 20:12:05', '2025-05-25 20:12:05'),
(5, 5, 15, 1, 10.18, 10.18, 60, '2025-05-29 12:12:00', '2025-05-29 13:12:00', NULL, '2025-05-25 20:12:48', '2025-05-25 20:12:48'),
(6, 6, 15, 1, 10.18, 10.18, 60, '2025-05-30 12:21:00', '2025-05-30 13:21:00', NULL, '2025-05-25 20:21:21', '2025-05-25 20:21:21'),
(7, 9, 8, 1, 5.09, 5.09, 40, '2025-05-27 22:28:00', '2025-05-27 23:08:00', NULL, '2025-05-26 05:40:08', '2025-05-26 05:40:08'),
(8, 10, 11, 1, 23.75, 23.75, 120, '2025-05-28 16:58:00', '2025-05-28 18:58:00', NULL, '2025-05-27 00:59:33', '2025-05-27 00:59:33'),
(9, 11, 15, 1, 10.18, 10.18, 60, '2025-05-31 11:00:00', '2025-05-31 12:00:00', '{\"id\":15,\"business_id\":5,\"service_category_id\":14,\"name\":\"Pedicura Basica Sin Esmaltado\",\"slug\":\"pedicura-basica-sin-esmaltado\",\"description\":\"Esta pedicura consiste en sumergir los pies en agua tibia\\/caliente, lijado, plantar, limpieza en \\u00e1rea de cut\\u00edculas y exfoliaci\\u00f3n\",\"short_description\":\"Esta pedicura consiste en sumergir los pies en agua tibia\\/caliente, lijado plantar, limpieza en \\u00e1rea de cut\\u00edculas y exfoliaci\\u00f3n\",\"duration_minutes\":60,\"base_price\":\"10.18\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":9,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-25T14:01:50.000000Z\",\"updated_at\":\"2025-05-25T14:01:50.000000Z\"}', '2025-05-28 18:09:24', '2025-05-28 18:09:24'),
(10, 12, 10, 1, 20.36, 20.36, 100, '2025-05-29 10:00:00', '2025-05-29 11:40:00', '{\"id\":10,\"business_id\":5,\"service_category_id\":13,\"name\":\"Dip Powder\",\"slug\":\"dip-powder\",\"description\":\"El dip powder es un polvo de inmersi\\u00f3n, Este sistema no necesita curado en l\\u00e1mpara\\r\\nLo trabajamos en la u\\u00f1a natural, incluye una manicura en seco\\r\\nOfrece una duraci\\u00f3n de 15 a 21 d\\u00edas\",\"short_description\":\"El dip powder es un polvo de inmersi\\u00f3n, Este sistema no necesita curado en l\\u00e1mpara\",\"duration_minutes\":100,\"base_price\":\"20.36\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":4,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-25T13:53:12.000000Z\",\"updated_at\":\"2025-05-27T14:48:23.000000Z\"}', '2025-05-28 18:10:41', '2025-05-28 18:10:41'),
(11, 13, 14, 1, 11.87, 11.87, 75, '2025-05-29 15:00:00', '2025-05-29 16:15:00', '{\"id\":14,\"business_id\":5,\"service_category_id\":14,\"name\":\"Pedicura basica\",\"slug\":\"pedicura-basica\",\"description\":\"Est\\u00e1 pedicura consiste en sumergir los pies en agua tibia\\/caliente, lijado plantar, limpieza en \\u00e1rea de cut\\u00edculas, exfoliaci\\u00f3n y esmaltado regular\",\"short_description\":\"Est\\u00e1 pedicura consiste en sumergir los pies en agua tibia\\/caliente, lijado plantar, limpieza en \\u00e1rea de cut\\u00edculas, exfoliaci\\u00f3n y esmaltado regular\",\"duration_minutes\":75,\"base_price\":\"11.87\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":8,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-25T14:00:04.000000Z\",\"updated_at\":\"2025-05-27T14:48:23.000000Z\"}', '2025-05-28 18:13:49', '2025-05-28 18:13:49'),
(12, 14, 8, 1, 5.09, 5.09, 40, '2025-05-30 14:00:00', '2025-05-30 14:40:00', '{\"id\":8,\"business_id\":5,\"service_category_id\":13,\"name\":\"Esmaltado Normal\",\"slug\":\"esmaltado-normal\",\"description\":\"Esmaltado Regular, incluye una ligera limpieza en \\u00e1rea de cut\\u00edcula\",\"short_description\":\"Esmaltado Regular, incluye una ligera limpieza en \\u00e1rea de cut\\u00edcula\",\"duration_minutes\":40,\"base_price\":\"5.09\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":2,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-25T13:38:49.000000Z\",\"updated_at\":\"2025-05-27T14:48:23.000000Z\"}', '2025-05-28 18:23:54', '2025-05-28 18:23:54'),
(13, 15, 30, 1, 25.01, 25.01, 50, '2025-05-29 14:00:00', '2025-05-29 14:50:00', '{\"id\":30,\"business_id\":6,\"service_category_id\":null,\"name\":\"Alex taxi\",\"slug\":\"alex-taxi\",\"description\":null,\"short_description\":null,\"duration_minutes\":50,\"base_price\":\"25.01\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":1,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-29T00:18:56.000000Z\",\"updated_at\":\"2025-05-29T00:18:56.000000Z\"}', '2025-05-29 04:19:05', '2025-05-29 04:19:05'),
(14, 16, 30, 1, 25.01, 25.01, 50, '2025-05-29 09:00:00', '2025-05-29 09:50:00', '{\"id\":30,\"business_id\":6,\"service_category_id\":null,\"name\":\"Alex taxi\",\"slug\":\"alex-taxi\",\"description\":null,\"short_description\":null,\"duration_minutes\":50,\"base_price\":\"25.01\",\"deposit_amount\":null,\"deposit_required\":false,\"buffer_time_before\":0,\"buffer_time_after\":0,\"max_advance_booking_days\":30,\"min_advance_booking_hours\":2,\"max_participants\":1,\"pricing_variables\":null,\"booking_rules\":null,\"online_booking_enabled\":true,\"requires_approval\":false,\"sort_order\":1,\"is_active\":true,\"is_public\":true,\"featured_on_landing\":false,\"landing_display_order\":null,\"landing_display_config\":null,\"show_price_on_landing\":true,\"show_duration_on_landing\":true,\"show_description_on_landing\":true,\"show_image_on_landing\":true,\"landing_page_title\":null,\"landing_page_description\":null,\"landing_page_keywords\":null,\"quick_booking_enabled\":true,\"booking_button_text\":null,\"booking_button_color\":null,\"landing_page_views\":0,\"landing_page_clicks\":0,\"last_landing_view\":null,\"created_at\":\"2025-05-29T00:18:56.000000Z\",\"updated_at\":\"2025-05-29T00:18:56.000000Z\"}', '2025-05-29 04:19:32', '2025-05-29 04:19:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `booking_service_resources`
--

CREATE TABLE `booking_service_resources` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `booking_service_id` bigint(20) UNSIGNED NOT NULL,
  `resource_id` bigint(20) UNSIGNED NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT 1,
  `start_datetime` datetime NOT NULL,
  `end_datetime` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `businesses`
--

CREATE TABLE `businesses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `landing_page_slug` varchar(255) DEFAULT NULL,
  `landing_page_status` enum('draft','published','maintenance') NOT NULL DEFAULT 'draft',
  `custom_domain` varchar(255) DEFAULT NULL,
  `domain_type` enum('subdirectory','subdomain','custom') NOT NULL DEFAULT 'subdirectory',
  `ssl_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `landing_page_theme` varchar(255) NOT NULL DEFAULT 'default',
  `landing_page_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`landing_page_config`)),
  `seo_optimized` tinyint(1) NOT NULL DEFAULT 0,
  `landing_page_last_updated` timestamp NULL DEFAULT NULL,
  `description` text DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `branding` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`branding`)),
  `timezone` varchar(255) NOT NULL DEFAULT 'UTC',
  `currency` varchar(3) NOT NULL DEFAULT 'USD',
  `language` varchar(2) NOT NULL DEFAULT 'en',
  `multi_branch` tinyint(1) NOT NULL DEFAULT 0,
  `online_booking_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `landing_page_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `booking_advance_days` int(11) NOT NULL DEFAULT 30,
  `booking_advance_hours` int(11) NOT NULL DEFAULT 2,
  `cancellation_hours` int(11) NOT NULL DEFAULT 24,
  `business_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`business_rules`)),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `owner_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `businesses`
--

INSERT INTO `businesses` (`id`, `name`, `slug`, `landing_page_slug`, `landing_page_status`, `custom_domain`, `domain_type`, `ssl_enabled`, `landing_page_theme`, `landing_page_config`, `seo_optimized`, `landing_page_last_updated`, `description`, `address`, `email`, `phone`, `website`, `logo`, `branding`, `timezone`, `currency`, `language`, `multi_branch`, `online_booking_enabled`, `landing_page_enabled`, `booking_advance_days`, `booking_advance_hours`, `cancellation_hours`, `business_rules`, `is_active`, `owner_id`, `created_at`, `updated_at`) VALUES
(1, 'Test Business', 'test-business', NULL, 'draft', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, 'A test business for booking system', NULL, '<EMAIL>', '+1234567890', 'https://bookkei.com', NULL, '{\"primary_color\":\"#007bff\",\"secondary_color\":\"#6c757d\",\"theme\":null}', 'UTC', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 3, '2025-05-23 18:38:20', '2025-05-24 00:01:17'),
(2, 'DR jonathan', 'dr-jonathan', NULL, 'draft', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, NULL, NULL, '<EMAIL>', '8293697584', NULL, 'business-logos/kVSHOqyh7kI9uflAiOp6LRihejObEt8tURAwe74M.png', NULL, 'UTC', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 1, '2025-05-23 20:24:01', '2025-05-23 20:24:01'),
(5, 'NNniconails', 'nnniconails', NULL, 'draft', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, 'Easy Nail Appointment Booking: Schedule Manicure and Pedicure in One Place\r\n\r\nTransform your beauty routine with our nail appointment booking app. Book manicures, pedicures, acrylic nails, and more with certified technicians, in-salon or at home. Quick, easy, and tailored to your needs!', NULL, '<EMAIL>', '8292856674', NULL, NULL, '{\"primary_color\":\"#28a745\",\"secondary_color\":\"#20c997\",\"theme\":\"auto\",\"font_family\":\"Inter\",\"logo_url\":\"business-logos\\/cUlLyaqPhSDPsLQkAf6YE0HQSoL4ZM0UdexG4rjP.png\",\"updated_at\":\"2025-05-28T11:53:48.430856Z\"}', 'America/New_York', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 4, '2025-05-25 06:02:31', '2025-05-28 15:53:48'),
(6, 'Alex taxi', 'alex-taxi', NULL, 'published', NULL, 'subdirectory', 1, 'creative', NULL, 0, '2025-05-28 02:49:44', 'Safe & Reliable Taxi Service in Puerto Plata\r\nExperience the best taxi service in Puerto Plata with comfortable rides, professional drivers, and competitive rates.', NULL, '<EMAIL>', '8295864712', NULL, NULL, NULL, 'America/New_York', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 5, '2025-05-25 06:05:45', '2025-05-28 05:04:23'),
(7, 'Elegant Beauty Salon', 'elegant-beauty-salon', 'elegant-beauty-salon', 'published', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, 'Premium beauty salon offering professional hair styling, skincare treatments, and wellness services in a luxurious environment.', NULL, '<EMAIL>', '(853) 373-5944', 'https://elegant-beauty-salon.com', NULL, NULL, 'America/New_York', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 36, '2025-05-27 21:48:34', '2025-05-27 21:48:34'),
(8, 'Chen Family Dental', 'chen-family-dental', 'chen-family-dental', 'published', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, 'Modern dental practice providing comprehensive oral health care with state-of-the-art technology and gentle, personalized treatment.', NULL, '<EMAIL>', '(834) 203-9085', 'https://chen-family-dental.com', NULL, NULL, 'America/New_York', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 38, '2025-05-27 21:49:14', '2025-05-27 21:49:14'),
(9, 'FitnessPlus Studio', 'fitnessplus-studio', 'fitnessplus-studio', 'published', NULL, 'subdirectory', 1, 'default', NULL, 0, NULL, 'Dynamic fitness studio offering personal training, group classes, and wellness programs to help you achieve your health goals.', NULL, '<EMAIL>', '(551) 767-1978', 'https://fitnessplus-studio.com', NULL, NULL, 'America/New_York', 'USD', 'en', 0, 1, 1, 30, 2, 24, NULL, 1, 39, '2025-05-27 21:50:51', '2025-05-27 21:50:51');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_branches`
--

CREATE TABLE `business_branches` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `address` varchar(255) NOT NULL,
  `city` varchar(255) NOT NULL,
  `state` varchar(255) DEFAULT NULL,
  `postal_code` varchar(255) DEFAULT NULL,
  `country` varchar(255) NOT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `google_maps_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`google_maps_data`)),
  `manager_name` varchar(255) DEFAULT NULL,
  `manager_email` varchar(255) DEFAULT NULL,
  `manager_phone` varchar(255) DEFAULT NULL,
  `is_main_branch` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_branches`
--

INSERT INTO `business_branches` (`id`, `business_id`, `name`, `slug`, `description`, `email`, `phone`, `address`, `city`, `state`, `postal_code`, `country`, `latitude`, `longitude`, `google_maps_data`, `manager_name`, `manager_email`, `manager_phone`, `is_main_branch`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'Test Location', 'test-location', NULL, NULL, NULL, '123 Test Street', 'Test City', NULL, NULL, 'United States', NULL, NULL, NULL, NULL, NULL, NULL, 1, 1, '2025-05-26 18:28:32', '2025-05-26 18:28:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_categories`
--

CREATE TABLE `business_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color` varchar(7) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_categories`
--

INSERT INTO `business_categories` (`id`, `name`, `slug`, `description`, `icon`, `color`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Health & Wellness', 'health-wellness', 'Medical practices, clinics, wellness centers, and health services', 'fas fa-heartbeat', '#28a745', 1, 1, '2025-05-23 16:19:35', '2025-05-24 05:15:05'),
(2, 'Beauty & Spa', 'beauty-spa', 'Beauty salons, spas, massage therapy, and cosmetic services', 'fas fa-spa', '#e83e8c', 2, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(3, 'Fitness & Sports', 'fitness-sports', 'Gyms, personal training, sports facilities, and fitness classes', 'fas fa-dumbbell', '#fd7e14', 3, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(4, 'Education & Training', 'education-training', 'Schools, tutoring, workshops, and educational services', 'fas fa-graduation-cap', '#007bff', 4, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(5, 'Professional Services', 'professional-services', 'Consulting, legal, accounting, and business services', 'fas fa-briefcase', '#6c757d', 5, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(6, 'Automotive', 'automotive', 'Car repair, maintenance, detailing, and automotive services', 'fas fa-car', '#dc3545', 6, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(7, 'Home Services', 'home-services', 'Cleaning, maintenance, repair, and home improvement services', 'fas fa-home', '#20c997', 7, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(8, 'Entertainment', 'entertainment', 'Event venues, entertainment services, and recreational activities', 'fas fa-music', '#6f42c1', 8, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(9, 'Food & Dining', 'food-dining', 'Restaurants, catering, food services, and dining experiences', 'fas fa-utensils', '#ffc107', 9, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(10, 'Technology', 'technology', 'IT services, computer repair, software development, and tech support', 'fas fa-laptop', '#17a2b8', 10, 1, '2025-05-23 16:19:35', '2025-05-23 16:19:35');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_category_assignments`
--

CREATE TABLE `business_category_assignments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_category_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_category_assignments`
--

INSERT INTO `business_category_assignments` (`id`, `business_id`, `business_category_id`, `created_at`, `updated_at`) VALUES
(1, 2, 1, NULL, NULL),
(2, 1, 10, NULL, NULL),
(5, 5, 2, NULL, NULL),
(6, 6, 6, NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_gallery_categories`
--

CREATE TABLE `business_gallery_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `color` varchar(255) DEFAULT NULL COMMENT 'Hex color for category display',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_gallery_categories`
--

INSERT INTO `business_gallery_categories` (`id`, `business_id`, `name`, `slug`, `description`, `color`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(2, 6, 'Alex taxi', 'alex-taxi', 'Alex taxi', '#007bff', 1, 0, '2025-05-28 06:09:23', '2025-05-28 06:09:23'),
(3, 1, 'Test Category', 'test-category', 'Test category for gallery images', '#007bff', 1, 0, '2025-05-28 14:29:27', '2025-05-28 14:29:27');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_gallery_images`
--

CREATE TABLE `business_gallery_images` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `category_id` bigint(20) UNSIGNED DEFAULT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `mime_type` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL COMMENT 'File size in bytes',
  `width` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `tags` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tags`)),
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Featured in galleries',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional image metadata' CHECK (json_valid(`metadata`)),
  `exif_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'EXIF data from image' CHECK (json_valid(`exif_data`)),
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_gallery_images`
--

INSERT INTO `business_gallery_images` (`id`, `business_id`, `category_id`, `filename`, `original_name`, `path`, `url`, `mime_type`, `file_size`, `width`, `height`, `title`, `alt_text`, `description`, `tags`, `is_featured`, `is_active`, `sort_order`, `metadata`, `exif_data`, `uploaded_at`, `created_at`, `updated_at`) VALUES
(1, 6, NULL, 'df88ce62-66b7-4ab1-8c48-545087064129.png', 'f7d390ea-d814-40a1-98cd-3258bc157694.png', 'gallery/business-6/df88ce62-66b7-4ab1-8c48-545087064129.png', NULL, 'image/png', 1409871, 1024, 1024, NULL, 'f7d390ea-d814-40a1-98cd-3258bc157694.png', NULL, '[]', 0, 1, 0, NULL, NULL, '2025-05-28 06:07:27', '2025-05-28 06:07:27', '2025-05-28 06:07:27'),
(2, 6, NULL, '087627e3-890b-4377-b7fa-656fdac17ef5.png', '89b9ec1b-05a7-4f7d-8b60-93cf1d59b4a9.png', 'gallery/business-6/087627e3-890b-4377-b7fa-656fdac17ef5.png', NULL, 'image/png', 1495978, 1024, 1024, NULL, '89b9ec1b-05a7-4f7d-8b60-93cf1d59b4a9.png', NULL, '[]', 0, 1, 0, NULL, NULL, '2025-05-28 06:07:27', '2025-05-28 06:07:27', '2025-05-28 06:07:27'),
(3, 6, NULL, 'cc63c895-9c27-49a1-b4a8-5e7efc2c2a10.png', '361656808201506817.png', 'gallery/business-6/cc63c895-9c27-49a1-b4a8-5e7efc2c2a10.png', NULL, 'image/png', 1811679, 1280, 720, NULL, '361656808201506817.png', NULL, '[]', 0, 1, 0, NULL, NULL, '2025-05-28 06:07:58', '2025-05-28 06:07:58', '2025-05-28 06:07:58'),
(4, 6, NULL, '3c4bdb50-a1b6-4a13-9b19-035f8800b04a.png', 'Yellow & Purple Simple Facebook Profile Picture (Banner para YouTube).png', 'gallery/business-6/3c4bdb50-a1b6-4a13-9b19-035f8800b04a.png', NULL, 'image/png', 1168973, 2560, 1440, NULL, 'Yellow & Purple Simple Facebook Profile Picture (Banner para YouTube).png', NULL, '[]', 0, 1, 0, NULL, NULL, '2025-05-28 06:07:58', '2025-05-28 06:07:58', '2025-05-28 06:07:58'),
(5, 6, 2, '98a1862e-be2b-4de6-8a41-7a581342be41.png', '89b9ec1b-05a7-4f7d-8b60-93cf1d59b4a9.png', 'gallery/business-6/98a1862e-be2b-4de6-8a41-7a581342be41.png', NULL, 'image/png', 1495978, 1024, 1024, 'test', '89b9ec1b-05a7-4f7d-8b60-93cf1d59b4a9.png', NULL, NULL, 1, 1, 0, NULL, NULL, '2025-05-28 14:08:26', '2025-05-28 14:08:26', '2025-05-28 14:32:19'),
(6, 1, 3, 'test-image-1.jpg', 'Test Image 1.jpg', 'gallery/business-1/test-image-1.jpg', NULL, 'image/jpeg', 1024000, 800, 600, 'Test Image 1', 'Test Image 1', 'This is a test image for gallery functionality', '[\"test\",\"gallery\"]', 0, 1, 0, '[]', '[]', '2025-05-28 14:29:27', '2025-05-28 14:29:27', '2025-05-28 14:38:53'),
(7, 1, 3, 'test-image-2.jpg', 'Test Image 2.jpg', 'gallery/business-1/test-image-2.jpg', NULL, 'image/jpeg', 1024000, 800, 600, 'Test Image 2', 'Test Image 2', 'Another test image for gallery functionality', '[\"test\",\"gallery\"]', 0, 1, 1, '[]', '[]', '2025-05-28 14:29:27', '2025-05-28 14:29:27', '2025-05-28 14:29:27'),
(8, 1, 3, 'test-image-3.jpg', 'Test Image 3.jpg', 'gallery/business-1/test-image-3.jpg', NULL, 'image/jpeg', 1024000, 800, 600, 'Test Image 3', 'Test Image 3', 'Third test image for gallery functionality', '[\"test\",\"gallery\"]', 0, 1, 2, '[]', '[]', '2025-05-28 14:29:27', '2025-05-28 14:29:27', '2025-05-28 14:29:27');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_holidays`
--

CREATE TABLE `business_holidays` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_branch_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `recurrence_type` varchar(255) DEFAULT NULL,
  `recurrence_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`recurrence_data`)),
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_holidays`
--

INSERT INTO `business_holidays` (`id`, `business_id`, `business_branch_id`, `name`, `start_date`, `end_date`, `is_recurring`, `recurrence_type`, `recurrence_data`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(14, 6, NULL, 'Christmas Week Closure', '2025-12-23', '2025-12-31', 0, NULL, NULL, 'Business closed for Christmas and New Year holidays', 1, '2025-05-25 15:00:00', '2025-05-25 15:00:00'),
(15, 6, NULL, 'Summer Break', '2025-07-01', '2025-07-15', 0, NULL, NULL, 'Annual summer break closure', 1, '2025-05-25 15:00:03', '2025-05-25 15:00:03'),
(18, 5, NULL, 'New Year\'s Day', '2025-01-01', '2025-01-01', 1, 'yearly', NULL, 'New Year\'s Day celebration', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(19, 5, NULL, 'Martin Luther King Jr. Day', '2025-01-20', '2025-01-20', 1, 'yearly', NULL, 'Martin Luther King Jr. Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(20, 5, NULL, 'Presidents\' Day', '2025-02-17', '2025-02-17', 1, 'yearly', NULL, 'Presidents\' Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(21, 5, NULL, 'Memorial Day', '2025-05-26', '2025-05-26', 1, 'yearly', NULL, 'Memorial Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(22, 5, NULL, 'Independence Day', '2025-07-04', '2025-07-04', 1, 'yearly', NULL, 'Independence Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(23, 5, NULL, 'Labor Day', '2025-09-08', '2025-09-08', 1, 'yearly', NULL, 'Labor Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(24, 5, NULL, 'Columbus Day', '2025-10-13', '2025-10-13', 1, 'yearly', NULL, 'Columbus Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(25, 5, NULL, 'Veterans Day', '2025-11-11', '2025-11-11', 1, 'yearly', NULL, 'Veterans Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(26, 5, NULL, 'Thanksgiving Day', '2025-11-27', '2025-11-27', 1, 'yearly', NULL, 'Thanksgiving Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55'),
(27, 5, NULL, 'Christmas Day', '2025-12-25', '2025-12-25', 1, 'yearly', NULL, 'Christmas Day', 1, '2025-05-25 18:59:55', '2025-05-25 18:59:55');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_landing_pages`
--

CREATE TABLE `business_landing_pages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `custom_slug` varchar(255) NOT NULL,
  `custom_domain` varchar(255) DEFAULT NULL,
  `domain_type` enum('subdirectory','subdomain','custom') NOT NULL DEFAULT 'subdirectory',
  `ssl_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `page_title` varchar(255) NOT NULL,
  `page_description` text DEFAULT NULL,
  `theme` varchar(255) NOT NULL DEFAULT 'default',
  `theme_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`theme_config`)),
  `theme_customization` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`theme_customization`)),
  `custom_css` longtext DEFAULT NULL,
  `last_customized_at` timestamp NULL DEFAULT NULL,
  `logo_url` varchar(255) DEFAULT NULL,
  `branding_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`branding_config`)),
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `open_graph_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`open_graph_config`)),
  `schema_markup` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`schema_markup`)),
  `hero_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`hero_section`)),
  `about_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`about_section`)),
  `services_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`services_section`)),
  `contact_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`contact_section`)),
  `testimonials_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`testimonials_section`)),
  `gallery_section` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`gallery_section`)),
  `custom_sections` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`custom_sections`)),
  `booking_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `booking_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`booking_config`)),
  `booking_button_text` varchar(255) NOT NULL DEFAULT 'Book Now',
  `booking_button_color` varchar(255) NOT NULL DEFAULT '#007bff',
  `google_analytics_id` varchar(255) DEFAULT NULL,
  `facebook_pixel_id` varchar(255) DEFAULT NULL,
  `tracking_codes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tracking_codes`)),
  `cache_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `cache_duration` int(11) NOT NULL DEFAULT 3600,
  `last_generated_at` timestamp NULL DEFAULT NULL,
  `is_published` tinyint(1) NOT NULL DEFAULT 0,
  `is_indexed` tinyint(1) NOT NULL DEFAULT 1,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_landing_pages`
--

INSERT INTO `business_landing_pages` (`id`, `business_id`, `custom_slug`, `custom_domain`, `domain_type`, `ssl_enabled`, `page_title`, `page_description`, `theme`, `theme_config`, `theme_customization`, `custom_css`, `last_customized_at`, `logo_url`, `branding_config`, `meta_title`, `meta_description`, `meta_keywords`, `open_graph_config`, `schema_markup`, `hero_section`, `about_section`, `services_section`, `contact_section`, `testimonials_section`, `gallery_section`, `custom_sections`, `booking_enabled`, `booking_config`, `booking_button_text`, `booking_button_color`, `google_analytics_id`, `facebook_pixel_id`, `tracking_codes`, `cache_enabled`, `cache_duration`, `last_generated_at`, `is_published`, `is_indexed`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 6, 'alex-taxi', NULL, 'subdirectory', 1, 'Alex taxi', 'Safe & Reliable Taxi Service in Puerto Plata\r\nExperience the best taxi service in Puerto Plata with comfortable rides, professional drivers, and competitive rates.', 'creative', NULL, NULL, NULL, NULL, NULL, NULL, 'Alex taxi', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 'Book Now', '#007bff', NULL, NULL, NULL, 1, 3600, NULL, 1, 1, '2025-05-28 05:04:23', '2025-05-27 05:51:44', '2025-05-28 05:04:23'),
(2, 5, 'nnniconails', NULL, 'subdomain', 1, 'NNniconails', 'Easy Nail Appointment Booking: Schedule Manicure and Pedicure in One Place\r\n\r\nTransform your beauty routine with our nail appointment booking app. Book manicures, pedicures, acrylic nails, and more with certified technicians, in-salon or at home. Quick, easy, and tailored to your needs!', 'creative', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 1, NULL, 'Book Now', '#007bff', NULL, NULL, NULL, 1, 3600, NULL, 1, 1, '2025-05-27 18:44:32', '2025-05-27 13:59:32', '2025-05-28 04:23:45');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_landing_page_sections`
--

CREATE TABLE `business_landing_page_sections` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_landing_page_id` bigint(20) UNSIGNED NOT NULL,
  `section_type` varchar(255) NOT NULL,
  `section_name` varchar(255) NOT NULL,
  `section_description` text DEFAULT NULL,
  `content_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL CHECK (json_valid(`content_data`)),
  `layout_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`layout_config`)),
  `style_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`style_config`)),
  `is_visible` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `mobile_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`mobile_config`)),
  `tablet_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tablet_config`)),
  `animation_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`animation_config`)),
  `parallax_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_landing_page_sections`
--

INSERT INTO `business_landing_page_sections` (`id`, `business_landing_page_id`, `section_type`, `section_name`, `section_description`, `content_data`, `layout_config`, `style_config`, `is_visible`, `sort_order`, `mobile_config`, `tablet_config`, `animation_config`, `parallax_enabled`, `created_at`, `updated_at`) VALUES
(9, 1, 'hero', 'Hero Section', 'Main banner with call-to-action', '{\"title\":\"Alex taxi\",\"subtitle\":\"Safe & Reliable Taxi Service in Puerto Plata\\r\\nExperience the best taxi service in Puerto Plata with comfortable rides, professional drivers, and competitive rates.\",\"background_image\":null,\"background_video\":null,\"cta_text\":\"Book Now\",\"cta_url\":\"#booking\",\"secondary_cta_text\":\"Learn More\",\"secondary_cta_url\":\"#about\",\"overlay_opacity\":\"0.5\",\"text_alignment\":\"center\"}', NULL, NULL, 0, 1, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 02:55:33'),
(10, 1, 'about', 'About Us', 'Information about your business', '{\"title\":\"About Alex taxi\",\"content\":\"Safe & Reliable Taxi Service in Puerto Plata\\r\\nExperience the best taxi service in Puerto Plata with comfortable rides, professional drivers, and competitive rates.\",\"image\":null,\"layout\":\"image-left\",\"features\":[\"Professional service\",\"Experienced team\",\"Customer satisfaction\"],\"stats\":[{\"value\":null,\"label\":null}]}', NULL, NULL, 0, 2, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 02:56:02'),
(11, 1, 'services', 'Our Services', 'Showcase your services and offerings', '{\"title\":\"Our Services\",\"subtitle\":\"Discover what we offer\",\"layout\":\"grid\",\"columns\":\"3\",\"show_prices\":\"1\",\"show_duration\":\"1\",\"show_description\":\"1\",\"show_booking_button\":\"1\"}', NULL, NULL, 0, 3, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 02:56:09'),
(12, 1, 'features', 'Why Choose Us', 'Highlight your key features and benefits', '{\"title\":\"Why Choose Alex taxi\",\"subtitle\":\"What sets us apart from the competition\",\"layout\":\"grid\",\"columns\":\"3\",\"features\":[{\"icon\":\"fas fa-star\",\"title\":\"Quality Service\",\"description\":\"We provide top-notch service with attention to detail.\"},{\"icon\":\"fas fa-clock\",\"title\":\"Convenient Hours\",\"description\":\"Flexible scheduling to fit your busy lifestyle.\"},{\"icon\":\"fas fa-users\",\"title\":\"Expert Team\",\"description\":\"Our experienced professionals are here to help you.\"}]}', NULL, NULL, 1, 4, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 03:08:09'),
(13, 1, 'testimonials', 'Customer Reviews', 'Display customer testimonials and reviews', '{\"title\":\"What Our Customers Say\",\"subtitle\":\"Read reviews from our satisfied clients\",\"layout\":\"carousel\",\"show_ratings\":\"1\",\"show_photos\":\"1\",\"auto_rotate\":\"1\",\"rotation_speed\":\"5\",\"max_testimonials\":\"6\",\"min_rating\":\"4\",\"featured_only\":\"1\",\"show_service_name\":\"1\"}', NULL, NULL, 1, 5, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 03:28:11'),
(14, 1, 'team', 'Our Team', 'Meet your professional team members', '{\"title\":\"Meet Our Team\",\"subtitle\":\"Get to know the professionals who will serve you\",\"layout\":\"list\",\"columns\":\"3\",\"show_bio\":\"0\",\"show_experience\":\"0\",\"show_specializations\":\"0\",\"show_contact\":\"0\",\"show_social_links\":\"0\",\"show_booking_button\":\"0\",\"team_filter\":\"all\",\"sort_order\":\"position\",\"max_members\":\"2\",\"image_style\":\"square\",\"enable_modal\":\"1\"}', NULL, NULL, 1, 6, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 03:59:11'),
(15, 1, 'gallery', 'Gallery', 'Showcase your work and facilities', '{\"title\":\"Our Work\",\"subtitle\":\"Take a look at our facilities and previous work\",\"layout\":\"masonry\",\"columns\":\"2\",\"show_captions\":\"1\",\"lightbox_enabled\":\"1\",\"lazy_loading\":\"1\",\"hover_effects\":\"1\",\"show_load_more\":\"1\",\"images_per_page\":\"9\",\"image_quality\":\"thumbnail\",\"filter_enabled\":\"1\"}', NULL, NULL, 1, 7, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 04:38:43'),
(16, 1, 'pricing', 'Pricing', 'Display your pricing packages', '{\"title\":\"Our Pricing\",\"subtitle\":\"Choose the package that works best for you\",\"layout\":\"cards\",\"columns\":\"4\",\"show_features\":\"1\",\"show_popular_badge\":\"1\",\"show_booking_button\":\"1\",\"packages\":[{\"name\":\"Servicios individuales\",\"price\":\"99\",\"period\":\"month\",\"description\":\"probando\",\"features\":[\"Feature 1\",\"Feature 2\",\"Feature 3\"],\"popular\":true,\"button_text\":\"Choose Plan\",\"button_url\":\"#booking\"}]}', NULL, NULL, 1, 8, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 04:37:13'),
(17, 1, 'faq', 'FAQ', 'Frequently asked questions', '{\"title\":\"Frequently Asked Questions\",\"subtitle\":\"Find answers to common questions\",\"layout\":\"grid\",\"search_enabled\":\"1\",\"allow_expand_all\":\"1\",\"show_contact_cta\":\"1\",\"contact_cta_text\":\"Still have questions? Contact us!\",\"questions\":[{\"question\":\"test\",\"answer\":\"dfdfdf\",\"category\":\"test\",\"sort_order\":1,\"featured\":true}]}', NULL, NULL, 1, 9, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 04:46:14'),
(18, 1, 'cta', 'Call to Action', 'Encourage visitors to take action', '{\"title\":\"Ready to Get Started?\",\"subtitle\":\"Book your appointment today and experience our exceptional service\",\"button_text\":\"Book Now\",\"button_url\":\"#booking\",\"secondary_button_text\":\"Call Us\",\"secondary_button_url\":\"tel:8295864712\",\"background_color\":\"#007bff\",\"text_color\":\"#ffffff\",\"background_image\":\"https:\\/\\/lirp.cdn-website.com\\/07aaf2d8\\/dms3rep\\/multi\\/opt\\/un%CC%83as-acrilicas3-640w.jpg\",\"text_alignment\":\"left\",\"section_padding\":\"large\",\"border_radius\":\"medium\",\"enable_animation\":false,\"full_width\":false}', NULL, NULL, 1, 10, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 05:04:00'),
(19, 1, 'contact', 'Contact Us', 'Contact information and form', '{\"title\":\"Get in Touch\",\"subtitle\":\"We\'d love to hear from you\",\"show_map\":\"1\",\"show_hours\":\"1\",\"show_contact_form\":\"1\",\"show_social_links\":\"1\",\"map_zoom\":\"15\",\"contact_form_fields\":[\"name\",\"email\",\"phone\",\"message\"]}', NULL, NULL, 0, 11, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 02:56:54'),
(20, 2, 'hero', 'Hero Section', 'Main banner with call-to-action', '{\"title\":\"NNniconails\",\"subtitle\":\"Easy Nail Appointment Booking: Schedule Manicure and Pedicure in One Place\\r\\n\\r\\nTransform your beauty routine with our nail appointment booking app. Book manicures, pedicures, acrylic nails, and more with certified technicians, in-salon or at home. Quick, easy, and tailored to your needs!\",\"background_image\":null,\"cta_text\":\"Book Now\",\"cta_url\":\"#booking\",\"secondary_cta_text\":\"Learn More\",\"secondary_cta_url\":\"#about\"}', NULL, NULL, 1, 1, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(21, 2, 'about', 'About Us', 'Information about your business', '{\"title\":\"About NNniconails\",\"content\":\"Easy Nail Appointment Booking: Schedule Manicure and Pedicure in One Place\\r\\n\\r\\nTransform your beauty routine with our nail appointment booking app. Book manicures, pedicures, acrylic nails, and more with certified technicians, in-salon or at home. Quick, easy, and tailored to your needs!\",\"image\":null,\"features\":[\"Professional service\",\"Experienced team\",\"Customer satisfaction\"],\"layout\":\"image-left\"}', NULL, NULL, 1, 2, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(22, 2, 'services', 'Our Services', 'Showcase your services and offerings', '{\"title\":\"Our Services\",\"subtitle\":\"Discover what we offer\",\"show_prices\":true,\"show_duration\":true,\"show_descriptions\":true,\"layout\":\"grid\",\"columns\":3}', NULL, NULL, 1, 3, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(23, 2, 'features', 'Why Choose Us', 'Highlight your key features and benefits', '{\"title\":\"Why Choose NNniconails\",\"subtitle\":\"What sets us apart from the competition\",\"features\":[{\"icon\":\"fas fa-star\",\"title\":\"Quality Service\",\"description\":\"We provide top-notch service with attention to detail.\"},{\"icon\":\"fas fa-clock\",\"title\":\"Convenient Hours\",\"description\":\"Flexible scheduling to fit your busy lifestyle.\"},{\"icon\":\"fas fa-users\",\"title\":\"Expert Team\",\"description\":\"Our experienced professionals are here to help you.\"}],\"layout\":\"grid\"}', NULL, NULL, 1, 4, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(24, 2, 'testimonials', 'Customer Reviews', 'Display customer testimonials and reviews', '{\"title\":\"What Our Customers Say\",\"subtitle\":\"Read reviews from our satisfied clients\",\"layout\":\"carousel\",\"show_ratings\":\"1\",\"show_photos\":\"1\",\"auto_rotate\":\"1\",\"rotation_speed\":\"5\",\"max_testimonials\":\"6\",\"min_rating\":\"4\",\"featured_only\":\"1\",\"show_service_name\":\"1\"}', NULL, NULL, 1, 5, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 04:00:02'),
(25, 2, 'team', 'Our Team', 'Meet your professional team members', '{\"title\":\"Meet Our Team\",\"subtitle\":\"Get to know the professionals who will serve you\",\"layout\":\"grid\",\"columns\":\"3\",\"show_bio\":\"1\",\"show_experience\":\"1\",\"show_specializations\":\"1\",\"show_contact\":\"1\",\"show_social_links\":\"1\",\"show_booking_button\":\"1\",\"team_filter\":\"accepts_bookings\",\"sort_order\":\"position\",\"max_members\":\"4\",\"image_style\":\"square\",\"enable_modal\":\"0\"}', NULL, NULL, 1, 6, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-28 04:21:44'),
(26, 2, 'gallery', 'Gallery', 'Showcase your work and facilities', '{\"title\":\"Our Work\",\"subtitle\":\"Take a look at our facilities and previous work\",\"layout\":\"masonry\",\"columns\":4,\"lightbox_enabled\":true,\"show_captions\":true}', NULL, NULL, 0, 7, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(27, 2, 'pricing', 'Pricing', 'Display your pricing packages', '{\"title\":\"Our Pricing\",\"subtitle\":\"Choose the package that works best for you\",\"show_features\":true,\"show_popular_badge\":true,\"layout\":\"cards\",\"columns\":3}', NULL, NULL, 0, 8, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(28, 2, 'faq', 'FAQ', 'Frequently asked questions', '{\"title\":\"Frequently Asked Questions\",\"subtitle\":\"Find answers to common questions\",\"layout\":\"accordion\",\"search_enabled\":true,\"categories\":[]}', NULL, NULL, 0, 9, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(29, 2, 'cta', 'Call to Action', 'Encourage visitors to take action', '{\"title\":\"Ready to Get Started?\",\"subtitle\":\"Book your appointment today and experience our exceptional service\",\"button_text\":\"Book Now\",\"button_url\":\"#booking\",\"secondary_button_text\":\"Call Us\",\"secondary_button_url\":\"tel:8292856674\",\"background_color\":\"#007bff\",\"text_color\":\"#ffffff\"}', NULL, NULL, 1, 10, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29'),
(30, 2, 'contact', 'Contact Us', 'Contact information and form', '{\"title\":\"Get in Touch\",\"subtitle\":\"We\'d love to hear from you\",\"show_map\":true,\"show_hours\":true,\"show_contact_form\":true,\"show_social_links\":true,\"map_zoom\":15}', NULL, NULL, 1, 11, NULL, NULL, NULL, 0, '2025-05-27 22:25:29', '2025-05-27 22:25:29');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_operating_hours`
--

CREATE TABLE `business_operating_hours` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_branch_id` bigint(20) UNSIGNED DEFAULT NULL,
  `day_of_week` int(11) NOT NULL,
  `open_time` time DEFAULT NULL,
  `close_time` time DEFAULT NULL,
  `is_closed` tinyint(1) NOT NULL DEFAULT 0,
  `break_times` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`break_times`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_operating_hours`
--

INSERT INTO `business_operating_hours` (`id`, `business_id`, `business_branch_id`, `day_of_week`, `open_time`, `close_time`, `is_closed`, `break_times`, `created_at`, `updated_at`) VALUES
(1, 2, NULL, 0, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(2, 2, NULL, 1, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(3, 2, NULL, 2, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(4, 2, NULL, 3, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(5, 2, NULL, 4, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(6, 2, NULL, 5, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(7, 2, NULL, 6, NULL, NULL, 1, NULL, '2025-05-25 01:22:17', '2025-05-25 01:22:17'),
(8, 6, NULL, 1, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 14:46:30'),
(9, 6, NULL, 2, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:10'),
(10, 6, NULL, 3, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:10'),
(11, 6, NULL, 4, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:10'),
(12, 6, NULL, 5, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:10'),
(13, 6, NULL, 6, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:10'),
(14, 6, NULL, 0, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:33:00', '2025-05-25 06:33:14'),
(15, 5, NULL, 1, NULL, NULL, 1, NULL, '2025-05-25 06:35:54', '2025-05-26 17:36:08'),
(16, 5, NULL, 2, '08:00:00', '20:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 06:35:54'),
(17, 5, NULL, 3, '08:00:00', '20:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 06:35:54'),
(18, 5, NULL, 4, '08:00:00', '20:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 06:35:54'),
(19, 5, NULL, 5, '08:00:00', '20:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 06:35:54'),
(20, 5, NULL, 6, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 18:59:41'),
(21, 5, NULL, 0, '09:00:00', '17:00:00', 0, NULL, '2025-05-25 06:35:54', '2025-05-25 18:59:41');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_seo_settings`
--

CREATE TABLE `business_seo_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `meta_keywords` text DEFAULT NULL,
  `canonical_url` varchar(255) DEFAULT NULL,
  `og_title` varchar(255) DEFAULT NULL,
  `og_description` text DEFAULT NULL,
  `og_image` varchar(255) DEFAULT NULL,
  `og_type` varchar(255) NOT NULL DEFAULT 'business.business',
  `og_locale` varchar(255) NOT NULL DEFAULT 'en_US',
  `twitter_card` varchar(255) NOT NULL DEFAULT 'summary_large_image',
  `twitter_site` varchar(255) DEFAULT NULL,
  `twitter_creator` varchar(255) DEFAULT NULL,
  `twitter_title` varchar(255) DEFAULT NULL,
  `twitter_description` text DEFAULT NULL,
  `twitter_image` varchar(255) DEFAULT NULL,
  `business_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`business_schema`)),
  `service_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`service_schema`)),
  `review_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`review_schema`)),
  `faq_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`faq_schema`)),
  `custom_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`custom_schema`)),
  `business_type` varchar(255) DEFAULT NULL,
  `opening_hours_schema` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`opening_hours_schema`)),
  `price_range` varchar(255) DEFAULT NULL,
  `geo_coordinates` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`geo_coordinates`)),
  `google_my_business_id` varchar(255) DEFAULT NULL,
  `robots_meta` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`robots_meta`)),
  `sitemap_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `sitemap_last_generated` timestamp NULL DEFAULT NULL,
  `hreflang_tags` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`hreflang_tags`)),
  `google_analytics_id` varchar(255) DEFAULT NULL,
  `google_tag_manager_id` varchar(255) DEFAULT NULL,
  `facebook_pixel_id` varchar(255) DEFAULT NULL,
  `custom_tracking_codes` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`custom_tracking_codes`)),
  `amp_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `critical_css` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`critical_css`)),
  `lazy_loading_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `business_seo_settings`
--

INSERT INTO `business_seo_settings` (`id`, `business_id`, `meta_title`, `meta_description`, `meta_keywords`, `canonical_url`, `og_title`, `og_description`, `og_image`, `og_type`, `og_locale`, `twitter_card`, `twitter_site`, `twitter_creator`, `twitter_title`, `twitter_description`, `twitter_image`, `business_schema`, `service_schema`, `review_schema`, `faq_schema`, `custom_schema`, `business_type`, `opening_hours_schema`, `price_range`, `geo_coordinates`, `google_my_business_id`, `robots_meta`, `sitemap_enabled`, `sitemap_last_generated`, `hreflang_tags`, `google_analytics_id`, `google_tag_manager_id`, `facebook_pixel_id`, `custom_tracking_codes`, `amp_enabled`, `critical_css`, `lazy_loading_enabled`, `created_at`, `updated_at`) VALUES
(1, 6, 'Alex taxi', 'Safe & Reliable Taxi Service in Puerto Plata\r\nExperience the best taxi service in Puerto Plata with comfortable rides, professional drivers, and competitive rates.', NULL, NULL, NULL, NULL, NULL, 'business.business', 'en_US', 'summary_large_image', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'AutomotiveBusiness', NULL, NULL, NULL, NULL, NULL, 1, '2025-05-27 21:22:53', NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, '2025-05-27 05:51:44', '2025-05-27 21:22:53'),
(2, 5, 'NNniconails', 'Easy Nail Appointment Booking: Schedule Manicure and Pedicure in One Place\r\n\r\nTransform your beauty routine with our nail appointment booking app. Book manicures, pedicures, acrylic nails, and more with certified technicians, in-salon or at home. Quick, easy, and tailored to your needs!', NULL, NULL, NULL, NULL, NULL, 'business.business', 'en_US', 'summary_large_image', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'LocalBusiness', NULL, NULL, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, 1, '2025-05-27 13:59:32', '2025-05-27 13:59:32');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_tags`
--

CREATE TABLE `business_tags` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `color` varchar(7) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `business_tag_assignments`
--

CREATE TABLE `business_tag_assignments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_tag_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('bookkei_cache_notification_analytics_5_4_30_days', 'a:6:{s:8:\"overview\";a:6:{s:19:\"total_notifications\";i:5;s:20:\"unread_notifications\";i:1;s:20:\"urgent_notifications\";i:0;s:21:\"deleted_notifications\";i:0;s:9:\"read_rate\";d:80;s:27:\"average_response_time_hours\";d:-269.26;}s:6:\"trends\";a:4:{s:11:\"daily_stats\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:1:{s:10:\"2025-05-28\";a:4:{s:5:\"total\";i:5;s:6:\"unread\";i:1;s:6:\"urgent\";i:1;s:5:\"types\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:3:{s:9:\"marketing\";i:1;s:12:\"waiting_list\";i:3;s:5:\"alert\";i:1;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}}}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:11:\"growth_rate\";d:0;s:8:\"peak_day\";s:10:\"2025-05-28\";s:12:\"busiest_hour\";s:2:\"15\";}s:12:\"distribution\";a:5:{s:7:\"by_type\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:3:{s:9:\"marketing\";i:1;s:12:\"waiting_list\";i:3;s:5:\"alert\";i:1;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:11:\"by_priority\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:3:{s:3:\"low\";i:3;s:6:\"normal\";i:1;s:6:\"urgent\";i:1;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:9:\"by_source\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:3:{s:9:\"marketing\";i:1;s:12:\"waiting_list\";i:3;s:5:\"alert\";i:1;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:7:\"by_hour\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:1:{i:15;i:5;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:14:\"by_day_of_week\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:1:{s:9:\"Wednesday\";i:5;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}}s:11:\"performance\";a:5:{s:15:\"engagement_rate\";d:80;s:26:\"response_times_by_priority\";O:29:\"Illuminate\\Support\\Collection\":2:{s:8:\"\0*\0items\";a:3:{s:3:\"low\";d:-19865.2;s:6:\"normal\";d:-24921;s:6:\"urgent\";d:29.1;}s:28:\"\0*\0escapeWhenCastingToString\";b:0;}s:21:\"fastest_response_time\";d:-662.65;s:21:\"slowest_response_time\";d:0.49;s:22:\"notification_frequency\";d:0.17;}s:8:\"insights\";a:2:{i:0;a:4:{s:4:\"type\";s:4:\"info\";s:5:\"title\";s:22:\"Peak Notification Time\";s:7:\"message\";s:77:\"Most notifications arrive at 15:00. Consider adjusting quiet hours if needed.\";s:6:\"action\";s:28:\"Adjust notification schedule\";}i:1;a:4:{s:4:\"type\";s:4:\"info\";s:5:\"title\";s:25:\"Notification Type Pattern\";s:7:\"message\";s:51:\"60% of your notifications are waiting_list related.\";s:6:\"action\";s:55:\"Consider customizing waiting_list notification settings\";}}s:15:\"recommendations\";a:1:{i:0;a:5:{s:8:\"priority\";s:3:\"low\";s:5:\"title\";s:33:\"Filter Low Priority Notifications\";s:11:\"description\";s:68:\"60% of your notifications are low priority. Consider filtering them.\";s:6:\"action\";s:27:\"Set minimum priority filter\";s:6:\"impact\";s:37:\"Focus on important notifications only\";}}}', 1748477616),
('bookkei_cache_setting_mail_from_address', 's:26:\"<EMAIL>\";', 2063837152),
('bookkei_cache_setting_mail_from_name', 's:7:\"BookKei\";', 2063837152);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `compliance_reports`
--

CREATE TABLE `compliance_reports` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `report_type` varchar(100) NOT NULL,
  `report_name` varchar(255) NOT NULL,
  `report_format` varchar(20) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `parameters` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`parameters`)),
  `summary` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`summary`)),
  `compliance_score` decimal(5,2) DEFAULT NULL,
  `risk_level` varchar(20) DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `file_size` bigint(20) DEFAULT NULL,
  `file_hash` varchar(255) DEFAULT NULL,
  `generated_by` bigint(20) UNSIGNED NOT NULL,
  `generated_at` timestamp NOT NULL DEFAULT '2025-05-26 04:28:08',
  `generation_time_ms` int(11) DEFAULT NULL,
  `download_count` int(11) NOT NULL DEFAULT 0,
  `last_accessed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_activity_timeline`
--

CREATE TABLE `customer_activity_timeline` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `activity_type` enum('booking_created','booking_modified','booking_cancelled','booking_completed','communication_sent','communication_received','points_awarded','points_redeemed','tag_assigned','tag_removed','profile_updated','note_added','status_changed','referral_made','feedback_received','campaign_sent','login','registration') NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `icon` varchar(255) DEFAULT NULL,
  `color` varchar(255) NOT NULL DEFAULT '#6c757d',
  `is_important` tinyint(1) NOT NULL DEFAULT 0,
  `is_system_generated` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_addresses`
--

CREATE TABLE `customer_addresses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `type` enum('home','work','billing','shipping','other') NOT NULL DEFAULT 'home',
  `label` varchar(255) DEFAULT NULL,
  `address_line_1` varchar(255) NOT NULL,
  `address_line_2` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `state_province` varchar(255) NOT NULL,
  `postal_code` varchar(255) NOT NULL,
  `country` varchar(255) NOT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `delivery_instructions` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_business_profiles`
--

CREATE TABLE `customer_business_profiles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('active','inactive','vip','blocked','prospect') NOT NULL DEFAULT 'active',
  `customer_since` date NOT NULL DEFAULT curdate(),
  `last_visit_date` date DEFAULT NULL,
  `total_visits` int(11) NOT NULL DEFAULT 0,
  `total_spent` decimal(12,2) NOT NULL DEFAULT 0.00,
  `average_order_value` decimal(10,2) NOT NULL DEFAULT 0.00,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`preferences`)),
  `communication_preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`communication_preferences`)),
  `notes` text DEFAULT NULL,
  `special_requirements` text DEFAULT NULL,
  `loyalty_points_balance` int(11) NOT NULL DEFAULT 0,
  `loyalty_tier` enum('bronze','silver','gold','platinum','diamond') NOT NULL DEFAULT 'bronze',
  `lifetime_value` decimal(12,2) NOT NULL DEFAULT 0.00,
  `referrals_made` int(11) NOT NULL DEFAULT 0,
  `no_show_count` int(11) NOT NULL DEFAULT 0,
  `cancellation_count` int(11) NOT NULL DEFAULT 0,
  `marketing_consent` tinyint(1) NOT NULL DEFAULT 0,
  `marketing_consent_date` timestamp NULL DEFAULT NULL,
  `custom_fields` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`custom_fields`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `customer_business_profiles`
--

INSERT INTO `customer_business_profiles` (`id`, `business_id`, `customer_id`, `status`, `customer_since`, `last_visit_date`, `total_visits`, `total_spent`, `average_order_value`, `preferences`, `communication_preferences`, `notes`, `special_requirements`, `loyalty_points_balance`, `loyalty_tier`, `lifetime_value`, `referrals_made`, `no_show_count`, `cancellation_count`, `marketing_consent`, `marketing_consent_date`, `custom_fields`, `created_at`, `updated_at`) VALUES
(1, 1, 18, 'vip', '2023-01-15', '2025-04-26', 25, 2500.00, 100.00, NULL, NULL, 'Prefers morning appointments. Allergic to certain fragrances.', NULL, 500, 'gold', 2500.00, 0, 0, 0, 1, '2025-05-27 01:37:30', NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(2, 1, 19, 'active', '2024-01-10', '2025-05-07', 3, 180.00, 60.00, NULL, NULL, 'New customer, very satisfied with services.', NULL, 90, 'bronze', 180.00, 0, 0, 0, 1, '2025-05-27 01:37:30', NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(3, 1, 20, 'active', '2023-06-20', '2025-05-08', 12, 960.00, 80.00, NULL, NULL, 'Regular customer, books monthly appointments.', NULL, 240, 'silver', 960.00, 0, 0, 0, 1, '2025-05-27 01:37:30', NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(4, 1, 21, 'active', '2022-08-05', '2025-05-07', 8, 640.00, 80.00, NULL, NULL, 'Corporate client, books for team events.', NULL, 160, 'bronze', 640.00, 0, 0, 0, 1, '2025-05-27 01:37:30', NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(5, 1, 22, 'inactive', '2023-03-10', '2023-08-15', 5, 300.00, 60.00, NULL, NULL, 'Has not visited in several months.', NULL, 75, 'bronze', 300.00, 0, 0, 0, 1, '2025-05-27 01:37:30', NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(6, 1, 25, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, NULL, NULL, 0, 'bronze', 0.00, 0, 0, 0, 0, NULL, NULL, '2025-05-27 02:16:04', '2025-05-27 02:16:04'),
(7, 5, 27, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, 'test', NULL, 50, 'bronze', 0.00, 0, 0, 0, 1, '2025-05-27 03:16:12', NULL, '2025-05-27 02:22:45', '2025-05-27 03:16:12'),
(8, 5, 28, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, 'probando', NULL, 0, 'bronze', 0.00, 0, 0, 0, 1, '2025-05-27 02:24:21', NULL, '2025-05-27 02:24:21', '2025-05-27 02:24:21'),
(9, 5, 29, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, 'probando', NULL, 0, 'bronze', 0.00, 0, 0, 0, 1, '2025-05-27 03:02:26', NULL, '2025-05-27 03:02:26', '2025-05-27 03:02:26'),
(10, 5, 32, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, 'probando', NULL, 0, 'bronze', 0.00, 0, 0, 0, 0, NULL, NULL, '2025-05-27 03:06:29', '2025-05-27 03:06:29'),
(11, 5, 33, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, 'probando', NULL, 0, 'bronze', 0.00, 0, 0, 0, 0, NULL, NULL, '2025-05-27 03:06:54', '2025-05-27 03:06:54'),
(12, 6, 34, 'active', '2025-05-26', NULL, 0, 0.00, 0.00, NULL, NULL, NULL, NULL, 0, 'bronze', 0.00, 0, 0, 0, 0, NULL, NULL, '2025-05-27 03:32:30', '2025-05-27 03:32:30');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_communications`
--

CREATE TABLE `customer_communications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `sent_by` bigint(20) UNSIGNED DEFAULT NULL,
  `type` enum('email','sms','push','in_app','phone_call','note') NOT NULL,
  `direction` enum('outbound','inbound') NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `status` enum('pending','sent','delivered','read','failed','bounced') NOT NULL DEFAULT 'pending',
  `sent_at` timestamp NULL DEFAULT NULL,
  `delivered_at` timestamp NULL DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `failure_reason` text DEFAULT NULL,
  `campaign_id` varchar(255) DEFAULT NULL,
  `template_id` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `customer_communications`
--

INSERT INTO `customer_communications` (`id`, `business_id`, `customer_id`, `sent_by`, `type`, `direction`, `subject`, `message`, `metadata`, `status`, `sent_at`, `delivered_at`, `read_at`, `failure_reason`, `campaign_id`, `template_id`, `created_at`, `updated_at`) VALUES
(1, 1, 18, 1, 'email', 'outbound', 'Welcome to our business!', 'Thank you for choosing our services. We look forward to serving you!', NULL, 'sent', '2025-05-03 01:37:30', NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(2, 1, 19, 1, 'email', 'outbound', 'Welcome to our business!', 'Thank you for choosing our services. We look forward to serving you!', NULL, 'sent', '2025-05-16 01:37:30', NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(3, 1, 20, 1, 'email', 'outbound', 'Welcome to our business!', 'Thank you for choosing our services. We look forward to serving you!', NULL, 'sent', '2025-05-06 01:37:30', NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(4, 1, 21, 1, 'email', 'outbound', 'Welcome to our business!', 'Thank you for choosing our services. We look forward to serving you!', NULL, 'sent', '2025-05-25 01:37:30', NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(5, 1, 22, 1, 'email', 'outbound', 'Welcome to our business!', 'Thank you for choosing our services. We look forward to serving you!', NULL, 'sent', '2025-04-30 01:37:30', NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(6, 5, 27, 4, 'email', 'outbound', 'hola', 'tes', NULL, 'sent', '2025-05-27 03:12:50', NULL, NULL, NULL, NULL, NULL, '2025-05-27 03:12:50', '2025-05-27 03:12:50');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_emergency_contacts`
--

CREATE TABLE `customer_emergency_contacts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `relationship` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_favorites`
--

CREATE TABLE `customer_favorites` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_loyalty_points`
--

CREATE TABLE `customer_loyalty_points` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `type` enum('earned','redeemed','expired','adjusted','bonus') NOT NULL,
  `points` int(11) NOT NULL,
  `points_value` decimal(10,2) DEFAULT NULL,
  `description` text NOT NULL,
  `reference_type` varchar(255) DEFAULT NULL,
  `reference_id` bigint(20) UNSIGNED DEFAULT NULL,
  `processed_by` bigint(20) UNSIGNED DEFAULT NULL,
  `expires_at` date DEFAULT NULL,
  `is_expired` tinyint(1) NOT NULL DEFAULT 0,
  `running_balance` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `customer_loyalty_points`
--

INSERT INTO `customer_loyalty_points` (`id`, `business_id`, `customer_id`, `type`, `points`, `points_value`, `description`, `reference_type`, `reference_id`, `processed_by`, `expires_at`, `is_expired`, `running_balance`, `created_at`, `updated_at`) VALUES
(1, 1, 18, 'earned', 50, NULL, 'Welcome bonus', NULL, NULL, 1, NULL, 0, 50, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(2, 1, 18, 'earned', 450, NULL, 'Points from bookings', NULL, NULL, 1, NULL, 0, 500, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(3, 1, 19, 'earned', 50, NULL, 'Welcome bonus', NULL, NULL, 1, NULL, 0, 50, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(4, 1, 19, 'earned', 40, NULL, 'Points from bookings', NULL, NULL, 1, NULL, 0, 90, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(5, 1, 20, 'earned', 50, NULL, 'Welcome bonus', NULL, NULL, 1, NULL, 0, 50, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(6, 1, 20, 'earned', 190, NULL, 'Points from bookings', NULL, NULL, 1, NULL, 0, 240, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(7, 1, 21, 'earned', 50, NULL, 'Welcome bonus', NULL, NULL, 1, NULL, 0, 50, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(8, 1, 21, 'earned', 110, NULL, 'Points from bookings', NULL, NULL, 1, NULL, 0, 160, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(9, 1, 22, 'earned', 50, NULL, 'Welcome bonus', NULL, NULL, 1, NULL, 0, 50, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(10, 1, 22, 'earned', 25, NULL, 'Points from bookings', NULL, NULL, 1, NULL, 0, 75, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(11, 5, 27, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 4, NULL, 0, 50, '2025-05-27 02:22:45', '2025-05-27 02:22:45'),
(12, 5, 28, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 4, NULL, 0, 50, '2025-05-27 02:24:21', '2025-05-27 02:24:21'),
(13, 5, 29, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 4, NULL, 0, 50, '2025-05-27 03:02:26', '2025-05-27 03:02:26'),
(14, 5, 32, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 4, NULL, 0, 50, '2025-05-27 03:06:29', '2025-05-27 03:06:29'),
(15, 5, 33, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 4, NULL, 0, 50, '2025-05-27 03:06:54', '2025-05-27 03:06:54'),
(16, 5, 27, 'earned', 50, NULL, 'tdf', NULL, NULL, 4, NULL, 0, 100, '2025-05-27 03:13:55', '2025-05-27 03:13:55'),
(17, 6, 34, 'earned', 50, NULL, 'Welcome bonus for new customer', NULL, NULL, 5, NULL, 0, 50, '2025-05-27 03:32:30', '2025-05-27 03:32:30');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_referrals`
--

CREATE TABLE `customer_referrals` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `referrer_id` bigint(20) UNSIGNED NOT NULL,
  `referred_id` bigint(20) UNSIGNED DEFAULT NULL,
  `referred_name` varchar(255) NOT NULL,
  `referred_email` varchar(255) NOT NULL,
  `referred_phone` varchar(255) DEFAULT NULL,
  `status` enum('pending','contacted','converted','declined') NOT NULL DEFAULT 'pending',
  `referred_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `contacted_at` timestamp NULL DEFAULT NULL,
  `converted_at` timestamp NULL DEFAULT NULL,
  `referral_reward` decimal(10,2) DEFAULT NULL,
  `reward_claimed` tinyint(1) NOT NULL DEFAULT 0,
  `reward_claimed_at` timestamp NULL DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_tags`
--

CREATE TABLE `customer_tags` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `color` varchar(7) NOT NULL DEFAULT '#007bff',
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `customer_tags`
--

INSERT INTO `customer_tags` (`id`, `business_id`, `name`, `color`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'VIP Customer', '#ffc107', 'High-value customers with premium status', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(2, 1, 'New Customer', '#28a745', 'Recently acquired customers (within 30 days)', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(3, 1, 'Regular Customer', '#007bff', 'Frequent visitors with multiple bookings', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(4, 1, 'At Risk', '#dc3545', 'Customers who haven\'t visited recently', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(5, 1, 'Birthday This Month', '#e83e8c', 'Customers celebrating birthdays this month', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(6, 1, 'Referral Source', '#17a2b8', 'Customers who have referred others', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(7, 1, 'Corporate Client', '#6f42c1', 'Business or corporate customers', 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(8, 6, 'VIP Customer', '#ffc107', 'High-value customers', 1, '2025-05-27 03:33:14', '2025-05-27 03:33:14'),
(9, 6, 'New Customer', '#28a745', 'Recently acquired customers', 1, '2025-05-27 03:33:14', '2025-05-27 03:33:14'),
(10, 6, 'Regular Customer', '#007bff', 'Frequent visitors', 1, '2025-05-27 03:33:14', '2025-05-27 03:33:14'),
(11, 6, 'At Risk', '#dc3545', 'Customers who haven\'t visited recently', 1, '2025-05-27 03:33:14', '2025-05-27 03:33:14'),
(12, 6, 'Birthday This Month', '#e83e8c', 'Customers with birthdays this month', 1, '2025-05-27 03:33:14', '2025-05-27 03:33:14'),
(13, 5, 'VIP Customer', '#ffc107', 'High-value customers', 1, '2025-05-27 03:34:41', '2025-05-27 03:34:41'),
(14, 5, 'New Customer', '#28a745', 'Recently acquired customers', 1, '2025-05-27 03:34:41', '2025-05-27 03:34:41'),
(15, 5, 'Regular Customer', '#007bff', 'Frequent visitors', 1, '2025-05-27 03:34:41', '2025-05-27 03:34:41'),
(16, 5, 'At Risk', '#dc3545', 'Customers who haven\'t visited recently', 1, '2025-05-27 03:34:41', '2025-05-27 03:34:41'),
(17, 5, 'Birthday This Month', '#e83e8c', 'Customers with birthdays this month', 1, '2025-05-27 03:34:41', '2025-05-27 03:34:41');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `customer_tag_assignments`
--

CREATE TABLE `customer_tag_assignments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED NOT NULL,
  `customer_tag_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_by` bigint(20) UNSIGNED DEFAULT NULL,
  `assigned_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `customer_tag_assignments`
--

INSERT INTO `customer_tag_assignments` (`id`, `business_id`, `customer_id`, `customer_tag_id`, `assigned_by`, `assigned_at`, `created_at`, `updated_at`) VALUES
(1, 1, 18, 1, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(2, 1, 18, 3, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(3, 1, 19, 2, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(4, 1, 20, 3, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(5, 1, 21, 7, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(6, 1, 22, 4, 1, '2025-05-27 01:37:30', '2025-05-27 01:37:30', '2025-05-27 01:37:30');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `jobs`
--

INSERT INTO `jobs` (`id`, `queue`, `payload`, `attempts`, `reserved_at`, `available_at`, `created_at`) VALUES
(1, 'default', '{\"uuid\":\"dfb0f10f-67a1-453f-b659-b9fde82a8dde\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"cb48f933-0019-41e7-aa22-4bba603216dc\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(2, 'default', '{\"uuid\":\"b85f446a-ce84-4f0f-87ed-9d95cd860cbb\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:2;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"37266639-2f80-4c3d-82b3-7d615b0c72ea\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(3, 'default', '{\"uuid\":\"7be70051-7d5a-4ce6-b593-0d836ffcf90d\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:3;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"46a2a100-4007-4e37-85f8-a9d6590dd59a\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(4, 'default', '{\"uuid\":\"681cff0d-ee9b-40bc-a0d6-99ab584a936d\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:4;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"67d17d1d-f810-4e28-b113-735f5de44b6e\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(5, 'default', '{\"uuid\":\"5ee6f6c3-f7da-476e-a5e1-b7e24ab69f0e\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:5;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"7e232dea-14e4-4857-a5a6-a7d9a2f8dea0\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(6, 'default', '{\"uuid\":\"61168b49-c8be-4679-ac8a-adcb577b99c7\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:6;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"64ed6b8f-0ee6-433a-90f4-e60cc01b1fde\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(7, 'default', '{\"uuid\":\"b93d4a0c-ca63-475d-a5b9-951b65c644d9\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:7;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"a5d4d61b-593b-4888-9c12-16b9b9144192\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(8, 'default', '{\"uuid\":\"3868b483-a1ca-4ef5-8b33-5d6da2ac5ace\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:8;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"dfdf7a10-4a5f-4fb6-93c9-b51ee335791a\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(9, 'default', '{\"uuid\":\"36c36123-9238-4a67-b393-0ca7d20fb422\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:9;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"0970649f-8b0e-4dc7-864d-3d10bbb52413\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(10, 'default', '{\"uuid\":\"1e600fea-e120-446c-8805-e0f18780548e\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:10;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"230c032d-7d9b-4ee9-aebb-facf75c940da\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(11, 'default', '{\"uuid\":\"e941c8c8-8639-4574-b9ee-9f504f94580e\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:11;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"59cab498-3a28-437c-8e0e-2a12ea9d9a16\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(12, 'default', '{\"uuid\":\"dff0bef4-118c-4487-83ca-0d0849f7f23f\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:12;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"aee78d1e-3b0f-468a-8424-ce7bc8881180\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219318,\"delay\":null}', 0, NULL, 1748219318, 1748219318),
(13, 'default', '{\"uuid\":\"fb227334-ccc7-4cc2-9778-ca215da93630\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:13;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"fe329892-4bf5-4a0e-96aa-9e52f35eb732\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(14, 'default', '{\"uuid\":\"449b9d9d-d6d4-4bb5-b73f-eca60aecec41\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:14;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"3e92067c-1960-4901-9cf8-984d06926bfe\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(15, 'default', '{\"uuid\":\"837d550c-af33-48f8-bb8d-24c478aa6dcf\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:15;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"a76ee964-c9b5-4c59-8072-476eecbd9bd9\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(16, 'default', '{\"uuid\":\"5fbafe98-d3b8-49fb-960f-60cd8a764afb\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:16;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"*************-43a0-8102-cee6568ccbac\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(17, 'default', '{\"uuid\":\"b4120072-c0c5-4c9b-9fc2-60f2d1755f21\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:17;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"6201c978-d556-4684-bef2-42b0d76ba918\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(18, 'default', '{\"uuid\":\"1c84833b-b5a9-40bc-ab20-09b88dad1ebc\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:18;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"00141a79-8949-4afb-ad9a-76428c8bd5c0\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(19, 'default', '{\"uuid\":\"b13d4836-4e97-4fe2-b58b-c59b80e7032e\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:19;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"b5fd7c5e-a48c-4a54-a98b-203e9a00aa3b\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(20, 'default', '{\"uuid\":\"8a3ddd6d-7e4f-4b73-a462-dfd81d1653dc\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:20;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"28664c70-1baa-4db8-bca1-46aa28af2de4\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(21, 'default', '{\"uuid\":\"beda2fbb-8092-4a2d-a8ef-0df585ef2157\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:21;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9e9e28fc-659a-4cb3-833d-6556b174b640\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(22, 'default', '{\"uuid\":\"1d07e8dd-d8ee-4552-90b4-f4280bd21712\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:22;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"2d06cf44-0b4b-411c-89de-a3d5d3b81585\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(23, 'default', '{\"uuid\":\"990e8939-aad9-47cd-a91c-0d580d4d2c95\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:23;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9ee5d60c-c457-4701-af09-59ee84da486f\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(24, 'default', '{\"uuid\":\"d6eebe28-ccf1-4ca6-87d5-bf8ea6f323d5\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:24;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"43268c95-5d1d-4c4e-89b3-b6226782a59a\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748219378,\"delay\":null}', 0, NULL, 1748219378, 1748219378),
(25, 'default', '{\"uuid\":\"148a44ef-d9d5-4012-a595-51c18b95da48\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:25;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"51d65743-e2e6-45e4-ac0a-5f264bf34b52\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(26, 'default', '{\"uuid\":\"8948706a-98f3-4363-aee5-bb5d267f6578\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:26;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"1776ae96-ac65-4979-bf3f-5a58dc7f9594\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(27, 'default', '{\"uuid\":\"aa6fe7fa-2850-4bd0-8e35-9f4948685576\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:27;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"ead5af88-e3c2-43af-aa61-897a66197ad3\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(28, 'default', '{\"uuid\":\"5b738d37-b4d6-482f-9d6f-bd8f9e18b6f9\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:28;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"6e88698b-fd5a-4246-a5e4-d2516f1aaae9\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(29, 'default', '{\"uuid\":\"c41cf933-6c4f-4988-b8fe-8b6a4074957f\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:29;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9b840456-52a5-451a-a76a-75427d8179b2\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(30, 'default', '{\"uuid\":\"efd56a54-f4ae-440f-bbf1-239ba6d67fd4\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:30;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"0c036f69-5db0-4c8f-bcda-f4e168d84818\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(31, 'default', '{\"uuid\":\"50815f37-a026-400c-b363-38435e8bce99\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:31;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"b0141006-53c0-4e10-b537-1fe0af727f17\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(32, 'default', '{\"uuid\":\"d0877704-70be-4c0f-acff-0d165b0701db\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:32;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"9185cff9-672e-41c7-ae36-fba95a4f21e8\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(33, 'default', '{\"uuid\":\"2ba7ad07-4d79-4fef-9bba-5519fb2ee09e\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:33;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"e75b266d-2e61-41b5-8dc2-66fcfd8bfaaf\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(34, 'default', '{\"uuid\":\"41eaac8f-ec50-45f5-874b-2d8c6203899a\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:34;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"0add53de-a98c-45c8-92fd-e26ab187857f\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(35, 'default', '{\"uuid\":\"00f5ec1d-8583-433e-b4a3-a7db740f86e1\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:35;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"40d2c8e6-89c8-4a82-8d87-417e87ae41b5\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194),
(36, 'default', '{\"uuid\":\"18144ace-b9e5-4f59-8e80-bcd870d4716f\",\"displayName\":\"App\\\\Notifications\\\\SecurityAlertNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:43:\\\"App\\\\Notifications\\\\SecurityAlertNotification\\\":2:{s:8:\\\"\\u0000*\\u0000alert\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:24:\\\"App\\\\Models\\\\SecurityAlert\\\";s:2:\\\"id\\\";i:36;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"d170dde9-2528-4a67-96f0-a7f8f9ccffc5\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"},\"createdAt\":1748222194,\"delay\":null}', 0, NULL, 1748222194, 1748222194);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `landing_page_analytics`
--

CREATE TABLE `landing_page_analytics` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `landing_page_id` bigint(20) UNSIGNED DEFAULT NULL,
  `service_id` bigint(20) UNSIGNED DEFAULT NULL,
  `booking_id` bigint(20) UNSIGNED DEFAULT NULL,
  `event_type` varchar(255) NOT NULL,
  `page` varchar(255) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `referrer` varchar(255) DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `landing_service_settings`
--

CREATE TABLE `landing_service_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `homepage_display_count` int(11) NOT NULL DEFAULT 6,
  `show_pricing` tinyint(1) NOT NULL DEFAULT 1,
  `show_duration` tinyint(1) NOT NULL DEFAULT 1,
  `show_description` tinyint(1) NOT NULL DEFAULT 1,
  `show_images` tinyint(1) NOT NULL DEFAULT 1,
  `show_categories` tinyint(1) NOT NULL DEFAULT 1,
  `layout_type` enum('grid','list','carousel','masonry') NOT NULL DEFAULT 'grid',
  `grid_columns` int(11) NOT NULL DEFAULT 3,
  `enable_filtering` tinyint(1) NOT NULL DEFAULT 1,
  `enable_sorting` tinyint(1) NOT NULL DEFAULT 1,
  `enable_search` tinyint(1) NOT NULL DEFAULT 1,
  `featured_services` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`featured_services`)),
  `hidden_services` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`hidden_services`)),
  `service_order` enum('manual','alphabetical','price_low_high','price_high_low','duration','popularity') NOT NULL DEFAULT 'manual',
  `group_by_category` tinyint(1) NOT NULL DEFAULT 1,
  `service_card_style` varchar(255) NOT NULL DEFAULT 'modern',
  `show_service_icons` tinyint(1) NOT NULL DEFAULT 1,
  `show_availability_status` tinyint(1) NOT NULL DEFAULT 1,
  `show_special_offers` tinyint(1) NOT NULL DEFAULT 1,
  `show_reviews_rating` tinyint(1) NOT NULL DEFAULT 1,
  `enable_quick_booking` tinyint(1) NOT NULL DEFAULT 1,
  `booking_button_style` varchar(255) NOT NULL DEFAULT 'primary',
  `booking_button_text` varchar(255) NOT NULL DEFAULT 'Book Now',
  `show_booking_calendar` tinyint(1) NOT NULL DEFAULT 0,
  `enable_service_seo` tinyint(1) NOT NULL DEFAULT 1,
  `generate_service_sitemap` tinyint(1) NOT NULL DEFAULT 1,
  `track_service_analytics` tinyint(1) NOT NULL DEFAULT 1,
  `seo_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`seo_config`)),
  `mobile_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`mobile_config`)),
  `tablet_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tablet_config`)),
  `mobile_optimized` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `landing_service_settings`
--

INSERT INTO `landing_service_settings` (`id`, `business_id`, `homepage_display_count`, `show_pricing`, `show_duration`, `show_description`, `show_images`, `show_categories`, `layout_type`, `grid_columns`, `enable_filtering`, `enable_sorting`, `enable_search`, `featured_services`, `hidden_services`, `service_order`, `group_by_category`, `service_card_style`, `show_service_icons`, `show_availability_status`, `show_special_offers`, `show_reviews_rating`, `enable_quick_booking`, `booking_button_style`, `booking_button_text`, `show_booking_calendar`, `enable_service_seo`, `generate_service_sitemap`, `track_service_analytics`, `seo_config`, `mobile_config`, `tablet_config`, `mobile_optimized`, `created_at`, `updated_at`) VALUES
(1, 6, 6, 1, 1, 1, 1, 1, 'grid', 3, 1, 1, 1, NULL, NULL, 'manual', 1, 'modern', 1, 1, 1, 1, 1, 'primary', 'Book Now', 0, 1, 1, 1, NULL, NULL, NULL, 1, '2025-05-27 13:34:21', '2025-05-27 13:34:32'),
(2, 5, 6, 1, 1, 1, 1, 1, 'grid', 3, 1, 1, 1, NULL, NULL, 'manual', 1, 'modern', 1, 1, 1, 1, 1, 'primary', 'Book Now', 1, 1, 1, 1, NULL, NULL, NULL, 1, '2025-05-27 18:47:40', '2025-05-27 18:47:40');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_01_01_000001_create_businesses_table', 1),
(5, '2025_01_01_000002_create_business_branches_table', 1),
(6, '2025_01_01_000003_create_business_categories_table', 1),
(7, '2025_01_01_000004_create_business_tags_table', 1),
(8, '2025_01_01_000005_create_business_operating_hours_table', 1),
(9, '2025_01_01_000006_create_business_holidays_table', 1),
(10, '2025_01_01_000007_create_resources_table', 1),
(11, '2025_01_01_000008_create_services_table', 1),
(12, '2025_01_01_000009_create_service_resources_table', 1),
(13, '2025_01_01_000010_create_bookings_table', 1),
(14, '2025_01_01_000011_create_booking_services_table', 1),
(15, '2025_01_01_000012_create_booking_payments_table', 1),
(16, '2025_01_01_000013_create_waiting_lists_table', 1),
(17, '2025_05_23_012113_create_personal_access_tokens_table', 1),
(18, '2025_05_23_012128_create_permission_tables', 1),
(19, '2025_05_23_012224_add_fields_to_users_table', 1),
(20, '2025_05_23_015106_add_two_factor_to_users_table', 1),
(21, '2025_01_01_000100_create_settings_table', 2),
(22, '2025_05_25_151717_add_recurring_sequence_to_bookings_table', 3),
(23, '2025_05_25_204038_add_customer_fields_to_users_table', 4),
(25, '2024_01_15_000000_enhance_roles_table', 6),
(26, '2024_01_15_000001_create_role_audit_logs_table', 6),
(27, '2025_05_25_233607_create_two_factor_settings_table', 7),
(28, '2024_01_16_000000_create_security_alerts_table', 8),
(29, '2024_01_17_000000_add_archived_fields_to_audit_logs', 8),
(30, '2025_05_26_013200_add_special_requests_to_bookings_table', 9),
(31, '2025_05_26_013250_add_address_to_businesses_table', 10),
(32, '2025_05_26_104519_create_customer_favorites_table', 11),
(33, '2025_05_26_105104_create_service_reviews_table', 12),
(34, '2025_05_26_105645_create_service_availability_table', 13),
(35, '2025_05_26_105938_create_service_images_table', 14),
(36, '2025_05_26_110614_add_privacy_settings_to_users_table', 15),
(37, '2025_01_15_000001_add_business_id_to_resource_types_table', 16),
(38, '2025_01_15_000002_migrate_existing_resource_types_to_businesses', 16),
(39, '2025_01_01_000020_create_customer_tags_table', 17),
(40, '2025_01_01_000021_create_customer_tag_assignments_table', 17),
(41, '2025_01_01_000022_create_customer_communications_table', 17),
(42, '2025_01_01_000023_create_customer_loyalty_points_table', 17),
(43, '2025_01_01_000024_create_customer_business_profiles_table', 17),
(44, '2025_05_23_012129_enhance_roles_table', 17),
(45, '2025_01_01_000030_create_customer_emergency_contacts_table', 18),
(46, '2025_01_01_000031_create_customer_addresses_table', 18),
(47, '2025_01_01_000032_create_customer_referrals_table', 18),
(48, '2025_01_20_000001_create_business_landing_pages_table', 19),
(49, '2025_01_20_000002_create_business_landing_page_sections_table', 20),
(50, '2025_01_20_000003_create_business_seo_settings_table', 21),
(51, '2025_01_20_000004_add_landing_page_fields_to_businesses_table', 22),
(52, '2025_01_21_000002_create_landing_service_settings_table', 23),
(53, '2025_01_21_000001_add_service_display_fields_to_services_table', 24),
(54, '2025_05_27_175656_create_landing_page_analytics_table', 25),
(55, '2025_05_27_180952_add_theme_customization_to_business_landing_pages_table', 26),
(56, '2025_01_21_000001_create_business_gallery_images_table', 27),
(57, '2025_01_21_000002_create_business_gallery_categories_table', 27),
(58, '2025_05_25_204506_create_notifications_table', 28),
(59, '2025_01_27_000002_fix_owner_notification_preferences_constraints', 29),
(60, '2025_01_27_000001_create_owner_notification_preferences_table', 30);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `model_has_permissions`
--

CREATE TABLE `model_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `model_has_roles`
--

CREATE TABLE `model_has_roles` (
  `role_id` bigint(20) UNSIGNED NOT NULL,
  `model_type` varchar(255) NOT NULL,
  `model_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `model_has_roles`
--

INSERT INTO `model_has_roles` (`role_id`, `model_type`, `model_id`) VALUES
(1, 'App\\Models\\User', 1),
(1, 'App\\Models\\User', 3),
(5, 'App\\Models\\User', 4),
(5, 'App\\Models\\User', 5),
(5, 'App\\Models\\User', 6),
(5, 'App\\Models\\User', 16),
(7, 'App\\Models\\User', 2),
(7, 'App\\Models\\User', 3),
(7, 'App\\Models\\User', 12),
(7, 'App\\Models\\User', 13),
(7, 'App\\Models\\User', 15),
(7, 'App\\Models\\User', 35);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(255) NOT NULL,
  `notifiable_type` varchar(255) NOT NULL,
  `notifiable_id` bigint(20) UNSIGNED NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `owner_notifications`
--

CREATE TABLE `owner_notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `owner_id` bigint(20) UNSIGNED NOT NULL,
  `notification_type` enum('booking','cancellation','payment','review','system','marketing','alert','reminder','customer_message','waiting_list') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`data`)),
  `source_type` varchar(255) DEFAULT NULL,
  `source_id` bigint(20) UNSIGNED DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT 0,
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0,
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `expires_at` timestamp NULL DEFAULT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `owner_notifications`
--

INSERT INTO `owner_notifications` (`id`, `business_id`, `owner_id`, `notification_type`, `title`, `message`, `data`, `source_type`, `source_id`, `is_read`, `is_deleted`, `priority`, `expires_at`, `read_at`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 3, 'cancellation', 'Booking Cancelled', 'Appointment cancelled by Lisa Davis for Consultation', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Consultation\",\"cancellation_reason\":\"Customer request\",\"refund_amount\":377}', 'booking', 542, 0, 0, 'urgent', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(2, 1, 3, 'cancellation', 'Booking Cancelled', 'Appointment cancelled by John Doe for Cleaning', '{\"customer_name\":\"John Doe\",\"service_name\":\"Cleaning\",\"cancellation_reason\":\"Customer request\",\"refund_amount\":451}', 'booking', 780, 1, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(3, 1, 3, 'system', 'System Notification', 'Backup completed successfully', '{\"type\":\"maintenance\"}', 'system', NULL, 1, 0, 'urgent', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(4, 1, 3, 'marketing', 'Marketing Opportunity', 'New promotional campaign available for your business', '{\"campaign_name\":\"Summer Special\",\"discount\":\"20%\",\"expires_at\":\"2025-06-27T15:59:38.459567Z\"}', 'marketing', 78, 0, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(5, 1, 3, 'review', 'New Positive Review', 'New 5-star review from Lisa Davis for Repair', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Repair\",\"rating\":5,\"comment\":\"Great service, highly recommended!\"}', 'review', 540, 1, 0, 'low', NULL, '2025-05-21 14:57:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(6, 2, 1, 'marketing', 'Marketing Opportunity', 'New promotional campaign available for your business', '{\"campaign_name\":\"Summer Special\",\"discount\":\"20%\",\"expires_at\":\"2025-06-27T15:59:38.463295Z\"}', 'marketing', 29, 0, 0, 'normal', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(7, 2, 1, 'payment', 'Payment Received', 'Payment of $177 received from Mike Johnson', '{\"customer_name\":\"Mike Johnson\",\"amount\":355,\"currency\":\"USD\",\"transaction_id\":\"TXN302887\"}', 'payment', 862, 1, 0, 'high', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(8, 2, 1, 'review', 'New Review', 'New 3-star review from Lisa Davis for Training', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Training\",\"rating\":3,\"comment\":\"Great service, highly recommended!\"}', 'review', 391, 1, 0, 'high', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(9, 2, 1, 'review', 'New Review', 'New 3-star review from John Doe for Consultation', '{\"customer_name\":\"John Doe\",\"service_name\":\"Consultation\",\"rating\":3,\"comment\":\"Great service, highly recommended!\"}', 'review', 745, 0, 0, 'urgent', NULL, '2025-05-08 20:03:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(10, 2, 1, 'reminder', 'Business Reminder', 'Don\'t forget to update your business hours for the holiday', '{\"holiday\":\"Christmas\"}', 'reminder', NULL, 1, 0, 'low', NULL, '2025-05-12 05:11:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(11, 5, 4, 'marketing', 'Marketing Opportunity', 'New promotional campaign available for your business', '{\"campaign_name\":\"Summer Special\",\"discount\":\"20%\",\"expires_at\":\"2025-06-27T15:59:38.471177Z\"}', 'marketing', 20, 1, 1, 'low', NULL, '2025-05-28 20:28:14', '2025-05-28 20:28:35', '2025-05-28 19:59:38', '2025-05-28 20:28:35'),
(12, 5, 4, 'waiting_list', 'New Waiting List Entry', 'New customer John Doe added to waiting list for Haircut', '{\"customer_name\":\"John Doe\",\"service_name\":\"Haircut\",\"preferred_date\":\"2025-06-02\"}', 'waiting_list', 57, 1, 0, 'normal', NULL, '2025-05-11 12:38:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(13, 5, 4, 'waiting_list', 'New Waiting List Entry', 'New customer Lisa Davis added to waiting list for Massage', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Massage\",\"preferred_date\":\"2025-06-02\"}', 'waiting_list', 778, 1, 0, 'low', NULL, '2025-05-01 05:20:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(14, 5, 4, 'alert', 'Business Alert', 'Multiple booking conflicts detected for tomorrow', '{\"conflict_count\":4}', 'alert', NULL, 1, 0, 'urgent', NULL, '2025-05-28 20:28:44', NULL, '2025-05-28 19:59:38', '2025-05-28 20:28:44'),
(15, 5, 4, 'waiting_list', 'New Waiting List Entry', 'New customer John Doe added to waiting list for Repair', '{\"customer_name\":\"John Doe\",\"service_name\":\"Repair\",\"preferred_date\":\"2025-06-03\"}', 'waiting_list', 904, 0, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-29 00:49:52'),
(16, 6, 5, 'booking', 'New Booking Received', 'New appointment booked by John Doe for Massage', '{\"customer_name\":\"John Doe\",\"service_name\":\"Massage\",\"appointment_date\":\"2025-06-02T15:59:38.477428Z\",\"booking_value\":198}', 'booking', 840, 0, 0, 'urgent', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-29 00:17:31'),
(17, 6, 5, 'booking', 'New Booking Received', 'New appointment booked by Mike Johnson for Training', '{\"customer_name\":\"Mike Johnson\",\"service_name\":\"Training\",\"appointment_date\":\"2025-05-30T15:59:38.478597Z\",\"booking_value\":216}', 'booking', 382, 1, 0, 'urgent', NULL, '2025-05-28 23:22:57', NULL, '2025-05-28 19:59:38', '2025-05-28 23:22:57'),
(18, 6, 5, 'customer_message', 'New Customer Message', 'New message from Lisa Davis: Question about services', '{\"customer_name\":\"Lisa Davis\",\"subject\":\"Question about services\",\"message\":\"Hi, I would like to know more about your services.\"}', 'message', 285, 1, 0, 'high', NULL, '2025-05-28 23:49:58', NULL, '2025-05-28 19:59:38', '2025-05-28 23:49:58'),
(19, 6, 5, 'reminder', 'Business Reminder', 'Don\'t forget to update your business hours for the holiday', '{\"holiday\":\"Christmas\"}', 'reminder', NULL, 1, 0, 'urgent', NULL, '2025-05-28 23:22:57', NULL, '2025-05-28 19:59:38', '2025-05-28 23:22:57'),
(20, 6, 5, 'review', 'New Positive Review', 'New 5-star review from David Brown for Haircut', '{\"customer_name\":\"David Brown\",\"service_name\":\"Haircut\",\"rating\":5,\"comment\":\"Great service, highly recommended!\"}', 'review', 770, 1, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(21, 7, 36, 'marketing', 'Marketing Opportunity', 'New promotional campaign available for your business', '{\"campaign_name\":\"Summer Special\",\"discount\":\"20%\",\"expires_at\":\"2025-06-27T15:59:38.483747Z\"}', 'marketing', 52, 1, 0, 'urgent', NULL, '2025-05-22 12:09:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(22, 7, 36, 'alert', 'Business Alert', 'Multiple booking conflicts detected for tomorrow', '{\"conflict_count\":5}', 'alert', NULL, 0, 0, 'normal', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(23, 7, 36, 'system', 'System Notification', 'Security update completed successfully', '{\"type\":\"maintenance\"}', 'system', NULL, 1, 0, 'normal', NULL, '2025-04-28 10:10:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(24, 7, 36, 'reminder', 'Business Reminder', 'Don\'t forget to update your business hours for the holiday', '{\"holiday\":\"Christmas\"}', 'reminder', NULL, 1, 0, 'normal', NULL, '2025-05-27 12:00:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(25, 7, 36, 'payment', 'Payment Received', 'Payment of $320 received from Lisa Davis', '{\"customer_name\":\"Lisa Davis\",\"amount\":243,\"currency\":\"USD\",\"transaction_id\":\"TXN818201\"}', 'payment', 980, 0, 0, 'high', NULL, '2025-05-01 02:25:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(26, 8, 38, 'cancellation', 'Booking Cancelled', 'Appointment cancelled by Mike Johnson for Consultation', '{\"customer_name\":\"Mike Johnson\",\"service_name\":\"Consultation\",\"cancellation_reason\":\"Customer request\",\"refund_amount\":406}', 'booking', 389, 1, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(27, 8, 38, 'waiting_list', 'New Waiting List Entry', 'New customer David Brown added to waiting list for Massage', '{\"customer_name\":\"David Brown\",\"service_name\":\"Massage\",\"preferred_date\":\"2025-06-03\"}', 'waiting_list', 707, 0, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(28, 8, 38, 'review', 'New Positive Review', 'New 5-star review from Lisa Davis for Haircut', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Haircut\",\"rating\":5,\"comment\":\"Great service, highly recommended!\"}', 'review', 553, 1, 0, 'urgent', NULL, '2025-05-12 17:31:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(29, 8, 38, 'waiting_list', 'New Waiting List Entry', 'New customer Lisa Davis added to waiting list for Consultation', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Consultation\",\"preferred_date\":\"2025-06-01\"}', 'waiting_list', 683, 0, 0, 'normal', NULL, '2025-05-19 11:43:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(30, 8, 38, 'review', 'New Review', 'New 3-star review from John Doe for Massage', '{\"customer_name\":\"John Doe\",\"service_name\":\"Massage\",\"rating\":3,\"comment\":\"Great service, highly recommended!\"}', 'review', 73, 1, 0, 'urgent', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(31, 9, 39, 'reminder', 'Business Reminder', 'Don\'t forget to update your business hours for the holiday', '{\"holiday\":\"Christmas\"}', 'reminder', NULL, 0, 0, 'normal', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(32, 9, 39, 'review', 'New Review', 'New 3-star review from Lisa Davis for Consultation', '{\"customer_name\":\"Lisa Davis\",\"service_name\":\"Consultation\",\"rating\":3,\"comment\":\"Great service, highly recommended!\"}', 'review', 15, 1, 0, 'low', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(33, 9, 39, 'reminder', 'Business Reminder', 'Don\'t forget to update your business hours for the holiday', '{\"holiday\":\"Christmas\"}', 'reminder', NULL, 1, 0, 'urgent', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(34, 9, 39, 'alert', 'Business Alert', 'Multiple booking conflicts detected for tomorrow', '{\"conflict_count\":3}', 'alert', NULL, 0, 0, 'low', NULL, '2025-05-20 12:12:38', NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38'),
(35, 9, 39, 'payment', 'Payment Received', 'Payment of $414 received from John Doe', '{\"customer_name\":\"John Doe\",\"amount\":451,\"currency\":\"USD\",\"transaction_id\":\"TXN691289\"}', 'payment', 596, 0, 0, 'high', NULL, NULL, NULL, '2025-05-28 19:59:38', '2025-05-28 19:59:38');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `owner_notification_preferences`
--

CREATE TABLE `owner_notification_preferences` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `owner_id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `notification_type` enum('booking','cancellation','payment','review','system','marketing','alert','reminder','customer_message','waiting_list') NOT NULL,
  `email_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `sms_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `push_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `in_app_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `sound_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `priority_filter` enum('low','normal','high','urgent') DEFAULT NULL,
  `quiet_hours_start` time DEFAULT NULL,
  `quiet_hours_end` time DEFAULT NULL,
  `weekend_notifications` tinyint(1) NOT NULL DEFAULT 1,
  `digest_frequency` enum('never','daily','weekly','monthly') NOT NULL DEFAULT 'daily',
  `auto_mark_read` tinyint(1) NOT NULL DEFAULT 0,
  `settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`settings`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `owner_notification_preferences`
--

INSERT INTO `owner_notification_preferences` (`id`, `owner_id`, `business_id`, `notification_type`, `email_enabled`, `sms_enabled`, `push_enabled`, `in_app_enabled`, `sound_enabled`, `priority_filter`, `quiet_hours_start`, `quiet_hours_end`, `weekend_notifications`, `digest_frequency`, `auto_mark_read`, `settings`, `created_at`, `updated_at`) VALUES
(1, 4, 5, 'booking', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(2, 4, 5, 'cancellation', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(3, 4, 5, 'payment', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(4, 4, 5, 'review', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(5, 4, 5, 'system', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(6, 4, 5, 'marketing', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(7, 4, 5, 'alert', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(8, 4, 5, 'reminder', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(9, 4, 5, 'customer_message', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(10, 4, 5, 'waiting_list', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-28 23:58:08', '2025-05-28 23:58:08'),
(11, 5, 6, 'booking', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(12, 5, 6, 'cancellation', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(13, 5, 6, 'payment', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(14, 5, 6, 'review', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(15, 5, 6, 'system', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(16, 5, 6, 'marketing', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(17, 5, 6, 'alert', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(18, 5, 6, 'reminder', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(19, 5, 6, 'customer_message', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22'),
(20, 5, 6, 'waiting_list', 1, 0, 1, 1, 1, NULL, NULL, NULL, 1, 'daily', 0, '[]', '2025-05-29 00:09:22', '2025-05-29 00:09:22');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `permissions`
--

CREATE TABLE `permissions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `permissions`
--

INSERT INTO `permissions` (`id`, `name`, `guard_name`, `created_at`, `updated_at`) VALUES
(1, 'manage users', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(2, 'manage roles', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(3, 'manage settings', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(4, 'manage customers', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(5, 'manage appointments', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(6, 'manage services', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(7, 'view reports', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(8, 'manage businesses', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(9, 'view businesses', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(10, 'create businesses', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(11, 'edit businesses', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(12, 'delete businesses', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(13, 'view services', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(14, 'create services', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(15, 'edit services', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(16, 'delete services', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(17, 'manage resources', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(18, 'view resources', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(19, 'create resources', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(20, 'edit resources', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(21, 'delete resources', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(22, 'manage bookings', 'web', '2025-05-23 16:19:34', '2025-05-23 16:19:34'),
(23, 'view bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(24, 'create bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(25, 'edit bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(26, 'cancel bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(27, 'check-in bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(28, 'check-out bookings', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(29, 'view calendar', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(30, 'manage calendar', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(31, 'create calendar events', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(32, 'edit calendar events', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(33, 'delete calendar events', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(34, 'manage payments', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(35, 'view payments', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(36, 'process payments', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(37, 'refund payments', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(38, 'export reports', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(39, 'manage waiting lists', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(40, 'view waiting lists', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(41, 'notify waiting lists', 'web', '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(42, 'manage notifications', 'web', '2025-05-23 22:12:07', '2025-05-23 22:12:07'),
(43, 'view notifications', 'web', '2025-05-23 22:12:07', '2025-05-23 22:12:07'),
(44, 'send notifications', 'web', '2025-05-23 22:12:07', '2025-05-23 22:12:07'),
(45, 'view users', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(46, 'create users', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(47, 'edit users', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(48, 'delete users', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(49, 'view roles', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(50, 'create roles', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(51, 'edit roles', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(52, 'delete roles', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(53, 'view settings', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(54, 'edit settings', 'web', '2025-05-24 01:14:14', '2025-05-24 01:14:14'),
(55, 'manage system settings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(56, 'manage server configuration', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(57, 'manage backups', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(58, 'manage audit logs', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(59, 'manage integrations', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(60, 'manage security settings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(61, 'view system monitoring', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(62, 'manage database', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(63, 'manage file system', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(64, 'manage email settings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(65, 'manage sms settings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(66, 'manage payment gateways', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(67, 'manage user roles', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(68, 'assign roles', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(69, 'revoke roles', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(70, 'view user activity', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(71, 'manage user permissions', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(72, 'reset user passwords', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(73, 'deactivate users', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(74, 'manage permissions', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(75, 'assign permissions', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(76, 'revoke permissions', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(77, 'view permission reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(78, 'manage role hierarchy', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(79, 'create permission templates', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(80, 'manage security policies', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(81, 'manage business settings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(82, 'manage business categories', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(83, 'manage operating hours', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(84, 'manage holidays', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(85, 'manage locations', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(86, 'view business reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(87, 'manage business branding', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(88, 'manage service categories', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(89, 'manage service pricing', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(90, 'manage service availability', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(91, 'manage service resources', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(92, 'manage service dependencies', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(93, 'view service reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(94, 'manage service intervals', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(95, 'delete bookings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(96, 'reschedule bookings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(97, 'manage recurring bookings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(98, 'manage resource types', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(99, 'manage resource availability', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(100, 'assign resources', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(101, 'view resource utilization', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(102, 'manage resource maintenance', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(103, 'manage resource bookings', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(104, 'view resource reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(105, 'create customers', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(106, 'edit customers', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(107, 'delete customers', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(108, 'view customers', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(109, 'view customer history', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(110, 'manage customer preferences', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(111, 'send customer notifications', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(112, 'export customer data', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(113, 'manage customer reviews', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(114, 'view customer reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(115, 'manage customer communications', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(116, 'view booking reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(117, 'view revenue reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(118, 'view business analytics', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(119, 'schedule reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(120, 'manage report templates', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(121, 'view real-time analytics', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(122, 'view performance metrics', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(123, 'view audit reports', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(124, 'view financial data', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(125, 'process refunds', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(126, 'manage pricing', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(127, 'manage discounts', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(128, 'manage taxes', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(129, 'view financial analytics', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(130, 'export financial data', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(131, 'manage payment methods', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(132, 'view transaction history', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17'),
(133, 'manage invoices', 'web', '2025-05-26 01:44:17', '2025-05-26 01:44:17');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `resources`
--

CREATE TABLE `resources` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `business_branch_id` bigint(20) UNSIGNED DEFAULT NULL,
  `resource_type_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `capacity` int(11) NOT NULL DEFAULT 1,
  `hourly_rate` decimal(10,2) DEFAULT NULL,
  `specifications` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`specifications`)),
  `availability_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`availability_rules`)),
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `resources`
--

INSERT INTO `resources` (`id`, `business_id`, `business_branch_id`, `resource_type_id`, `name`, `slug`, `description`, `capacity`, `hourly_rate`, `specifications`, `availability_rules`, `requires_approval`, `is_active`, `created_at`, `updated_at`) VALUES
(20, 6, NULL, 60, 'Alex taxi', 'alex-taxi', 'tes', 1, NULL, '[]', '{\"operating_days\":[\"monday\",\"tuesday\",\"wednesday\",\"thursday\",\"friday\"],\"available_from\":\"09:00\",\"available_until\":\"17:00\",\"advance_booking_days\":\"30\",\"minimum_notice_hours\":\"2\"}', 0, 1, '2025-05-27 00:23:57', '2025-05-27 00:23:57'),
(21, 5, NULL, 48, 'Airport Transferg', 'airport-transferg', 'testtttt', 1, NULL, '[]', '{\"operating_days\":[\"monday\",\"tuesday\",\"wednesday\",\"thursday\",\"friday\"],\"available_from\":\"09:00\",\"available_until\":\"17:00\",\"advance_booking_days\":\"30\",\"minimum_notice_hours\":\"2\"}', 0, 1, '2025-05-27 00:32:09', '2025-05-27 00:32:09');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `resource_types`
--

CREATE TABLE `resource_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color` varchar(7) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `resource_types`
--

INSERT INTO `resource_types` (`id`, `business_id`, `name`, `slug`, `description`, `icon`, `color`, `created_at`, `updated_at`) VALUES
(14, 1, 'Room', 'room', 'Physical rooms and spaces for appointments', 'fas fa-door-open', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(15, 1, 'Equipment', 'equipment', 'Medical equipment, machines, and tools', 'fas fa-tools', '#28a745', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(16, 1, 'Staff', 'staff', 'Doctors, therapists, and service providers', 'fas fa-user-md', '#17a2b8', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(17, 1, 'Vehicle', 'vehicle', 'Cars, trucks, and transportation resources', 'fas fa-car', '#dc3545', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(18, 1, 'Table', 'table', 'Dining tables, treatment tables, and workstations', 'fas fa-table', '#ffc107', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(19, 1, 'Court', 'court', 'Sports courts, tennis courts, and playing fields', 'fas fa-basketball-ball', '#fd7e14', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(20, 1, 'Computer', 'computer', 'Computers, laptops, and IT equipment', 'fas fa-desktop', '#6c757d', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(21, 1, 'Bed', 'bed', 'Hospital beds, massage beds, and treatment beds', 'fas fa-bed', '#e83e8c', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(22, 1, 'Workspace', 'workspace', 'Office spaces, coworking areas, and study rooms', 'fas fa-laptop-house', '#20c997', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(23, 1, 'Other', 'other', 'Other types of resources not listed above', 'fas fa-cube', '#6f42c1', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(24, 1, 'Airport Transfer', 'airport-transfer', 'ter', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(25, 1, 'Alex Guzmán', 'alex-guzman', 'test', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(26, 2, 'Room', 'room', 'Physical rooms and spaces for appointments', 'fas fa-door-open', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(27, 2, 'Equipment', 'equipment', 'Medical equipment, machines, and tools', 'fas fa-tools', '#28a745', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(28, 2, 'Staff', 'staff', 'Doctors, therapists, and service providers', 'fas fa-user-md', '#17a2b8', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(29, 2, 'Vehicle', 'vehicle', 'Cars, trucks, and transportation resources', 'fas fa-car', '#dc3545', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(30, 2, 'Table', 'table', 'Dining tables, treatment tables, and workstations', 'fas fa-table', '#ffc107', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(31, 2, 'Court', 'court', 'Sports courts, tennis courts, and playing fields', 'fas fa-basketball-ball', '#fd7e14', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(32, 2, 'Computer', 'computer', 'Computers, laptops, and IT equipment', 'fas fa-desktop', '#6c757d', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(33, 2, 'Bed', 'bed', 'Hospital beds, massage beds, and treatment beds', 'fas fa-bed', '#e83e8c', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(34, 2, 'Workspace', 'workspace', 'Office spaces, coworking areas, and study rooms', 'fas fa-laptop-house', '#20c997', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(35, 2, 'Other', 'other', 'Other types of resources not listed above', 'fas fa-cube', '#6f42c1', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(36, 2, 'Airport Transfer', 'airport-transfer', 'ter', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(37, 2, 'Alex Guzmán', 'alex-guzman', 'test', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(38, 5, 'Room', 'room', 'Physical rooms and spaces for appointments', 'fas fa-door-open', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(39, 5, 'Equipment', 'equipment', 'Medical equipment, machines, and tools', 'fas fa-tools', '#28a745', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(40, 5, 'Staff', 'staff', 'Doctors, therapists, and service providers', 'fas fa-user-md', '#17a2b8', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(41, 5, 'Vehicle', 'vehicle', 'Cars, trucks, and transportation resources', 'fas fa-car', '#dc3545', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(42, 5, 'Table', 'table', 'Dining tables, treatment tables, and workstations', 'fas fa-table', '#ffc107', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(43, 5, 'Court', 'court', 'Sports courts, tennis courts, and playing fields', 'fas fa-basketball-ball', '#fd7e14', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(44, 5, 'Computer', 'computer', 'Computers, laptops, and IT equipment', 'fas fa-desktop', '#6c757d', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(45, 5, 'Bed', 'bed', 'Hospital beds, massage beds, and treatment beds', 'fas fa-bed', '#e83e8c', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(46, 5, 'Workspace', 'workspace', 'Office spaces, coworking areas, and study rooms', 'fas fa-laptop-house', '#20c997', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(47, 5, 'Other', 'other', 'Other types of resources not listed above', 'fas fa-cube', '#6f42c1', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(48, 5, 'Airport Transfer', 'airport-transfer', 'ter', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(49, 5, 'Alex Guzmán', 'alex-guzman', 'test', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(50, 6, 'Room', 'room', 'Physical rooms and spaces for appointments', 'fas fa-door-open', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(51, 6, 'Equipment', 'equipment', 'Medical equipment, machines, and tools', 'fas fa-tools', '#28a745', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(52, 6, 'Staff', 'staff', 'Doctors, therapists, and service providers', 'fas fa-user-md', '#17a2b8', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(53, 6, 'Vehicle', 'vehicle', 'Cars, trucks, and transportation resources', 'fas fa-car', '#dc3545', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(54, 6, 'Table', 'table', 'Dining tables, treatment tables, and workstations', 'fas fa-table', '#ffc107', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(55, 6, 'Court', 'court', 'Sports courts, tennis courts, and playing fields', 'fas fa-basketball-ball', '#fd7e14', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(56, 6, 'Computer', 'computer', 'Computers, laptops, and IT equipment', 'fas fa-desktop', '#6c757d', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(57, 6, 'Bed', 'bed', 'Hospital beds, massage beds, and treatment beds', 'fas fa-bed', '#e83e8c', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(58, 6, 'Workspace', 'workspace', 'Office spaces, coworking areas, and study rooms', 'fas fa-laptop-house', '#20c997', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(59, 6, 'Other', 'other', 'Other types of resources not listed above', 'fas fa-cube', '#6f42c1', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(60, 6, 'Airport Transfer', 'airport-transfer', 'ter', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(61, 6, 'Alex Guzmán', 'alex-guzman', 'test', 'fas fa-cube', '#007bff', '2025-05-26 23:52:19', '2025-05-26 23:52:19'),
(62, 6, 'Alex taxi', 'alex-taxi', 'test', 'fas fa-cube', '#007bff', '2025-05-27 00:04:40', '2025-05-27 00:04:40');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `guard_name` varchar(255) NOT NULL,
  `hierarchy_level` int(11) NOT NULL DEFAULT 5,
  `description` text DEFAULT NULL,
  `is_system_role` tinyint(1) NOT NULL DEFAULT 0,
  `max_users` int(11) DEFAULT NULL,
  `security_level` int(11) NOT NULL DEFAULT 2,
  `encrypted_permissions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`encrypted_permissions`)),
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `roles`
--

INSERT INTO `roles` (`id`, `name`, `guard_name`, `hierarchy_level`, `description`, `is_system_role`, `max_users`, `security_level`, `encrypted_permissions`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Super Admin', 'web', 0, 'Highest privilege level with complete system access and administration capabilities.', 1, NULL, 5, NULL, NULL, NULL, '2025-05-23 16:19:34', '2025-05-26 01:44:22'),
(2, 'Admin', 'web', 1, 'Administrative role with operational management capabilities across the system.', 1, NULL, 4, NULL, NULL, NULL, '2025-05-23 16:19:34', '2025-05-26 01:44:22'),
(3, 'Manager', 'web', 3, 'Operational management role with booking and customer management capabilities.', 1, NULL, 3, NULL, NULL, NULL, '2025-05-23 16:19:34', '2025-05-26 01:44:22'),
(4, 'Staff', 'web', 4, 'Basic operational role with limited booking and customer interaction capabilities.', 1, NULL, 2, NULL, NULL, NULL, '2025-05-23 16:19:34', '2025-05-26 01:44:22'),
(5, 'Business Owner', 'web', 2, 'Business-specific management role with control over owned business operations.', 1, NULL, 3, NULL, NULL, NULL, '2025-05-23 16:19:35', '2025-05-26 01:44:22'),
(6, 'Receptionist', 'web', 5, NULL, 0, NULL, 2, NULL, NULL, NULL, '2025-05-23 16:19:35', '2025-05-23 16:19:35'),
(7, 'Customer', 'web', 5, 'End-user role with access to customer portal and booking capabilities.', 1, NULL, 1, NULL, NULL, NULL, '2025-05-23 16:19:35', '2025-05-26 01:44:22');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `role_audit_logs`
--

CREATE TABLE `role_audit_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `target_type` varchar(100) DEFAULT NULL,
  `target_id` bigint(20) UNSIGNED DEFAULT NULL,
  `target_name` varchar(255) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `risk_level` tinyint(4) NOT NULL DEFAULT 1,
  `additional_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`additional_data`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `archived_at` timestamp NULL DEFAULT NULL,
  `archive_file` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `role_audit_logs`
--

INSERT INTO `role_audit_logs` (`id`, `user_id`, `action`, `target_type`, `target_id`, `target_name`, `old_values`, `new_values`, `ip_address`, `user_agent`, `session_id`, `risk_level`, `additional_data`, `created_at`, `updated_at`, `archived_at`, `archive_file`) VALUES
(1, 1, '2fa_verification_success', 'security', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 3, '{\"action\":\"role_create\",\"verified_at\":\"2025-05-25T22:42:30.459695Z\"}', '2025-05-26 02:42:30', '2025-05-26 02:42:30', NULL, NULL),
(2, 1, '2fa_verification_success', 'security', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 3, '{\"action\":\"role_create\",\"verified_at\":\"2025-05-25T23:30:16.786917Z\"}', '2025-05-26 03:30:16', '2025-05-26 03:30:16', NULL, NULL),
(3, 1, '2fa_enable', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"enable\",\"timestamp\":\"2025-05-25T23:45:10.212379Z\"}', '2025-05-26 03:45:10', '2025-05-26 03:45:10', NULL, NULL),
(4, 1, '2fa_settings_updated', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"changes\":{\"2fa_enabled\":{\"old\":true,\"new\":\"1\"},\"2fa_required_for_roles\":{\"old\":false,\"new\":\"1\"},\"2fa_required_for_super_admin\":{\"old\":true,\"new\":\"1\"},\"2fa_session_duration\":{\"old\":30,\"new\":\"30\"},\"2fa_code_expiry\":{\"old\":10,\"new\":\"10\"}},\"updated_at\":\"2025-05-25T23:45:27.790097Z\"}', '2025-05-26 03:45:27', '2025-05-26 03:45:27', NULL, NULL),
(5, 1, '2fa_settings_updated', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"changes\":{\"2fa_enabled\":{\"old\":1,\"new\":\"1\"},\"2fa_required_for_roles\":{\"old\":1,\"new\":\"1\"},\"2fa_session_duration\":{\"old\":30,\"new\":\"30\"},\"2fa_code_expiry\":{\"old\":10,\"new\":\"10\"}},\"updated_at\":\"2025-05-25T23:46:25.735927Z\"}', '2025-05-26 03:46:25', '2025-05-26 03:46:25', NULL, NULL),
(6, 1, '2fa_settings_updated', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"changes\":{\"2fa_required_for_roles\":{\"old\":1,\"new\":\"1\"},\"2fa_required_for_super_admin\":{\"old\":1,\"new\":\"1\"},\"2fa_session_duration\":{\"old\":30,\"new\":\"30\"},\"2fa_code_expiry\":{\"old\":10,\"new\":\"10\"}},\"updated_at\":\"2025-05-25T23:46:33.984035Z\"}', '2025-05-26 03:46:33', '2025-05-26 03:46:33', NULL, NULL),
(7, 1, '2fa_disable', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"disable\",\"timestamp\":\"2025-05-25T23:46:38.925428Z\"}', '2025-05-26 03:46:38', '2025-05-26 03:46:38', NULL, NULL),
(8, 1, '2fa_settings_updated', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"changes\":{\"2fa_required_for_roles\":{\"old\":1,\"new\":\"1\"},\"2fa_session_duration\":{\"old\":30,\"new\":\"30\"},\"2fa_code_expiry\":{\"old\":10,\"new\":\"10\"}},\"updated_at\":\"2025-05-25T23:46:45.253304Z\"}', '2025-05-26 03:46:45', '2025-05-26 03:46:45', NULL, NULL),
(9, 1, '2fa_disable_roles', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"disable_roles\",\"timestamp\":\"2025-05-25T23:47:31.053031Z\"}', '2025-05-26 03:47:31', '2025-05-26 03:47:31', NULL, NULL),
(10, 1, '2fa_enable', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"enable\",\"timestamp\":\"2025-05-25T23:49:27.352660Z\"}', '2025-05-26 03:49:27', '2025-05-26 03:49:27', NULL, NULL),
(11, 1, '2fa_enable_roles', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"enable_roles\",\"timestamp\":\"2025-05-25T23:49:34.923123Z\"}', '2025-05-26 03:49:34', '2025-05-26 03:49:34', NULL, NULL),
(12, 1, '2fa_disable_roles', 'security_settings', NULL, NULL, NULL, NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '0s7CxQUx2fqovprJjgqgWuHSmRKQe03qgh2jhmIJ', 4, '{\"action\":\"disable_roles\",\"timestamp\":\"2025-05-25T23:49:37.684610Z\"}', '2025-05-26 03:49:37', '2025-05-26 03:49:37', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `role_has_permissions`
--

CREATE TABLE `role_has_permissions` (
  `permission_id` bigint(20) UNSIGNED NOT NULL,
  `role_id` bigint(20) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `role_has_permissions`
--

INSERT INTO `role_has_permissions` (`permission_id`, `role_id`) VALUES
(1, 1),
(1, 2),
(2, 1),
(3, 1),
(4, 1),
(4, 2),
(5, 1),
(6, 1),
(6, 2),
(6, 5),
(7, 1),
(7, 2),
(7, 3),
(7, 4),
(7, 5),
(8, 1),
(8, 2),
(9, 1),
(9, 2),
(9, 5),
(9, 6),
(9, 7),
(10, 1),
(10, 2),
(11, 1),
(11, 2),
(12, 1),
(13, 1),
(13, 2),
(13, 3),
(13, 5),
(13, 6),
(13, 7),
(14, 1),
(14, 2),
(14, 5),
(15, 1),
(15, 2),
(15, 5),
(16, 1),
(17, 1),
(18, 1),
(18, 6),
(19, 1),
(20, 1),
(21, 1),
(22, 1),
(22, 2),
(22, 3),
(22, 5),
(22, 6),
(23, 1),
(23, 2),
(23, 3),
(23, 4),
(23, 5),
(23, 6),
(23, 7),
(24, 1),
(24, 2),
(24, 3),
(24, 5),
(24, 6),
(24, 7),
(25, 1),
(25, 2),
(25, 3),
(25, 5),
(25, 6),
(26, 1),
(26, 2),
(26, 3),
(26, 5),
(26, 7),
(27, 1),
(27, 2),
(27, 3),
(27, 4),
(27, 5),
(27, 6),
(28, 1),
(28, 2),
(28, 3),
(28, 4),
(28, 5),
(28, 6),
(29, 1),
(29, 2),
(29, 3),
(29, 4),
(29, 5),
(29, 6),
(30, 1),
(30, 2),
(30, 5),
(31, 1),
(31, 6),
(32, 1),
(33, 1),
(34, 1),
(35, 1),
(35, 6),
(36, 1),
(37, 1),
(38, 1),
(38, 2),
(39, 1),
(39, 2),
(39, 3),
(39, 5),
(39, 6),
(40, 1),
(40, 6),
(41, 1),
(41, 6),
(42, 1),
(43, 1),
(43, 6),
(44, 1),
(44, 2),
(44, 5),
(44, 6),
(45, 1),
(45, 2),
(46, 1),
(46, 2),
(47, 1),
(47, 2),
(48, 1),
(49, 1),
(50, 1),
(51, 1),
(52, 1),
(53, 1),
(54, 1),
(55, 1),
(56, 1),
(57, 1),
(58, 1),
(59, 1),
(60, 1),
(61, 1),
(62, 1),
(63, 1),
(64, 1),
(65, 1),
(66, 1),
(67, 1),
(67, 2),
(68, 1),
(68, 2),
(69, 1),
(70, 1),
(70, 2),
(71, 1),
(72, 1),
(72, 2),
(73, 1),
(73, 2),
(74, 1),
(75, 1),
(76, 1),
(77, 1),
(78, 1),
(79, 1),
(80, 1),
(81, 1),
(81, 2),
(81, 5),
(82, 1),
(82, 2),
(83, 1),
(83, 2),
(83, 5),
(84, 1),
(84, 2),
(84, 5),
(85, 1),
(85, 2),
(85, 5),
(86, 1),
(86, 2),
(86, 5),
(87, 1),
(87, 2),
(87, 5),
(88, 1),
(88, 2),
(88, 5),
(89, 1),
(89, 2),
(89, 5),
(90, 1),
(90, 2),
(90, 3),
(90, 5),
(91, 1),
(91, 2),
(91, 5),
(92, 1),
(92, 2),
(93, 1),
(93, 2),
(93, 5),
(94, 1),
(95, 1),
(96, 1),
(96, 2),
(96, 3),
(96, 5),
(97, 1),
(97, 2),
(98, 1),
(99, 1),
(100, 1),
(101, 1),
(101, 2),
(102, 1),
(103, 1),
(104, 1),
(105, 1),
(105, 2),
(106, 1),
(106, 2),
(107, 1),
(108, 1),
(108, 2),
(108, 3),
(108, 4),
(108, 5),
(109, 1),
(109, 2),
(109, 3),
(109, 4),
(109, 5),
(110, 1),
(110, 2),
(110, 5),
(111, 1),
(111, 2),
(111, 3),
(111, 5),
(112, 1),
(112, 2),
(113, 1),
(113, 2),
(114, 1),
(114, 2),
(114, 3),
(114, 5),
(115, 1),
(116, 1),
(116, 2),
(116, 3),
(116, 5),
(117, 1),
(117, 2),
(117, 5),
(118, 1),
(118, 2),
(118, 5),
(119, 1),
(120, 1),
(121, 1),
(122, 1),
(122, 2),
(123, 1),
(124, 1),
(124, 2),
(124, 5),
(125, 1),
(126, 1),
(127, 1),
(128, 1),
(129, 1),
(129, 2),
(129, 5),
(130, 1),
(131, 1),
(132, 1),
(132, 2),
(132, 5),
(133, 1);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `security_alerts`
--

CREATE TABLE `security_alerts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(50) NOT NULL,
  `severity` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `status` enum('open','investigating','resolved','false_positive','ignored') NOT NULL DEFAULT 'open',
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `message` varchar(255) NOT NULL,
  `details` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`details`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT '2025-05-26 04:28:08',
  `resolved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `archived_at` timestamp NULL DEFAULT NULL,
  `archive_file` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `security_alerts`
--

INSERT INTO `security_alerts` (`id`, `type`, `severity`, `status`, `user_id`, `message`, `details`, `ip_address`, `user_agent`, `timestamp`, `resolved_by`, `resolved_at`, `resolution_notes`, `created_at`, `updated_at`, `archived_at`, `archive_file`) VALUES
(1, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(2, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(3, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(4, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(5, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(6, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(7, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(8, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(9, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(10, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(11, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable_roles\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(12, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 04:28:38', NULL, NULL, NULL, '2025-05-26 04:28:38', '2025-05-26 04:28:38', NULL, NULL),
(13, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(14, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(15, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(16, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(17, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(18, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(19, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(20, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(21, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(22, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(23, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable_roles\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(24, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 04:29:38', NULL, NULL, NULL, '2025-05-26 04:29:38', '2025-05-26 04:29:38', NULL, NULL),
(25, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(26, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_verification_success\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(27, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(28, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(29, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(30, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(31, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(32, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_settings_updated\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(33, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(34, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(35, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_enable_roles\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL),
(36, 'off_hours_access', 'medium', 'open', 1, 'High-privilege user accessed system during off-hours', '{\"user_email\":\"<EMAIL>\",\"action\":\"2fa_disable_roles\"}', '::1', NULL, '2025-05-26 05:16:34', NULL, NULL, NULL, '2025-05-26 05:16:34', '2025-05-26 05:16:34', NULL, NULL);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `security_logs`
--

CREATE TABLE `security_logs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `level` varchar(20) NOT NULL,
  `category` varchar(50) NOT NULL,
  `event_type` varchar(100) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `session_id` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `request_method` varchar(10) DEFAULT NULL,
  `request_url` text DEFAULT NULL,
  `message` text NOT NULL,
  `context` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`context`)),
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `event_timestamp` timestamp NOT NULL DEFAULT '2025-05-26 04:28:08',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `security_metrics`
--

CREATE TABLE `security_metrics` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `metric_name` varchar(100) NOT NULL,
  `metric_type` varchar(50) NOT NULL,
  `category` varchar(50) NOT NULL,
  `value` decimal(15,4) NOT NULL,
  `unit` varchar(20) DEFAULT NULL,
  `dimensions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`dimensions`)),
  `tags` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tags`)),
  `recorded_at` timestamp NOT NULL DEFAULT '2025-05-26 04:28:08',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `services`
--

CREATE TABLE `services` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `service_category_id` bigint(20) UNSIGNED DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `short_description` text DEFAULT NULL,
  `duration_minutes` int(11) NOT NULL,
  `base_price` decimal(10,2) NOT NULL,
  `deposit_amount` decimal(10,2) DEFAULT NULL,
  `deposit_required` tinyint(1) NOT NULL DEFAULT 0,
  `buffer_time_before` int(11) NOT NULL DEFAULT 0,
  `buffer_time_after` int(11) NOT NULL DEFAULT 0,
  `max_advance_booking_days` int(11) DEFAULT NULL,
  `min_advance_booking_hours` int(11) DEFAULT NULL,
  `max_participants` int(11) NOT NULL DEFAULT 1,
  `pricing_variables` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pricing_variables`)),
  `booking_rules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`booking_rules`)),
  `online_booking_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `featured_on_landing` tinyint(1) NOT NULL DEFAULT 0,
  `landing_display_order` int(11) DEFAULT NULL,
  `landing_display_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`landing_display_config`)),
  `show_price_on_landing` tinyint(1) NOT NULL DEFAULT 1,
  `show_duration_on_landing` tinyint(1) NOT NULL DEFAULT 1,
  `show_description_on_landing` tinyint(1) NOT NULL DEFAULT 1,
  `show_image_on_landing` tinyint(1) NOT NULL DEFAULT 1,
  `landing_page_title` varchar(255) DEFAULT NULL,
  `landing_page_description` text DEFAULT NULL,
  `landing_page_keywords` text DEFAULT NULL,
  `quick_booking_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `booking_button_text` varchar(255) DEFAULT NULL,
  `booking_button_color` varchar(255) DEFAULT NULL,
  `landing_page_views` int(11) NOT NULL DEFAULT 0,
  `landing_page_clicks` int(11) NOT NULL DEFAULT 0,
  `last_landing_view` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `services`
--

INSERT INTO `services` (`id`, `business_id`, `service_category_id`, `name`, `slug`, `description`, `short_description`, `duration_minutes`, `base_price`, `deposit_amount`, `deposit_required`, `buffer_time_before`, `buffer_time_after`, `max_advance_booking_days`, `min_advance_booking_hours`, `max_participants`, `pricing_variables`, `booking_rules`, `online_booking_enabled`, `requires_approval`, `sort_order`, `is_active`, `is_public`, `featured_on_landing`, `landing_display_order`, `landing_display_config`, `show_price_on_landing`, `show_duration_on_landing`, `show_description_on_landing`, `show_image_on_landing`, `landing_page_title`, `landing_page_description`, `landing_page_keywords`, `quick_booking_enabled`, `booking_button_text`, `booking_button_color`, `landing_page_views`, `landing_page_clicks`, `last_landing_view`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, 'Basic Consultation', 'basic-consultation', 'A basic consultation service', NULL, 60, 100.00, NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-23 18:38:20', '2025-05-23 18:38:20'),
(2, 1, NULL, 'Premium Service', 'premium-service', 'A premium service offering', NULL, 90, 150.00, NULL, 0, 0, 0, NULL, NULL, 2, NULL, NULL, 1, 0, 0, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-23 18:38:20', '2025-05-23 18:38:20'),
(3, 1, NULL, 'Premium Service (Copy)', 'premium-service-copy', 'A premium service offering', NULL, 90, 150.00, NULL, 0, 0, 0, NULL, NULL, 2, NULL, NULL, 1, 0, 0, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-23 19:31:12', '2025-05-23 19:31:12'),
(4, 2, NULL, 'JKZ NETWORK', 'jkz-network', 'recetas', 'recetas', 60, 20.00, 10.00, 1, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-23 20:27:09', '2025-05-23 20:27:09'),
(8, 5, 13, 'Esmaltado Normal', 'esmaltado-normal', 'Esmaltado Regular, incluye una ligera limpieza en área de cutícula', 'Esmaltado Regular, incluye una ligera limpieza en área de cutícula', 40, 5.09, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 2, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:38:49', '2025-05-27 18:48:23'),
(9, 5, 13, 'Rubber Base', 'rubber-base', 'Es una base autonivelante que aporta volumen y resistencia extra a la uña natural, este servicio incluye una manicura en seco', 'Es una base autonivelante que aporta volumen y resistencia extra a la uña natural, este servicio incluye una manicura en seco', 100, 16.96, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 3, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:51:39', '2025-05-27 18:48:23'),
(10, 5, 13, 'Dip Powder', 'dip-powder', 'El dip powder es un polvo de inmersión, Este sistema no necesita curado en lámpara\r\nLo trabajamos en la uña natural, incluye una manicura en seco\r\nOfrece una duración de 15 a 21 días', 'El dip powder es un polvo de inmersión, Este sistema no necesita curado en lámpara', 100, 20.36, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 4, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:53:12', '2025-05-27 18:48:23'),
(11, 5, 13, 'Soft Gel', 'soft-gel', 'Extensiones con tip hechas a base de Gel, aporta el largo deseado mientras sus uñas crecen, con una duración de 15/21 días Este servicio incluye una manicura en seco y Esmaltado en Gel caliente', 'Extensiones con tip hechas a base de Gel, aporta el largo deseado mientras sus uñas crecen', 120, 23.75, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 5, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:54:53', '2025-05-27 18:48:23'),
(12, 5, 13, 'Foundation flex', 'foundation-flex', 'Base semipermanente, aporta protección a la uña natural\r\nEste servicio incluye una manicura en seco, duración de 15/21 días', 'Base semipermanente, aporta protección a la uña natural', 60, 11.87, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 6, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:57:50', '2025-05-27 18:48:23'),
(13, 5, 14, 'Pedicura basica + Esmaltado en Gel', 'pedicura-basica-esmaltado-en-gel', 'Esta pedicura consiste en sumergir los pies en agua tibia/caliente, lijado, plantar, limpieza en área de cutículas, exfoliación y esmaltado en gel caliente', 'Está pedicura consiste en sumergir los pies en agua tibia/caliente, lijado plantar, limpieza en área de cutículas, exfoliación y esmaltado en gel caliente', 90, 22.90, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 7, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 17:59:07', '2025-05-27 18:48:23'),
(14, 5, 14, 'Pedicura basica', 'pedicura-basica', 'Está pedicura consiste en sumergir los pies en agua tibia/caliente, lijado plantar, limpieza en área de cutículas, exfoliación y esmaltado regular', 'Está pedicura consiste en sumergir los pies en agua tibia/caliente, lijado plantar, limpieza en área de cutículas, exfoliación y esmaltado regular', 75, 11.87, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 8, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 18:00:04', '2025-05-27 18:48:23'),
(15, 5, 14, 'Pedicura Basica Sin Esmaltado', 'pedicura-basica-sin-esmaltado', 'Esta pedicura consiste en sumergir los pies en agua tibia/caliente, lijado, plantar, limpieza en área de cutículas y exfoliación', 'Esta pedicura consiste en sumergir los pies en agua tibia/caliente, lijado plantar, limpieza en área de cutículas y exfoliación', 60, 10.18, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 9, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 18:01:50', '2025-05-25 18:01:50'),
(16, 5, 15, 'Extension por unidad.', 'extension-por-unidad', 'Extension por unidad.', 'Extension por unidad.', 25, 2.54, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 10, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-25 18:02:40', '2025-05-27 18:48:23'),
(26, 9, 28, 'Personal Training Session', 'personal-training-session', 'Achieve your fitness goals with personalized training sessions. Our certified trainers create custom workout plans tailored to your needs and fitness level.', 'Individual fitness coaching session', 60, 75.00, NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 1, 1, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-27 21:50:51', '2025-05-27 21:50:51'),
(27, 9, 28, 'Fitness Assessment', 'fitness-assessment', 'Get a complete assessment of your current fitness level, including body composition analysis, strength testing, and personalized recommendations.', 'Comprehensive fitness evaluation', 45, 50.00, NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 1, 2, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-27 21:50:51', '2025-05-27 21:50:51'),
(28, 9, 29, 'Yoga Class', 'yoga-class', 'Find balance and flexibility in our welcoming yoga classes. Suitable for beginners to advanced practitioners.', 'Relaxing yoga session for all levels', 60, 25.00, NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 1, 3, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-27 21:50:51', '2025-05-27 21:50:51'),
(29, 9, 29, 'HIIT Training', 'hiit-training', 'Burn calories and build strength with our dynamic HIIT classes. Maximum results in minimum time.', 'High-intensity interval training', 45, 30.00, NULL, 0, 0, 0, NULL, NULL, 1, NULL, NULL, 1, 0, 0, 1, 1, 0, 4, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-27 21:50:51', '2025-05-27 21:50:51'),
(30, 6, NULL, 'Alex taxi', 'alex-taxi', NULL, NULL, 50, 25.01, NULL, 0, 0, 0, 30, 2, 1, NULL, NULL, 1, 0, 1, 1, 1, 0, NULL, NULL, 1, 1, 1, 1, NULL, NULL, NULL, 1, NULL, NULL, 0, 0, NULL, '2025-05-29 04:18:56', '2025-05-29 04:18:56');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_addons`
--

CREATE TABLE `service_addons` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `addon_service_id` bigint(20) UNSIGNED NOT NULL,
  `addon_price` decimal(10,2) DEFAULT NULL,
  `is_required` tinyint(1) NOT NULL DEFAULT 0,
  `max_quantity` int(11) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_availability`
--

CREATE TABLE `service_availability` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `date` date NOT NULL,
  `start_time` time NOT NULL,
  `end_time` time NOT NULL,
  `max_bookings` int(11) NOT NULL DEFAULT 1 COMMENT 'Maximum concurrent bookings for this slot',
  `current_bookings` int(11) NOT NULL DEFAULT 0 COMMENT 'Current number of bookings',
  `is_available` tinyint(1) NOT NULL DEFAULT 1,
  `unavailable_reason` varchar(255) DEFAULT NULL,
  `staff_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Specific staff members available for this slot' CHECK (json_valid(`staff_ids`)),
  `resource_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Resources allocated for this slot' CHECK (json_valid(`resource_ids`)),
  `price_override` decimal(10,2) DEFAULT NULL COMMENT 'Override price for this specific slot',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_categories`
--

CREATE TABLE `service_categories` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `color` varchar(7) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `service_categories`
--

INSERT INTO `service_categories` (`id`, `business_id`, `name`, `slug`, `description`, `icon`, `color`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(2, 1, 'Software Development', 'software-development', 'Custom software development, web applications, and mobile app development services', 'fas fa-code', '#007bff', 1, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(3, 1, 'IT Consulting', 'it-consulting', 'Technology consulting, system architecture, and digital transformation services', 'fas fa-laptop-code', '#28a745', 2, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(5, 1, 'Cybersecurity', 'cybersecurity', 'Security audits, penetration testing, and cybersecurity consulting', 'fas fa-shield-alt', '#dc3545', 4, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(6, 1, 'Data Analytics', 'data-analytics', 'Business intelligence, data analysis, and reporting solutions', 'fas fa-chart-bar', '#6f42c1', 5, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(7, 2, 'General Consultation', 'general-consultation', 'General medical consultations and health check-ups', 'fas fa-stethoscope', '#28a745', 1, 1, '2025-05-24 18:13:31', '2025-05-24 18:15:46'),
(8, 2, 'Preventive Care', 'preventive-care', 'Preventive health services, vaccinations, and wellness programs', 'fas fa-heartbeat', '#e83e8c', 2, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(9, 2, 'Diagnostic Services', 'diagnostic-services', 'Medical tests, laboratory services, and diagnostic procedures', 'fas fa-microscope', '#fd7e14', 3, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(10, 2, 'Specialist Referrals', 'specialist-referrals', 'Referrals to medical specialists and specialized treatments', 'fas fa-user-md', '#007bff', 4, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(11, 2, 'Telemedicine', 'telemedicine', 'Remote consultations and virtual health services', 'fas fa-video', '#20c997', 5, 1, '2025-05-24 18:13:31', '2025-05-24 18:13:31'),
(13, 5, 'Manicura', 'manicura', 'Manicura', 'fas fa-magic', '#007bff', 1, 1, '2025-05-25 17:23:47', '2025-05-25 17:23:47'),
(14, 5, 'Pedicura', 'pedicura', 'Pedicura', 'fas fa-hands', '#28a745', 2, 1, '2025-05-25 17:35:36', '2025-05-25 17:35:36'),
(15, 5, 'Extension por uña', 'extension-por-una', 'Extension por uña', 'fas fa-magic', '#ffc107', 3, 1, '2025-05-25 17:36:00', '2025-05-25 17:36:00'),
(26, 7, 'Hair Services', 'hair-services', 'Professional hair styling and treatments', NULL, NULL, 1, 1, '2025-05-27 21:48:34', '2025-05-27 21:48:34'),
(27, 8, 'General Dentistry', 'general-dentistry', 'Comprehensive dental care services', NULL, NULL, 1, 1, '2025-05-27 21:49:14', '2025-05-27 21:49:14'),
(28, 9, 'Personal Training', 'personal-training', 'One-on-one fitness coaching', NULL, NULL, 1, 1, '2025-05-27 21:50:51', '2025-05-27 21:50:51'),
(29, 9, 'Group Classes', 'group-classes', 'Fun and energetic group fitness classes', NULL, NULL, 2, 1, '2025-05-27 21:50:51', '2025-05-27 21:50:51');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_images`
--

CREATE TABLE `service_images` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `path` varchar(255) NOT NULL,
  `url` varchar(255) DEFAULT NULL,
  `mime_type` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL COMMENT 'File size in bytes',
  `width` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Primary image for the service',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Featured in galleries',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional image metadata' CHECK (json_valid(`metadata`)),
  `uploaded_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_resources`
--

CREATE TABLE `service_resources` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `resource_id` bigint(20) UNSIGNED NOT NULL,
  `quantity_required` int(11) NOT NULL DEFAULT 1,
  `is_required` tinyint(1) NOT NULL DEFAULT 1,
  `setup_time_minutes` int(11) NOT NULL DEFAULT 0,
  `cleanup_time_minutes` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `service_reviews`
--

CREATE TABLE `service_reviews` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `booking_id` bigint(20) UNSIGNED DEFAULT NULL,
  `rating` int(10) UNSIGNED NOT NULL COMMENT 'Rating from 1 to 5',
  `title` varchar(255) DEFAULT NULL,
  `comment` text DEFAULT NULL,
  `rating_breakdown` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Detailed ratings for different aspects' CHECK (json_valid(`rating_breakdown`)),
  `is_verified` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this review is from a verified booking',
  `is_approved` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Whether this review is approved for display',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this review is featured',
  `reviewed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('6ZvSEZRu0vq2NapEgJTaJvC7AmG4URzTfR5A7DBl', 5, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiZHFPTFhBMGs4TVdMUlR1U3I0NEF6SDJ5anRhNERVTWg1YnlvOWU2UyI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NTtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0NDoiaHR0cDovL2xvY2FsaG9zdC9ib29ra2VpL293bmVyL25vdGlmaWNhdGlvbnMiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1748478009),
('93L5fpznthXOGMzuA3xrc1EjDCMGpC11AtWMQEMJ', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YToyOntzOjY6Il90b2tlbiI7czo0MDoiZjBKSjIyamhuanZIT0FoYUxBSUkyazNQZEVzQThDdzZ0dUFHb3RQNyI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1748477101),
('dx5c1oY6Q9wwOcOWsdzKKtADo5jtYA4Fft8naiWL', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiaDJ0VFJJSWVsd1JJMjNCY0gzTVJkbE5mWjlMTGU5aFJvTzhDUXVzNSI7czozOiJ1cmwiO2E6MTp7czo4OiJpbnRlbmRlZCI7czoxMjU6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9vd25lci9ub3RpZmljYXRpb24tcHJlZmVyZW5jZXM/aWQ9MDg1NjJmNjEtMDczZS00Njg1LTg1NGEtNzkzN2QzNDU3MTYzJnZzY29kZUJyb3dzZXJSZXFJZD0xNzQ4NDc3MDc1Njk3Ijt9czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MTI1OiJodHRwOi8vMTI3LjAuMC4xOjgwMDAvb3duZXIvbm90aWZpY2F0aW9uLXByZWZlcmVuY2VzP2lkPTA4NTYyZjYxLTA3M2UtNDY4NS04NTRhLTc5MzdkMzQ1NzE2MyZ2c2NvZGVCcm93c2VyUmVxSWQ9MTc0ODQ3NzA3NTY5NyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1748477075),
('L7AUQ2IaxsLml9R8aFbxdvk2us3jcBvvneco8cjB', 4, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiVWFnZ3djc3drUnJ5TzBzbXdVZGdiS01VVXJtczNRRWt5M3Y0cVBxciI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6NDtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo1NToiaHR0cDovL2xvY2FsaG9zdC9ib29ra2VpL293bmVyL25vdGlmaWNhdGlvbi1wcmVmZXJlbmNlcyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1748478290),
('L9WLPWVXVTgVOX4MCeXInGj4mb5itAHJ3G89IaeC', 4, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiMVpFeURVcmh1Q0xpZWZ0SDdGeFF0bWhrMXNMTGp4Q1FudEQ3NFU0RiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NjE6Imh0dHA6Ly9sb2NhbGhvc3QvYm9va2tlaS9vd25lci9zZXJ2aWNlcy9lc21hbHRhZG8tbm9ybWFsL2VkaXQiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjUwOiJsb2dpbl93ZWJfNTliYTM2YWRkYzJiMmY5NDAxNTgwZjAxNGM3ZjU4ZWE0ZTMwOTg5ZCI7aTo0O30=', 1748479375),
('r6GedazOZFXolUOl1f3gjo47C65av0fUgZCXJbOM', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Code/1.100.2 Chrome/132.0.6834.210 Electron/34.5.1 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSXhjaGs4dWJIUnBtdGRvZ2xvc2xKYzNqcGI5RWJjZHY2VGNvQ04wWCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9sb2dpbiI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1748477075);

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `settings`
--

CREATE TABLE `settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `key` varchar(255) NOT NULL,
  `group` varchar(255) NOT NULL DEFAULT 'general',
  `value` text DEFAULT NULL,
  `display_name` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL DEFAULT 'text',
  `options` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `settings`
--

INSERT INTO `settings` (`id`, `key`, `group`, `value`, `display_name`, `type`, `options`, `description`, `is_public`, `order`, `created_at`, `updated_at`) VALUES
(1, 'app_name', 'general', 'BookKei', 'Application Name', 'text', NULL, 'The name of the application', 1, 1, '2025-05-23 20:58:50', '2025-05-23 20:58:50'),
(2, 'company_email', 'contact', '<EMAIL>', 'Company Email', 'text', NULL, 'Primary contact email', 1, 1, '2025-05-23 20:58:50', '2025-05-25 01:18:43'),
(3, 'company_phone', 'contact', '+1234567890', 'Company Phone', 'text', NULL, 'Primary contact phone number', 1, 2, '2025-05-23 20:58:50', '2025-05-23 20:58:50'),
(4, 'app_logo', 'appearance', 'settings/app-logo-1748022547.png', 'Application Logo', 'file', NULL, 'Logo of the application', 1, 1, '2025-05-23 20:58:50', '2025-05-23 21:49:07'),
(5, 'app_footer', 'appearance', 'Copyright © 2025 Bookkei. All rights reserved.', 'Footer Text', 'text', NULL, 'Text displayed in the footer', 1, 2, '2025-05-23 20:58:50', '2025-05-23 20:58:50'),
(6, 'mail_from_address', 'mail', '<EMAIL>', 'Mail From Address', 'text', NULL, 'Email address used for sending system emails', 0, 1, '2025-05-23 20:58:50', '2025-05-25 01:18:14'),
(7, 'mail_from_name', 'mail', 'BookKei', 'Mail From Name', 'text', NULL, 'Name used for sending system emails', 0, 2, '2025-05-23 20:58:50', '2025-05-23 20:58:50'),
(8, 'allow_registration', 'security', '1', 'Allow Registration', 'checkbox', NULL, 'Allow new users to register', 0, 1, '2025-05-23 20:58:50', '2025-05-25 01:18:24'),
(9, 'default_role', 'security', 'customer', 'Default User Role', 'select', '{\"customer\":\"Customer\",\"staff\":\"Staff\"}', 'Default role assigned to new users', 0, 2, '2025-05-23 20:58:50', '2025-05-23 20:58:50'),
(10, 'test_check_in', 'general', '15', 'Test Check In', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:13', '2025-05-24 18:37:13'),
(11, 'auto_check_in_enabled', 'general', '1', 'Auto Check In Enabled', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:40:19'),
(12, 'check_in_window_minutes', 'general', '25', 'Check In Window Minutes', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:40:39'),
(13, 'late_arrival_grace_minutes', 'general', '10', 'Late Arrival Grace Minutes', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:40:39'),
(14, 'auto_no_show_minutes', 'general', '40', 'Auto No Show Minutes', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:40:39'),
(15, 'require_check_in_confirmation', 'general', '1', 'Require Check In Confirmation', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 19:10:37'),
(16, 'send_check_in_notifications', 'general', '1', 'Send Check In Notifications', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 19:10:37'),
(17, 'allow_early_check_in', 'general', '1', 'Allow Early Check In', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:37:38'),
(18, 'early_check_in_minutes', 'general', '20', 'Early Check In Minutes', 'text', NULL, NULL, 0, 0, '2025-05-24 18:37:38', '2025-05-24 18:40:39'),
(19, 'no_show_fee_amount', 'general', '5', 'No Show Fee Amount', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:09:26'),
(20, 'no_show_fee_percentage', 'general', '15', 'No Show Fee Percentage', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:07:46'),
(21, 'no_show_blacklist_enabled', 'general', '1', 'No Show Blacklist Enabled', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 18:54:04'),
(22, 'no_show_blacklist_threshold', 'general', '5', 'No Show Blacklist Threshold', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:07:46'),
(23, 'no_show_blacklist_period_days', 'general', '60', 'No Show Blacklist Period Days', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:07:46'),
(24, 'send_no_show_notifications', 'general', '1', 'Send No Show Notifications', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:09:08'),
(25, 'no_show_follow_up_enabled', 'general', '1', 'No Show Follow Up Enabled', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:08:43'),
(26, 'no_show_follow_up_delay_hours', 'general', '48', 'No Show Follow Up Delay Hours', 'text', NULL, NULL, 0, 0, '2025-05-24 18:54:04', '2025-05-24 19:07:46'),
(27, 'no_show_fee_enabled', 'general', '1', 'No Show Fee Enabled', 'text', NULL, NULL, 0, 0, '2025-05-24 18:55:30', '2025-05-24 19:08:37'),
(28, 'email_template_booking_confirmation', 'general', 'Asunto: Confirmación de tu reserva con {business_name}\r\n\r\nHola {customer_name},\r\n\r\n¡Tu reserva ha sido confirmada exitosamente!\r\nAquí tienes los detalles:\r\n\r\nFecha: {booking_date}\r\n\r\nHora: {booking_time}\r\n\r\nServicio: {service_name}\r\n\r\nNegocio: {business_name}\r\n\r\nGracias por confiar en nosotros. Si necesitas reprogramar o cancelar, por favor hazlo con anticipación desde tu cuenta.\r\n\r\n¡Te esperamos!', 'Email Template Booking Confirmation', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:35:21'),
(29, 'email_template_booking_reminder', 'general', 'Asunto: Recordatorio de tu próxima cita con {business_name}\r\n\r\nHola {customer_name},\r\n\r\nEste es un recordatorio amable de tu próxima cita:\r\n\r\nFecha: {booking_date}\r\n\r\nHora: {booking_time}\r\n\r\nServicio: {service_name}\r\n\r\nNegocio: {business_name}\r\n\r\nTe recomendamos llegar 5 minutos antes para aprovechar al máximo tu experiencia.', 'Email Template Booking Reminder', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:36:46'),
(30, 'email_template_booking_cancellation', 'general', 'Asunto: Tu cita con {business_name} ha sido cancelada\r\n\r\nHola {customer_name},\r\n\r\nTu cita programada para:\r\n\r\n{booking_date} a las {booking_time}\r\n\r\nServicio: {service_name}\r\nha sido cancelada.\r\n\r\nSi esto fue un error o deseas volver a reservar, puedes hacerlo fácilmente desde tu cuenta.', 'Email Template Booking Cancellation', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:36:46'),
(31, 'email_template_waiting_list_notification', 'general', 'Asunto: ¡Una plaza se ha liberado en {business_name}!\r\n\r\nHola {customer_name},\r\n\r\n¡Buenas noticias! Se ha liberado una plaza para el siguiente servicio:\r\n\r\nServicio: {service_name}\r\n\r\nFecha disponible: {available_slot}\r\n\r\nNegocio: {business_name}\r\n\r\nApresúrate a reservar antes de que alguien más tome el lugar.', 'Email Template Waiting List Notification', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:36:46'),
(32, 'email_template_check_in_confirmation', 'general', 'Asunto: Registro exitoso en {business_name}\r\n\r\nHola {customer_name},\r\n\r\nGracias por confirmar tu asistencia.\r\nTu check-in para:\r\n\r\n{booking_date} a las {booking_time}\r\n\r\nServicio: {service_name}\r\nha sido registrado exitosamente en {business_name}.\r\n\r\n¡Estamos listos para atenderte!', 'Email Template Check In Confirmation', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:36:46'),
(33, 'email_template_no_show_follow_up', 'general', 'Asunto: Te extrañamos en tu cita con {business_name}\r\n\r\nHola {customer_name},\r\n\r\nNotamos que no asististe a tu cita del {booking_date} a las {booking_time} para el servicio {service_name}.\r\n\r\nEntendemos que surgen imprevistos. Si deseas reprogramar tu cita, estamos aquí para ayudarte.\r\n\r\nHaz clic aquí para agendar una nueva cita.', 'Email Template No Show Follow Up', 'text', NULL, NULL, 0, 0, '2025-05-24 19:18:28', '2025-05-25 01:36:46'),
(34, 'mail_mailer', 'general', 'smtp', 'Mail Mailer', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-26 02:33:35'),
(35, 'mail_encryption', 'general', 'ssl', 'Mail Encryption', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:12:11'),
(36, 'mail_host', 'general', 'destinoseguro.site', 'Mail Host', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:12:11'),
(37, 'mail_port', 'general', '465', 'Mail Port', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:12:11'),
(38, 'mail_username', 'general', '<EMAIL>', 'Mail Username', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:12:11'),
(39, 'mail_password', 'general', 'Keira8092613a.', 'Mail Password', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:12:11'),
(40, 'mail_enabled', 'general', '1', 'Mail Enabled', 'text', NULL, NULL, 0, 0, '2025-05-24 20:10:01', '2025-05-24 20:10:01'),
(41, 'mailgun_domain', 'general', NULL, 'Mailgun Domain', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(42, 'mailgun_secret', 'general', NULL, 'Mailgun Secret', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(43, 'mailgun_endpoint', 'general', 'api.mailgun.net', 'Mailgun Endpoint', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(44, 'aws_access_key_id', 'general', NULL, 'Aws Access Key Id', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(45, 'aws_secret_access_key', 'general', NULL, 'Aws Secret Access Key', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(46, 'aws_default_region', 'general', 'us-east-1', 'Aws Default Region', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(47, 'ses_configuration_set', 'general', NULL, 'Ses Configuration Set', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(48, 'postmark_token', 'general', NULL, 'Postmark Token', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35'),
(49, 'postmark_message_stream_id', 'general', NULL, 'Postmark Message Stream Id', 'text', NULL, NULL, 0, 0, '2025-05-26 02:33:35', '2025-05-26 02:33:35');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `two_factor_settings`
--

CREATE TABLE `two_factor_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `category` varchar(255) NOT NULL DEFAULT 'security',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `two_factor_settings`
--

INSERT INTO `two_factor_settings` (`id`, `setting_key`, `setting_value`, `description`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '2fa_enabled', 'true', 'Two-factor authentication enabled globally', 'security', 1, '2025-05-26 03:40:12', '2025-05-26 03:49:27'),
(2, '2fa_required_for_roles', 'false', 'Two-factor authentication not required for role operations', 'security', 1, '2025-05-26 03:40:12', '2025-05-26 03:49:37'),
(3, '2fa_required_for_super_admin', '1', 'Always require 2FA for Super Admin operations', 'security', 1, '2025-05-26 03:40:12', '2025-05-26 03:45:27'),
(4, '2fa_session_duration', '30', '2FA verification validity duration in minutes', 'security', 1, '2025-05-26 03:40:12', '2025-05-26 03:40:12'),
(5, '2fa_code_expiry', '10', '2FA code expiry time in minutes', 'security', 1, '2025-05-26 03:40:12', '2025-05-26 03:40:12');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT 0,
  `two_factor_secret` varchar(255) DEFAULT NULL,
  `two_factor_recovery_codes` text DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other','prefer_not_to_say') DEFAULT NULL,
  `timezone` varchar(255) DEFAULT NULL,
  `language` varchar(10) DEFAULT NULL,
  `preferences` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`preferences`)),
  `privacy_settings` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`privacy_settings`)),
  `last_login_at` timestamp NULL DEFAULT NULL,
  `last_login_ip` varchar(255) DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `two_factor_enabled`, `two_factor_secret`, `two_factor_recovery_codes`, `phone`, `profile_image`, `date_of_birth`, `gender`, `timezone`, `language`, `preferences`, `privacy_settings`, `last_login_at`, `last_login_ip`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Updated Test Name', '<EMAIL>', '2025-05-23 16:19:34', '$2y$12$Brzw13wLKgPaht1Y4NeF2OVuJu1Dp5Ha1cJW8o8.v7ogwv4SDIJ7m', 0, NULL, NULL, '+****************', NULL, '1990-01-01', 'male', 'America/New_York', 'en', NULL, NULL, '2025-05-28 17:25:20', '::1', '5SVu5AvJI2GYfq9d5rKzwhnkpx4IRepS3W2AJmiWpHqE7CkTfhKznPP1qH9b', '2025-05-23 16:19:34', '2025-05-28 17:25:20'),
(2, 'Test User', '<EMAIL>', '2025-05-23 16:19:35', '$2y$12$2.dL6V2xwOnAmleoCGEYW.cDFraeroc1dHR6oPWXfCRhdWIGs2K1.', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-26 01:22:58', '127.0.0.1', 'sA32ncGD11og69eiJAHiLHYO7mPDyFVm0Ji88z7MFmlGD8d99OnJfqHusPKC', '2025-05-23 16:19:35', '2025-05-26 01:22:58'),
(3, 'Test Admin', '<EMAIL>', '2025-05-23 18:37:54', '$2y$12$ImcSsz1TsaQf67tYw5mtAuaEhxRFHLx5oYG0uxbhdB9FlgWMmu9wG', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-28 14:38:53', '127.0.0.1', NULL, '2025-05-23 18:37:54', '2025-05-28 14:38:53'),
(4, 'Yonathan Cruz', '<EMAIL>', '2025-05-23 17:52:28', '$2y$12$5myKhdyvmUw/s5VSE9zdsud.904TU1s9vvhMBw9/0jFahXNRRGigu', 0, NULL, NULL, '8293697584', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 04:38:43', '::1', 'ey343P7lgHt2i1uhmRBBlCOqw0A20hTMSOp6mOUKpnHOTzaqn9MGlCWfvukD', '2025-05-23 21:51:15', '2025-05-29 04:38:43'),
(5, 'Alejandro Alvares', '<EMAIL>', '2025-05-25 01:34:06', '$2y$12$N.F00CUwuyhP/lzdcnSywO2TYUoZHVo26nlYg2j0GZn3MiwbAhlAi', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 03:52:23', '::1', 'vY3fk5BJc9tZo8S3RwkXyRobsZnSLvHwKAsjz10hoFxGZFUM2j46BAQjay3L', '2025-05-25 05:32:59', '2025-05-29 03:52:23'),
(6, 'Test Business Owner', '<EMAIL>', '2025-05-25 05:53:25', '$2y$12$NQdYzTvsW73VixtZw/XjjuGBkZWbqRe.M42TXDyyqgTJuEqPNwghS', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 05:43:53', '127.0.0.1', NULL, '2025-05-25 05:53:25', '2025-05-27 05:43:53'),
(12, 'Yonathan De la cruz 03-032193', '<EMAIL>', NULL, '$2y$12$v.r6XpGajmXhNXnpVlbc0OSdqNqwLgQxV5GATKtpuJ/5Qi7BwNuTm', 0, NULL, NULL, '8292856674', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-25 20:21:21', '2025-05-25 20:21:21'),
(13, 'Adriana', '<EMAIL>', NULL, '$2y$12$MGhjhuPDI7NmMGLgiou04eVUegl8pAIAcnq7Dqn3brHpWZcbQb1Wy', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-28 23:20:11', '::1', 'cmeITE7Pf9gz6pSPrTn5l1azxBSy4qbQYIrXCbgVwrZR2lFGhbEFgdanTJlJ', '2025-05-26 01:04:09', '2025-05-28 23:20:11'),
(15, 'Test User 2', '<EMAIL>', NULL, '$2y$12$Mgj/R6i0BUkv0BlnF/XeD.KwAuqjqdA9JAgPx3Clv/WOCaBF4MTLu', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-26 01:08:48', '2025-05-26 01:08:48'),
(16, 'Aradelis peralta', '<EMAIL>', NULL, '$2y$12$cnp0w0540.Ml9GJ/iGM.c.x5jAgMe0l3tut2nqLtpJJaBkJoxwaaG', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-29 00:59:05', '::1', 'dZEa1NQdazECoJnvkacOQTgmrMMLMdlhMSWPlYlIea1XURGEP5pP4sX08pBB', '2025-05-26 01:13:07', '2025-05-29 00:59:05'),
(17, 'Raul mendez', '<EMAIL>', NULL, '$2y$12$yLrDzGq7/ttZFY5MEX0lM.WF8.ib/PCvJPaJgHnESwlhjjwKIuhl2', 0, NULL, NULL, '8292854569', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 00:59:33', '2025-05-27 00:59:33'),
(18, 'Sarah Johnson', '<EMAIL>', '2025-05-27 01:37:30', '$2y$12$j2CuFjQ8IDdsftfA501Sm.HmTCNAoj3yMDPqHZCE6VueKnLGtiEYu', 0, NULL, NULL, '(555) 123-4567', NULL, '1985-03-15', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(19, 'Michael Chen', '<EMAIL>', '2025-05-27 01:37:30', '$2y$12$JyTnN.giy.nl/1FZOoy8UOHgXzADun6z04yQe0XoP/ZDyZWNNmqBO', 0, NULL, NULL, '(555) 234-5678', NULL, '1990-07-22', 'male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(20, 'Emily Rodriguez', '<EMAIL>', '2025-05-27 01:37:30', '$2y$12$AZftBZ94jgDIM4HVPEuk5uhWjjwGL1CgnpxlXfuL2EuGLSBOJdAie', 0, NULL, NULL, '(555) 345-6789', NULL, '1988-11-08', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(21, 'David Thompson', '<EMAIL>', '2025-05-27 01:37:30', '$2y$12$tAcxxpDNRf0S7mhtkb3pFeOj96ghD3DElFQyCHOSlitS905YVNOom', 0, NULL, NULL, '(555) 456-7890', NULL, '1975-05-12', 'male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(22, 'Lisa Wang', '<EMAIL>', '2025-05-27 01:37:30', '$2y$12$ouJ6lppQMQnZDmJRWLnvY.wqRUVuLxljM/YnezdKvkf5el1A4iAem', 0, NULL, NULL, '(555) 567-8901', NULL, '1992-12-03', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 01:37:30', '2025-05-27 01:37:30'),
(25, 'Test Customer', '<EMAIL>', '2025-05-27 02:16:04', '$2y$12$hCY.V.uXe4BKtXnU226P6e2VNO6CPArhS3g.xbWUzWrR1USQsR7I.', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 02:16:04', '2025-05-27 02:16:04'),
(27, 'Rosanna alvares', '<EMAIL>', '2025-05-27 02:22:45', '$2y$12$jfdvG9B73eWdcjTPE9CM1OH.RLcQYPjn.oLukMM3FwmJQqa2YSJJG', 0, NULL, NULL, '+****************', NULL, '1998-05-26', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 02:22:45', '2025-05-27 03:16:12'),
(28, 'Lucrecia gonzales', '<EMAIL>', '2025-05-27 02:24:21', '$2y$12$ZHe2eeCBsjM8m6yEaLylbOe2BVsCAe5/6eFcCZ/sxPU/VZO4AXnRq', 0, NULL, NULL, '8097892545', NULL, '1996-06-20', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 02:24:21', '2025-05-27 02:24:21'),
(29, 'Lucrecia gonzales', '<EMAIL>', '2025-05-27 03:02:26', '$2y$12$4MHeOz9svG8Hbu5OZOMM/.FPrFxl6ZdxPkmUs6ttuYQ5d5FNrng5G', 0, NULL, NULL, '8097892545', NULL, '1996-06-20', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 03:02:26', '2025-05-27 03:02:26'),
(32, 'Lucrecia gonzales', '<EMAIL>', '2025-05-27 03:06:29', '$2y$12$HdY3x20IU7eBDhwKyJOlpewH2FjFZAdBUA0tzdsR.7joNImDi/VgC', 0, NULL, NULL, '8097892545', NULL, '1996-06-20', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 03:06:29', '2025-05-27 03:06:29'),
(33, 'Lucrecia gonzales', '<EMAIL>', '2025-05-27 03:06:54', '$2y$12$/622K4DZeR8ZWwXtSAOLgusvhEdDNIjnw2ezzTxDI2mM2cqtr6a4i', 0, NULL, NULL, '8097892545', NULL, '1996-06-20', 'female', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 03:06:54', '2025-05-27 03:06:54'),
(34, 'Santiago Sanches', '<EMAIL>', '2025-05-27 03:32:30', '$2y$12$WLlsg3buo.wmiQ6f.WfggO51mBCL7D9UuK.Zyvx.IWN0UltOGevMy', 0, NULL, NULL, '(809) 789-2548', NULL, '2016-12-20', 'male', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 03:32:30', '2025-05-27 03:32:30'),
(35, 'Elizabeth Cambero', '<EMAIL>', NULL, '$2y$12$OtaZ4Wetm5TDtl2W6lZOn.cBuo.H1nMs3UMDr8EbYNLgIrSufMCoi', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-28 19:29:04', '::1', 'lGmJgG0CV1ZEDZrEE0frZAgH92nvVGVSbcqmlPGyFxFB7B2OvexnMTE8pJgD', '2025-05-27 04:21:47', '2025-05-28 19:29:04'),
(36, 'Sarah Johnson', '<EMAIL>', '2025-05-27 21:32:31', '$2y$12$FPsOTOpn66IlvRoFGJz2eO.xsF5JDn6gX3vvBaEq5WVOrM6Hu9Dzy', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 21:32:31', '2025-05-27 21:32:31'),
(38, 'Dr. Michael Chen', '<EMAIL>', '2025-05-27 21:49:14', '$2y$12$DckwSjVE35NO2SyLPYgpiOSmmNXaLusrlPO.JaX.8xeO0rXP5w0X2', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 21:49:14', '2025-05-27 21:49:14'),
(39, 'Maria Rodriguez', '<EMAIL>', '2025-05-27 21:50:51', '$2y$12$q8BmHXALxCna3yCyg37JnOLdsyAqXyEDeZ3ovA23RFIqwg/tuyJcm', 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-05-27 21:50:51', '2025-05-27 21:50:51');

-- --------------------------------------------------------

--
-- Estructura de tabla para la tabla `waiting_lists`
--

CREATE TABLE `waiting_lists` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `business_id` bigint(20) UNSIGNED NOT NULL,
  `service_id` bigint(20) UNSIGNED NOT NULL,
  `customer_id` bigint(20) UNSIGNED DEFAULT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `customer_phone` varchar(255) DEFAULT NULL,
  `preferred_date` date NOT NULL,
  `preferred_time_start` time DEFAULT NULL,
  `preferred_time_end` time DEFAULT NULL,
  `preferred_days_of_week` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`preferred_days_of_week`)),
  `participant_count` int(11) NOT NULL DEFAULT 1,
  `notes` text DEFAULT NULL,
  `status` enum('active','notified','booked','expired','cancelled') NOT NULL DEFAULT 'active',
  `notified_at` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Volcado de datos para la tabla `waiting_lists`
--

INSERT INTO `waiting_lists` (`id`, `business_id`, `service_id`, `customer_id`, `customer_name`, `customer_email`, `customer_phone`, `preferred_date`, `preferred_time_start`, `preferred_time_end`, `preferred_days_of_week`, `participant_count`, `notes`, `status`, `notified_at`, `expires_at`, `priority`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, 'Super Admin', '<EMAIL>', '+1234567890', '2025-05-25', '10:00:00', '11:00:00', NULL, 1, 'Test waiting list entry for fix verification', 'active', NULL, NULL, 1, '2025-05-24 05:33:13', '2025-05-24 05:33:13');

--
-- Índices para tablas volcadas
--

--
-- Indices de la tabla `availability_blocks`
--
ALTER TABLE `availability_blocks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `availability_blocks_business_branch_id_foreign` (`business_branch_id`),
  ADD KEY `availability_business_time_idx` (`business_id`,`start_datetime`,`end_datetime`),
  ADD KEY `availability_resource_time_idx` (`resource_id`,`start_datetime`,`end_datetime`);

--
-- Indices de la tabla `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `bookings_booking_number_unique` (`booking_number`),
  ADD KEY `bookings_business_branch_id_foreign` (`business_branch_id`),
  ADD KEY `bookings_business_id_start_datetime_index` (`business_id`,`start_datetime`),
  ADD KEY `bookings_business_id_status_index` (`business_id`,`status`),
  ADD KEY `bookings_customer_id_start_datetime_index` (`customer_id`,`start_datetime`),
  ADD KEY `bookings_booking_number_index` (`booking_number`),
  ADD KEY `bookings_recurring_group_id_index` (`recurring_group_id`);

--
-- Indices de la tabla `booking_payments`
--
ALTER TABLE `booking_payments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_payments_payment_reference_unique` (`payment_reference`),
  ADD KEY `booking_payments_booking_id_payment_type_index` (`booking_id`,`payment_type`),
  ADD KEY `booking_payments_payment_reference_index` (`payment_reference`),
  ADD KEY `booking_payments_status_processed_at_index` (`status`,`processed_at`);

--
-- Indices de la tabla `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_reminders_booking_id_reminder_type_index` (`booking_id`,`reminder_type`),
  ADD KEY `booking_reminders_scheduled_at_status_index` (`scheduled_at`,`status`);

--
-- Indices de la tabla `booking_services`
--
ALTER TABLE `booking_services`
  ADD PRIMARY KEY (`id`),
  ADD KEY `booking_services_booking_id_service_id_index` (`booking_id`,`service_id`),
  ADD KEY `booking_services_service_id_start_datetime_index` (`service_id`,`start_datetime`);

--
-- Indices de la tabla `booking_service_resources`
--
ALTER TABLE `booking_service_resources`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `booking_service_resource_unique` (`booking_service_id`,`resource_id`),
  ADD KEY `booking_resource_time_idx` (`resource_id`,`start_datetime`,`end_datetime`);

--
-- Indices de la tabla `businesses`
--
ALTER TABLE `businesses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `businesses_slug_unique` (`slug`),
  ADD UNIQUE KEY `businesses_landing_page_slug_unique` (`landing_page_slug`),
  ADD KEY `businesses_owner_id_foreign` (`owner_id`),
  ADD KEY `businesses_slug_is_active_index` (`slug`,`is_active`),
  ADD KEY `businesses_landing_page_slug_landing_page_status_index` (`landing_page_slug`,`landing_page_status`),
  ADD KEY `businesses_domain_type_landing_page_enabled_index` (`domain_type`,`landing_page_enabled`),
  ADD KEY `businesses_owner_id_landing_page_enabled_index` (`owner_id`,`landing_page_enabled`);

--
-- Indices de la tabla `business_branches`
--
ALTER TABLE `business_branches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_branches_business_id_slug_unique` (`business_id`,`slug`),
  ADD KEY `business_branches_business_id_is_active_index` (`business_id`,`is_active`);

--
-- Indices de la tabla `business_categories`
--
ALTER TABLE `business_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_categories_slug_unique` (`slug`),
  ADD KEY `business_categories_slug_is_active_index` (`slug`,`is_active`);

--
-- Indices de la tabla `business_category_assignments`
--
ALTER TABLE `business_category_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_category_unique` (`business_id`,`business_category_id`),
  ADD KEY `business_category_assignments_business_category_id_foreign` (`business_category_id`);

--
-- Indices de la tabla `business_gallery_categories`
--
ALTER TABLE `business_gallery_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `business_gallery_categories_business_id_foreign` (`business_id`);

--
-- Indices de la tabla `business_gallery_images`
--
ALTER TABLE `business_gallery_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `business_gallery_images_business_id_foreign` (`business_id`);

--
-- Indices de la tabla `business_holidays`
--
ALTER TABLE `business_holidays`
  ADD PRIMARY KEY (`id`),
  ADD KEY `business_holidays_business_branch_id_foreign` (`business_branch_id`),
  ADD KEY `business_holidays_business_id_start_date_end_date_index` (`business_id`,`start_date`,`end_date`),
  ADD KEY `business_holidays_business_id_is_active_index` (`business_id`,`is_active`);

--
-- Indices de la tabla `business_landing_pages`
--
ALTER TABLE `business_landing_pages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_landing_pages_custom_slug_unique` (`custom_slug`),
  ADD UNIQUE KEY `business_landing_pages_custom_domain_unique` (`custom_domain`),
  ADD KEY `business_landing_pages_business_id_is_published_index` (`business_id`,`is_published`),
  ADD KEY `business_landing_pages_custom_slug_is_published_index` (`custom_slug`,`is_published`),
  ADD KEY `business_landing_pages_domain_type_is_published_index` (`domain_type`,`is_published`);

--
-- Indices de la tabla `business_landing_page_sections`
--
ALTER TABLE `business_landing_page_sections`
  ADD PRIMARY KEY (`id`),
  ADD KEY `blp_sections_page_type_idx` (`business_landing_page_id`,`section_type`),
  ADD KEY `blp_sections_page_visible_order_idx` (`business_landing_page_id`,`is_visible`,`sort_order`);

--
-- Indices de la tabla `business_operating_hours`
--
ALTER TABLE `business_operating_hours`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_hours_unique` (`business_id`,`business_branch_id`,`day_of_week`),
  ADD KEY `business_operating_hours_business_branch_id_foreign` (`business_branch_id`),
  ADD KEY `business_operating_hours_business_id_day_of_week_index` (`business_id`,`day_of_week`);

--
-- Indices de la tabla `business_seo_settings`
--
ALTER TABLE `business_seo_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_seo_settings_business_id_unique` (`business_id`);

--
-- Indices de la tabla `business_tags`
--
ALTER TABLE `business_tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_tags_slug_unique` (`slug`);

--
-- Indices de la tabla `business_tag_assignments`
--
ALTER TABLE `business_tag_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `business_tag_unique` (`business_id`,`business_tag_id`),
  ADD KEY `business_tag_assignments_business_tag_id_foreign` (`business_tag_id`);

--
-- Indices de la tabla `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indices de la tabla `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indices de la tabla `compliance_reports`
--
ALTER TABLE `compliance_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `compliance_reports_report_type_index` (`report_type`),
  ADD KEY `compliance_reports_report_format_index` (`report_format`),
  ADD KEY `compliance_reports_period_start_index` (`period_start`),
  ADD KEY `compliance_reports_period_end_index` (`period_end`),
  ADD KEY `compliance_reports_generated_by_index` (`generated_by`),
  ADD KEY `compliance_reports_generated_at_index` (`generated_at`),
  ADD KEY `compliance_reports_report_type_generated_at_index` (`report_type`,`generated_at`),
  ADD KEY `compliance_reports_period_start_period_end_index` (`period_start`,`period_end`);

--
-- Indices de la tabla `customer_activity_timeline`
--
ALTER TABLE `customer_activity_timeline`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_activity_timeline_business_id_foreign` (`business_id`),
  ADD KEY `customer_activity_timeline_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_activity_timeline_created_by_foreign` (`created_by`);

--
-- Indices de la tabla `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_addresses_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_addresses_business_id_customer_id_index` (`business_id`,`customer_id`),
  ADD KEY `customer_addresses_business_id_type_index` (`business_id`,`type`),
  ADD KEY `customer_addresses_business_id_is_primary_index` (`business_id`,`is_primary`);

--
-- Indices de la tabla `customer_business_profiles`
--
ALTER TABLE `customer_business_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customer_business_profiles_business_id_customer_id_unique` (`business_id`,`customer_id`),
  ADD KEY `customer_business_profiles_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_business_profiles_business_id_status_index` (`business_id`,`status`),
  ADD KEY `customer_business_profiles_business_id_loyalty_tier_index` (`business_id`,`loyalty_tier`),
  ADD KEY `customer_business_profiles_business_id_total_spent_index` (`business_id`,`total_spent`),
  ADD KEY `customer_business_profiles_business_id_last_visit_date_index` (`business_id`,`last_visit_date`),
  ADD KEY `customer_business_profiles_customer_since_index` (`customer_since`);

--
-- Indices de la tabla `customer_communications`
--
ALTER TABLE `customer_communications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_communications_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_communications_sent_by_foreign` (`sent_by`),
  ADD KEY `customer_communications_business_id_customer_id_created_at_index` (`business_id`,`customer_id`,`created_at`),
  ADD KEY `customer_communications_business_id_type_status_index` (`business_id`,`type`,`status`),
  ADD KEY `customer_communications_campaign_id_index` (`campaign_id`),
  ADD KEY `customer_communications_sent_at_index` (`sent_at`);

--
-- Indices de la tabla `customer_emergency_contacts`
--
ALTER TABLE `customer_emergency_contacts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_emergency_contacts_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_emergency_contacts_business_id_customer_id_index` (`business_id`,`customer_id`),
  ADD KEY `customer_emergency_contacts_business_id_is_primary_index` (`business_id`,`is_primary`);

--
-- Indices de la tabla `customer_favorites`
--
ALTER TABLE `customer_favorites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customer_favorites_user_id_service_id_unique` (`user_id`,`service_id`),
  ADD KEY `customer_favorites_user_id_created_at_index` (`user_id`,`created_at`),
  ADD KEY `customer_favorites_service_id_index` (`service_id`);

--
-- Indices de la tabla `customer_loyalty_points`
--
ALTER TABLE `customer_loyalty_points`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_loyalty_points_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_loyalty_points_processed_by_foreign` (`processed_by`),
  ADD KEY `customer_loyalty_points_business_id_customer_id_created_at_index` (`business_id`,`customer_id`,`created_at`),
  ADD KEY `customer_loyalty_points_business_id_customer_id_type_index` (`business_id`,`customer_id`,`type`),
  ADD KEY `customer_loyalty_points_expires_at_is_expired_index` (`expires_at`,`is_expired`),
  ADD KEY `customer_loyalty_points_reference_type_reference_id_index` (`reference_type`,`reference_id`);

--
-- Indices de la tabla `customer_referrals`
--
ALTER TABLE `customer_referrals`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_referrals_referrer_id_foreign` (`referrer_id`),
  ADD KEY `customer_referrals_referred_id_foreign` (`referred_id`),
  ADD KEY `customer_referrals_business_id_referrer_id_index` (`business_id`,`referrer_id`),
  ADD KEY `customer_referrals_business_id_status_index` (`business_id`,`status`),
  ADD KEY `customer_referrals_business_id_referred_at_index` (`business_id`,`referred_at`);

--
-- Indices de la tabla `customer_tags`
--
ALTER TABLE `customer_tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customer_tags_business_id_name_unique` (`business_id`,`name`),
  ADD KEY `customer_tags_business_id_is_active_index` (`business_id`,`is_active`);

--
-- Indices de la tabla `customer_tag_assignments`
--
ALTER TABLE `customer_tag_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_customer_tag_assignment` (`business_id`,`customer_id`,`customer_tag_id`),
  ADD KEY `customer_tag_assignments_customer_id_foreign` (`customer_id`),
  ADD KEY `customer_tag_assignments_assigned_by_foreign` (`assigned_by`),
  ADD KEY `customer_tag_assignments_business_id_customer_id_index` (`business_id`,`customer_id`),
  ADD KEY `customer_tag_assignments_customer_tag_id_assigned_at_index` (`customer_tag_id`,`assigned_at`);

--
-- Indices de la tabla `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indices de la tabla `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indices de la tabla `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `landing_page_analytics`
--
ALTER TABLE `landing_page_analytics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `landing_page_analytics_landing_page_id_foreign` (`landing_page_id`),
  ADD KEY `landing_page_analytics_service_id_foreign` (`service_id`),
  ADD KEY `landing_page_analytics_booking_id_foreign` (`booking_id`),
  ADD KEY `landing_page_analytics_business_id_event_type_created_at_index` (`business_id`,`event_type`,`created_at`),
  ADD KEY `landing_page_analytics_business_id_service_id_created_at_index` (`business_id`,`service_id`,`created_at`),
  ADD KEY `landing_page_analytics_session_id_created_at_index` (`session_id`,`created_at`),
  ADD KEY `landing_page_analytics_created_at_index` (`created_at`);

--
-- Indices de la tabla `landing_service_settings`
--
ALTER TABLE `landing_service_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `landing_service_settings_business_id_layout_type_index` (`business_id`,`layout_type`),
  ADD KEY `landing_service_settings_business_id_enable_quick_booking_index` (`business_id`,`enable_quick_booking`);

--
-- Indices de la tabla `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indices de la tabla `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`model_id`,`model_type`),
  ADD KEY `model_has_permissions_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indices de la tabla `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD PRIMARY KEY (`role_id`,`model_id`,`model_type`),
  ADD KEY `model_has_roles_model_id_model_type_index` (`model_id`,`model_type`);

--
-- Indices de la tabla `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Indices de la tabla `owner_notifications`
--
ALTER TABLE `owner_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `owner_notifications_owner_id_foreign` (`owner_id`),
  ADD KEY `owner_notifications_business_id_owner_id_index` (`business_id`,`owner_id`),
  ADD KEY `owner_notifications_notification_type_priority_index` (`notification_type`,`priority`),
  ADD KEY `owner_notifications_is_read_is_deleted_index` (`is_read`,`is_deleted`),
  ADD KEY `owner_notifications_created_at_priority_index` (`created_at`,`priority`),
  ADD KEY `owner_notifications_source_type_source_id_index` (`source_type`,`source_id`);

--
-- Indices de la tabla `owner_notification_preferences`
--
ALTER TABLE `owner_notification_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `owner_notif_prefs_unique` (`owner_id`,`business_id`,`notification_type`),
  ADD KEY `owner_notification_preferences_business_id_foreign` (`business_id`),
  ADD KEY `owner_notification_preferences_owner_id_business_id_index` (`owner_id`,`business_id`),
  ADD KEY `owner_notification_preferences_notification_type_index` (`notification_type`),
  ADD KEY `owner_notif_prefs_owner_business_idx` (`owner_id`,`business_id`),
  ADD KEY `owner_notif_prefs_type_idx` (`notification_type`);

--
-- Indices de la tabla `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indices de la tabla `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `permissions_name_guard_name_unique` (`name`,`guard_name`);

--
-- Indices de la tabla `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indices de la tabla `resources`
--
ALTER TABLE `resources`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resources_business_id_slug_unique` (`business_id`,`slug`),
  ADD KEY `resources_business_branch_id_foreign` (`business_branch_id`),
  ADD KEY `resources_resource_type_id_foreign` (`resource_type_id`),
  ADD KEY `resources_business_id_resource_type_id_is_active_index` (`business_id`,`resource_type_id`,`is_active`);

--
-- Indices de la tabla `resource_types`
--
ALTER TABLE `resource_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `resource_types_business_id_slug_unique` (`business_id`,`slug`),
  ADD KEY `resource_types_business_id_name_index` (`business_id`,`name`);

--
-- Indices de la tabla `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_guard_name_unique` (`name`,`guard_name`),
  ADD KEY `roles_hierarchy_level_index` (`hierarchy_level`),
  ADD KEY `roles_security_level_index` (`security_level`),
  ADD KEY `roles_is_system_role_index` (`is_system_role`),
  ADD KEY `roles_created_by_foreign` (`created_by`),
  ADD KEY `roles_updated_by_foreign` (`updated_by`);

--
-- Indices de la tabla `role_audit_logs`
--
ALTER TABLE `role_audit_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `role_audit_logs_user_id_index` (`user_id`),
  ADD KEY `role_audit_logs_action_index` (`action`),
  ADD KEY `role_audit_logs_target_type_index` (`target_type`),
  ADD KEY `role_audit_logs_target_id_index` (`target_id`),
  ADD KEY `role_audit_logs_risk_level_index` (`risk_level`),
  ADD KEY `role_audit_logs_created_at_index` (`created_at`),
  ADD KEY `role_audit_logs_target_type_target_id_index` (`target_type`,`target_id`),
  ADD KEY `role_audit_logs_user_id_created_at_index` (`user_id`,`created_at`),
  ADD KEY `role_audit_logs_action_created_at_index` (`action`,`created_at`),
  ADD KEY `role_audit_logs_archived_at_index` (`archived_at`);

--
-- Indices de la tabla `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD PRIMARY KEY (`permission_id`,`role_id`),
  ADD KEY `role_has_permissions_role_id_foreign` (`role_id`);

--
-- Indices de la tabla `security_alerts`
--
ALTER TABLE `security_alerts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `security_alerts_type_index` (`type`),
  ADD KEY `security_alerts_severity_index` (`severity`),
  ADD KEY `security_alerts_status_index` (`status`),
  ADD KEY `security_alerts_user_id_index` (`user_id`),
  ADD KEY `security_alerts_ip_address_index` (`ip_address`),
  ADD KEY `security_alerts_timestamp_index` (`timestamp`),
  ADD KEY `security_alerts_created_at_index` (`created_at`),
  ADD KEY `security_alerts_type_severity_index` (`type`,`severity`),
  ADD KEY `security_alerts_status_severity_index` (`status`,`severity`),
  ADD KEY `security_alerts_user_id_created_at_index` (`user_id`,`created_at`),
  ADD KEY `security_alerts_resolved_by_foreign` (`resolved_by`),
  ADD KEY `security_alerts_archived_at_index` (`archived_at`);

--
-- Indices de la tabla `security_logs`
--
ALTER TABLE `security_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `security_logs_level_index` (`level`),
  ADD KEY `security_logs_category_index` (`category`),
  ADD KEY `security_logs_event_type_index` (`event_type`),
  ADD KEY `security_logs_user_id_index` (`user_id`),
  ADD KEY `security_logs_ip_address_index` (`ip_address`),
  ADD KEY `security_logs_event_timestamp_index` (`event_timestamp`),
  ADD KEY `security_logs_created_at_index` (`created_at`),
  ADD KEY `security_logs_level_category_index` (`level`,`category`),
  ADD KEY `security_logs_user_id_event_timestamp_index` (`user_id`,`event_timestamp`);

--
-- Indices de la tabla `security_metrics`
--
ALTER TABLE `security_metrics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `security_metrics_metric_name_index` (`metric_name`),
  ADD KEY `security_metrics_metric_type_index` (`metric_type`),
  ADD KEY `security_metrics_category_index` (`category`),
  ADD KEY `security_metrics_recorded_at_index` (`recorded_at`),
  ADD KEY `security_metrics_metric_name_recorded_at_index` (`metric_name`,`recorded_at`),
  ADD KEY `security_metrics_category_recorded_at_index` (`category`,`recorded_at`);

--
-- Indices de la tabla `services`
--
ALTER TABLE `services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `services_business_id_slug_unique` (`business_id`,`slug`),
  ADD KEY `services_service_category_id_foreign` (`service_category_id`),
  ADD KEY `services_business_id_service_category_id_is_active_index` (`business_id`,`service_category_id`,`is_active`),
  ADD KEY `services_business_id_is_public_is_active_index` (`business_id`,`is_public`,`is_active`);

--
-- Indices de la tabla `service_addons`
--
ALTER TABLE `service_addons`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_addons_service_id_addon_service_id_unique` (`service_id`,`addon_service_id`),
  ADD KEY `service_addons_addon_service_id_foreign` (`addon_service_id`);

--
-- Indices de la tabla `service_availability`
--
ALTER TABLE `service_availability`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_availability_service_id_date_start_time_unique` (`service_id`,`date`,`start_time`),
  ADD KEY `service_availability_service_id_date_is_available_index` (`service_id`,`date`,`is_available`),
  ADD KEY `service_availability_business_id_date_index` (`business_id`,`date`),
  ADD KEY `service_availability_date_start_time_end_time_index` (`date`,`start_time`,`end_time`);

--
-- Indices de la tabla `service_categories`
--
ALTER TABLE `service_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_categories_business_id_slug_unique` (`business_id`,`slug`);

--
-- Indices de la tabla `service_images`
--
ALTER TABLE `service_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `service_images_service_id_is_active_sort_order_index` (`service_id`,`is_active`,`sort_order`),
  ADD KEY `service_images_service_id_is_primary_index` (`service_id`,`is_primary`),
  ADD KEY `service_images_service_id_is_featured_index` (`service_id`,`is_featured`),
  ADD KEY `service_images_uploaded_at_index` (`uploaded_at`);

--
-- Indices de la tabla `service_resources`
--
ALTER TABLE `service_resources`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_resources_service_id_resource_id_unique` (`service_id`,`resource_id`),
  ADD KEY `service_resources_resource_id_foreign` (`resource_id`);

--
-- Indices de la tabla `service_reviews`
--
ALTER TABLE `service_reviews`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `service_reviews_user_id_service_id_booking_id_unique` (`user_id`,`service_id`,`booking_id`),
  ADD KEY `service_reviews_booking_id_foreign` (`booking_id`),
  ADD KEY `service_reviews_service_id_is_approved_rating_index` (`service_id`,`is_approved`,`rating`),
  ADD KEY `service_reviews_user_id_reviewed_at_index` (`user_id`,`reviewed_at`),
  ADD KEY `service_reviews_is_featured_rating_index` (`is_featured`,`rating`);

--
-- Indices de la tabla `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indices de la tabla `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `settings_key_unique` (`key`);

--
-- Indices de la tabla `two_factor_settings`
--
ALTER TABLE `two_factor_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `two_factor_settings_setting_key_unique` (`setting_key`);

--
-- Indices de la tabla `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indices de la tabla `waiting_lists`
--
ALTER TABLE `waiting_lists`
  ADD PRIMARY KEY (`id`),
  ADD KEY `waiting_lists_service_id_foreign` (`service_id`),
  ADD KEY `waiting_lists_business_id_service_id_status_index` (`business_id`,`service_id`,`status`),
  ADD KEY `waiting_lists_customer_id_status_index` (`customer_id`,`status`),
  ADD KEY `waiting_lists_preferred_date_status_index` (`preferred_date`,`status`);

--
-- AUTO_INCREMENT de las tablas volcadas
--

--
-- AUTO_INCREMENT de la tabla `availability_blocks`
--
ALTER TABLE `availability_blocks`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT de la tabla `booking_payments`
--
ALTER TABLE `booking_payments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `booking_reminders`
--
ALTER TABLE `booking_reminders`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `booking_services`
--
ALTER TABLE `booking_services`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT de la tabla `booking_service_resources`
--
ALTER TABLE `booking_service_resources`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `businesses`
--
ALTER TABLE `businesses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT de la tabla `business_branches`
--
ALTER TABLE `business_branches`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT de la tabla `business_categories`
--
ALTER TABLE `business_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT de la tabla `business_category_assignments`
--
ALTER TABLE `business_category_assignments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `business_gallery_categories`
--
ALTER TABLE `business_gallery_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT de la tabla `business_gallery_images`
--
ALTER TABLE `business_gallery_images`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT de la tabla `business_holidays`
--
ALTER TABLE `business_holidays`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT de la tabla `business_landing_pages`
--
ALTER TABLE `business_landing_pages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `business_landing_page_sections`
--
ALTER TABLE `business_landing_page_sections`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT de la tabla `business_operating_hours`
--
ALTER TABLE `business_operating_hours`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT de la tabla `business_seo_settings`
--
ALTER TABLE `business_seo_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `business_tags`
--
ALTER TABLE `business_tags`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `business_tag_assignments`
--
ALTER TABLE `business_tag_assignments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `compliance_reports`
--
ALTER TABLE `compliance_reports`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_activity_timeline`
--
ALTER TABLE `customer_activity_timeline`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_addresses`
--
ALTER TABLE `customer_addresses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_business_profiles`
--
ALTER TABLE `customer_business_profiles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT de la tabla `customer_communications`
--
ALTER TABLE `customer_communications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `customer_emergency_contacts`
--
ALTER TABLE `customer_emergency_contacts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_favorites`
--
ALTER TABLE `customer_favorites`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_loyalty_points`
--
ALTER TABLE `customer_loyalty_points`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT de la tabla `customer_referrals`
--
ALTER TABLE `customer_referrals`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `customer_tags`
--
ALTER TABLE `customer_tags`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT de la tabla `customer_tag_assignments`
--
ALTER TABLE `customer_tag_assignments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT de la tabla `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT de la tabla `landing_page_analytics`
--
ALTER TABLE `landing_page_analytics`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `landing_service_settings`
--
ALTER TABLE `landing_service_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT de la tabla `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=61;

--
-- AUTO_INCREMENT de la tabla `owner_notifications`
--
ALTER TABLE `owner_notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=36;

--
-- AUTO_INCREMENT de la tabla `owner_notification_preferences`
--
ALTER TABLE `owner_notification_preferences`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT de la tabla `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=134;

--
-- AUTO_INCREMENT de la tabla `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `resources`
--
ALTER TABLE `resources`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT de la tabla `resource_types`
--
ALTER TABLE `resource_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT de la tabla `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT de la tabla `role_audit_logs`
--
ALTER TABLE `role_audit_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT de la tabla `security_alerts`
--
ALTER TABLE `security_alerts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- AUTO_INCREMENT de la tabla `security_logs`
--
ALTER TABLE `security_logs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `security_metrics`
--
ALTER TABLE `security_metrics`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `services`
--
ALTER TABLE `services`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT de la tabla `service_addons`
--
ALTER TABLE `service_addons`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `service_availability`
--
ALTER TABLE `service_availability`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `service_categories`
--
ALTER TABLE `service_categories`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT de la tabla `service_images`
--
ALTER TABLE `service_images`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `service_resources`
--
ALTER TABLE `service_resources`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `service_reviews`
--
ALTER TABLE `service_reviews`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de la tabla `settings`
--
ALTER TABLE `settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT de la tabla `two_factor_settings`
--
ALTER TABLE `two_factor_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT de la tabla `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- AUTO_INCREMENT de la tabla `waiting_lists`
--
ALTER TABLE `waiting_lists`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- Restricciones para tablas volcadas
--

--
-- Filtros para la tabla `availability_blocks`
--
ALTER TABLE `availability_blocks`
  ADD CONSTRAINT `availability_blocks_business_branch_id_foreign` FOREIGN KEY (`business_branch_id`) REFERENCES `business_branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `availability_blocks_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `availability_blocks_resource_id_foreign` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_business_branch_id_foreign` FOREIGN KEY (`business_branch_id`) REFERENCES `business_branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `bookings_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `booking_payments`
--
ALTER TABLE `booking_payments`
  ADD CONSTRAINT `booking_payments_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `booking_reminders`
--
ALTER TABLE `booking_reminders`
  ADD CONSTRAINT `booking_reminders_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `booking_services`
--
ALTER TABLE `booking_services`
  ADD CONSTRAINT `booking_services_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_services_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `booking_service_resources`
--
ALTER TABLE `booking_service_resources`
  ADD CONSTRAINT `booking_service_resources_booking_service_id_foreign` FOREIGN KEY (`booking_service_id`) REFERENCES `booking_services` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `booking_service_resources_resource_id_foreign` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `businesses`
--
ALTER TABLE `businesses`
  ADD CONSTRAINT `businesses_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_branches`
--
ALTER TABLE `business_branches`
  ADD CONSTRAINT `business_branches_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_category_assignments`
--
ALTER TABLE `business_category_assignments`
  ADD CONSTRAINT `business_category_assignments_business_category_id_foreign` FOREIGN KEY (`business_category_id`) REFERENCES `business_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `business_category_assignments_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_gallery_categories`
--
ALTER TABLE `business_gallery_categories`
  ADD CONSTRAINT `business_gallery_categories_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_gallery_images`
--
ALTER TABLE `business_gallery_images`
  ADD CONSTRAINT `business_gallery_images_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_holidays`
--
ALTER TABLE `business_holidays`
  ADD CONSTRAINT `business_holidays_business_branch_id_foreign` FOREIGN KEY (`business_branch_id`) REFERENCES `business_branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `business_holidays_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_landing_pages`
--
ALTER TABLE `business_landing_pages`
  ADD CONSTRAINT `business_landing_pages_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_landing_page_sections`
--
ALTER TABLE `business_landing_page_sections`
  ADD CONSTRAINT `business_landing_page_sections_business_landing_page_id_foreign` FOREIGN KEY (`business_landing_page_id`) REFERENCES `business_landing_pages` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_operating_hours`
--
ALTER TABLE `business_operating_hours`
  ADD CONSTRAINT `business_operating_hours_business_branch_id_foreign` FOREIGN KEY (`business_branch_id`) REFERENCES `business_branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `business_operating_hours_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_seo_settings`
--
ALTER TABLE `business_seo_settings`
  ADD CONSTRAINT `business_seo_settings_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `business_tag_assignments`
--
ALTER TABLE `business_tag_assignments`
  ADD CONSTRAINT `business_tag_assignments_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `business_tag_assignments_business_tag_id_foreign` FOREIGN KEY (`business_tag_id`) REFERENCES `business_tags` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `compliance_reports`
--
ALTER TABLE `compliance_reports`
  ADD CONSTRAINT `compliance_reports_generated_by_foreign` FOREIGN KEY (`generated_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_activity_timeline`
--
ALTER TABLE `customer_activity_timeline`
  ADD CONSTRAINT `customer_activity_timeline_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_activity_timeline_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `customer_activity_timeline_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_addresses`
--
ALTER TABLE `customer_addresses`
  ADD CONSTRAINT `customer_addresses_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_addresses_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_business_profiles`
--
ALTER TABLE `customer_business_profiles`
  ADD CONSTRAINT `customer_business_profiles_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_business_profiles_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_communications`
--
ALTER TABLE `customer_communications`
  ADD CONSTRAINT `customer_communications_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_communications_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_communications_sent_by_foreign` FOREIGN KEY (`sent_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `customer_emergency_contacts`
--
ALTER TABLE `customer_emergency_contacts`
  ADD CONSTRAINT `customer_emergency_contacts_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_emergency_contacts_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_favorites`
--
ALTER TABLE `customer_favorites`
  ADD CONSTRAINT `customer_favorites_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_favorites_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_loyalty_points`
--
ALTER TABLE `customer_loyalty_points`
  ADD CONSTRAINT `customer_loyalty_points_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_loyalty_points_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_loyalty_points_processed_by_foreign` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `customer_referrals`
--
ALTER TABLE `customer_referrals`
  ADD CONSTRAINT `customer_referrals_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_referrals_referred_id_foreign` FOREIGN KEY (`referred_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `customer_referrals_referrer_id_foreign` FOREIGN KEY (`referrer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_tags`
--
ALTER TABLE `customer_tags`
  ADD CONSTRAINT `customer_tags_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `customer_tag_assignments`
--
ALTER TABLE `customer_tag_assignments`
  ADD CONSTRAINT `customer_tag_assignments_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `customer_tag_assignments_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_tag_assignments_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `customer_tag_assignments_customer_tag_id_foreign` FOREIGN KEY (`customer_tag_id`) REFERENCES `customer_tags` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `landing_page_analytics`
--
ALTER TABLE `landing_page_analytics`
  ADD CONSTRAINT `landing_page_analytics_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `landing_page_analytics_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `landing_page_analytics_landing_page_id_foreign` FOREIGN KEY (`landing_page_id`) REFERENCES `business_landing_pages` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `landing_page_analytics_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `landing_service_settings`
--
ALTER TABLE `landing_service_settings`
  ADD CONSTRAINT `landing_service_settings_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `model_has_permissions`
--
ALTER TABLE `model_has_permissions`
  ADD CONSTRAINT `model_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `model_has_roles`
--
ALTER TABLE `model_has_roles`
  ADD CONSTRAINT `model_has_roles_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `owner_notifications`
--
ALTER TABLE `owner_notifications`
  ADD CONSTRAINT `owner_notifications_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `owner_notifications_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `owner_notification_preferences`
--
ALTER TABLE `owner_notification_preferences`
  ADD CONSTRAINT `owner_notification_preferences_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `owner_notification_preferences_owner_id_foreign` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `resources`
--
ALTER TABLE `resources`
  ADD CONSTRAINT `resources_business_branch_id_foreign` FOREIGN KEY (`business_branch_id`) REFERENCES `business_branches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `resources_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `resources_resource_type_id_foreign` FOREIGN KEY (`resource_type_id`) REFERENCES `resource_types` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `resource_types`
--
ALTER TABLE `resource_types`
  ADD CONSTRAINT `resource_types_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `roles`
--
ALTER TABLE `roles`
  ADD CONSTRAINT `roles_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `roles_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `role_audit_logs`
--
ALTER TABLE `role_audit_logs`
  ADD CONSTRAINT `role_audit_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `role_has_permissions`
--
ALTER TABLE `role_has_permissions`
  ADD CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `security_alerts`
--
ALTER TABLE `security_alerts`
  ADD CONSTRAINT `security_alerts_resolved_by_foreign` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `security_alerts_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `security_logs`
--
ALTER TABLE `security_logs`
  ADD CONSTRAINT `security_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `services`
--
ALTER TABLE `services`
  ADD CONSTRAINT `services_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `services_service_category_id_foreign` FOREIGN KEY (`service_category_id`) REFERENCES `service_categories` (`id`) ON DELETE SET NULL;

--
-- Filtros para la tabla `service_addons`
--
ALTER TABLE `service_addons`
  ADD CONSTRAINT `service_addons_addon_service_id_foreign` FOREIGN KEY (`addon_service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `service_addons_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `service_availability`
--
ALTER TABLE `service_availability`
  ADD CONSTRAINT `service_availability_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `service_availability_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `service_categories`
--
ALTER TABLE `service_categories`
  ADD CONSTRAINT `service_categories_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `service_images`
--
ALTER TABLE `service_images`
  ADD CONSTRAINT `service_images_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `service_resources`
--
ALTER TABLE `service_resources`
  ADD CONSTRAINT `service_resources_resource_id_foreign` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `service_resources_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `service_reviews`
--
ALTER TABLE `service_reviews`
  ADD CONSTRAINT `service_reviews_booking_id_foreign` FOREIGN KEY (`booking_id`) REFERENCES `bookings` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `service_reviews_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `service_reviews_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Filtros para la tabla `waiting_lists`
--
ALTER TABLE `waiting_lists`
  ADD CONSTRAINT `waiting_lists_business_id_foreign` FOREIGN KEY (`business_id`) REFERENCES `businesses` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `waiting_lists_customer_id_foreign` FOREIGN KEY (`customer_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `waiting_lists_service_id_foreign` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
