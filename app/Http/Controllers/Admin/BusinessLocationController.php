<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use App\Models\BusinessBranch;
use Illuminate\Http\Request;

class BusinessLocationController extends Controller
{
    // Note: Middleware is applied at the route level in routes/web.php

    /**
     * Display business locations.
     */
    public function index(Business $business)
    {
        $branches = $business->branches()->with('operatingHours')->get();

        return view('admin.businesses.locations.index', compact('business', 'branches'));
    }

    /**
     * Show the form for creating a new location.
     */
    public function create(Business $business)
    {
        return view('admin.businesses.locations.create', compact('business'));
    }

    /**
     * Store a newly created location.
     */
    public function store(Request $request, Business $business)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string',
            'is_main' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string',
            'parking_info' => 'nullable|string',
            'public_transport_info' => 'nullable|string',
        ]);

        // Set defaults
        $validated['business_id'] = $business->id;
        $validated['is_main'] = $validated['is_main'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;

        // If this is set as main branch, unset other main branches
        if ($validated['is_main']) {
            BusinessBranch::where('business_id', $business->id)
                ->where('is_main', true)
                ->update(['is_main' => false]);
        }

        // If no coordinates provided, try to geocode the address
        if (empty($validated['latitude']) || empty($validated['longitude'])) {
            $coordinates = $this->geocodeAddress($validated);
            if ($coordinates) {
                $validated['latitude'] = $coordinates['lat'];
                $validated['longitude'] = $coordinates['lng'];
            }
        }

        $branch = BusinessBranch::create($validated);

        return redirect()->route('admin.businesses.locations.show', [$business, $branch])
                        ->with('success', 'Location created successfully.');
    }

    /**
     * Display the specified location.
     */
    public function show(Business $business, BusinessBranch $branch)
    {
        $branch->load(['operatingHours', 'resources', 'holidays']);

        return view('admin.businesses.locations.show', compact('business', 'branch'));
    }

    /**
     * Show the form for editing the specified location.
     */
    public function edit(Business $business, BusinessBranch $branch)
    {
        return view('admin.businesses.locations.edit', compact('business', 'branch'));
    }

    /**
     * Update the specified location.
     */
    public function update(Request $request, Business $business, BusinessBranch $branch)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'address_line_1' => 'required|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'postal_code' => 'required|string|max:20',
            'country' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'timezone' => 'nullable|string',
            'is_main' => 'boolean',
            'is_active' => 'boolean',
            'description' => 'nullable|string',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string',
            'parking_info' => 'nullable|string',
            'public_transport_info' => 'nullable|string',
        ]);

        // Set defaults
        $validated['is_main'] = $validated['is_main'] ?? false;
        $validated['is_active'] = $validated['is_active'] ?? true;

        // If this is set as main branch, unset other main branches
        if ($validated['is_main'] && !$branch->is_main) {
            BusinessBranch::where('business_id', $business->id)
                ->where('id', '!=', $branch->id)
                ->where('is_main', true)
                ->update(['is_main' => false]);
        }

        // Check if address changed and geocode if needed
        $addressChanged = $branch->address_line_1 !== $validated['address_line_1'] ||
                         $branch->city !== $validated['city'] ||
                         $branch->state !== $validated['state'] ||
                         $branch->postal_code !== $validated['postal_code'];

        if ($addressChanged && (empty($validated['latitude']) || empty($validated['longitude']))) {
            $coordinates = $this->geocodeAddress($validated);
            if ($coordinates) {
                $validated['latitude'] = $coordinates['lat'];
                $validated['longitude'] = $coordinates['lng'];
            }
        }

        $branch->update($validated);

        return redirect()->route('admin.businesses.locations.show', [$business, $branch])
                        ->with('success', 'Location updated successfully.');
    }

    /**
     * Remove the specified location.
     */
    public function destroy(Business $business, BusinessBranch $branch)
    {
        // Prevent deletion of main branch if it's the only one
        if ($branch->is_main && $business->branches()->count() === 1) {
            return back()->withErrors(['error' => 'Cannot delete the only location for this business.']);
        }

        $branch->delete();

        return redirect()->route('admin.businesses.locations.index', $business)
                        ->with('success', 'Location deleted successfully.');
    }

    /**
     * Get locations for map display.
     */
    public function getMapData(Business $business)
    {
        $branches = $business->branches()
            ->active()
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->get();

        $locations = $branches->map(function ($branch) {
            return [
                'id' => $branch->id,
                'name' => $branch->name,
                'address' => $branch->full_address,
                'lat' => (float) $branch->latitude,
                'lng' => (float) $branch->longitude,
                'phone' => $branch->phone,
                'email' => $branch->email,
                'is_main' => $branch->is_main,
                'amenities' => $branch->amenities,
                'description' => $branch->description
            ];
        });

        return response()->json($locations);
    }

    /**
     * Geocode an address using Google Maps API.
     */
    private function geocodeAddress($addressData)
    {
        $apiKey = config('services.google_maps.api_key');

        if (!$apiKey) {
            return null;
        }

        $address = implode(', ', array_filter([
            $addressData['address_line_1'],
            $addressData['address_line_2'],
            $addressData['city'],
            $addressData['state'],
            $addressData['postal_code'],
            $addressData['country']
        ]));

        $url = 'https://maps.googleapis.com/maps/api/geocode/json?' . http_build_query([
            'address' => $address,
            'key' => $apiKey
        ]);

        try {
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if ($data['status'] === 'OK' && !empty($data['results'])) {
                $location = $data['results'][0]['geometry']['location'];
                return [
                    'lat' => $location['lat'],
                    'lng' => $location['lng']
                ];
            }
        } catch (\Exception $e) {
            // Log error but don't fail the request
            \Log::error('Geocoding failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Search for places using Google Places API.
     */
    public function searchPlaces(Request $request)
    {
        $query = $request->get('query');
        $apiKey = config('services.google_maps.api_key');

        if (!$apiKey || !$query) {
            return response()->json([]);
        }

        $url = 'https://maps.googleapis.com/maps/api/place/textsearch/json?' . http_build_query([
            'query' => $query,
            'key' => $apiKey
        ]);

        try {
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if ($data['status'] === 'OK') {
                $places = array_map(function ($place) {
                    return [
                        'place_id' => $place['place_id'],
                        'name' => $place['name'],
                        'formatted_address' => $place['formatted_address'],
                        'lat' => $place['geometry']['location']['lat'],
                        'lng' => $place['geometry']['location']['lng'],
                        'types' => $place['types'] ?? []
                    ];
                }, $data['results']);

                return response()->json($places);
            }
        } catch (\Exception $e) {
            \Log::error('Places search failed: ' . $e->getMessage());
        }

        return response()->json([]);
    }

    /**
     * Get place details using Google Places API.
     */
    public function getPlaceDetails(Request $request)
    {
        $placeId = $request->get('place_id');
        $apiKey = config('services.google_maps.api_key');

        if (!$apiKey || !$placeId) {
            return response()->json(['error' => 'Invalid request'], 400);
        }

        $url = 'https://maps.googleapis.com/maps/api/place/details/json?' . http_build_query([
            'place_id' => $placeId,
            'fields' => 'name,formatted_address,geometry,formatted_phone_number,website,address_components',
            'key' => $apiKey
        ]);

        try {
            $response = file_get_contents($url);
            $data = json_decode($response, true);

            if ($data['status'] === 'OK') {
                $place = $data['result'];

                // Parse address components
                $addressComponents = [];
                foreach ($place['address_components'] as $component) {
                    $types = $component['types'];
                    if (in_array('street_number', $types)) {
                        $addressComponents['street_number'] = $component['long_name'];
                    } elseif (in_array('route', $types)) {
                        $addressComponents['route'] = $component['long_name'];
                    } elseif (in_array('locality', $types)) {
                        $addressComponents['city'] = $component['long_name'];
                    } elseif (in_array('administrative_area_level_1', $types)) {
                        $addressComponents['state'] = $component['long_name'];
                    } elseif (in_array('postal_code', $types)) {
                        $addressComponents['postal_code'] = $component['long_name'];
                    } elseif (in_array('country', $types)) {
                        $addressComponents['country'] = $component['long_name'];
                    }
                }

                return response()->json([
                    'name' => $place['name'],
                    'formatted_address' => $place['formatted_address'],
                    'lat' => $place['geometry']['location']['lat'],
                    'lng' => $place['geometry']['location']['lng'],
                    'phone' => $place['formatted_phone_number'] ?? null,
                    'website' => $place['website'] ?? null,
                    'address_components' => $addressComponents
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Place details failed: ' . $e->getMessage());
        }

        return response()->json(['error' => 'Failed to get place details'], 500);
    }
}
