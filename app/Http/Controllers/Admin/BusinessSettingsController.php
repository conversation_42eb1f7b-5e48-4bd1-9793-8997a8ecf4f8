<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Routing\Controller;
use App\Models\Business;
use App\Models\Setting;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Artisan;

class BusinessSettingsController extends Controller
{
    // Note: Middleware is applied at the route level in routes/web.php

    /**
     * Display business branding settings.
     */
    public function branding(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        return view('admin.settings.branding', compact('business', 'businesses'));
    }

    /**
     * Update business branding settings.
     */
    public function updateBranding(Request $request)
    {
        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'primary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'accent_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'font_family' => 'nullable|string|max:100',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'favicon' => 'nullable|image|mimes:ico,png|max:512',
            'custom_css' => 'nullable|string',
            'booking_page_header' => 'nullable|string|max:255',
            'booking_page_footer' => 'nullable|string|max:255',
            'email_header_color' => 'nullable|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'email_footer_text' => 'nullable|string|max:500',
        ]);

        $business = Business::findOrFail($validated['business_id']);
        $branding = $business->branding ?? [];

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $logoPath = $request->file('logo')->store('branding/logos', 'public');
            $branding['logo'] = $logoPath;
        }

        if ($request->hasFile('favicon')) {
            $faviconPath = $request->file('favicon')->store('branding/favicons', 'public');
            $branding['favicon'] = $faviconPath;
        }

        // Update branding data
        $branding = array_merge($branding, array_filter([
            'primary_color' => $validated['primary_color'],
            'secondary_color' => $validated['secondary_color'],
            'accent_color' => $validated['accent_color'],
            'font_family' => $validated['font_family'],
            'custom_css' => $validated['custom_css'],
            'booking_page_header' => $validated['booking_page_header'],
            'booking_page_footer' => $validated['booking_page_footer'],
            'email_header_color' => $validated['email_header_color'],
            'email_footer_text' => $validated['email_footer_text'],
        ]));

        $business->update(['branding' => $branding]);

        return redirect()->route('admin.settings.branding', ['business_id' => $business->id])
                        ->with('success', 'Branding settings updated successfully.');
    }

    /**
     * Display check-in rules settings.
     */
    public function checkInRules(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'auto_check_in_enabled' => Setting::getValue('auto_check_in_enabled', false),
            'check_in_window_minutes' => Setting::getValue('check_in_window_minutes', 15),
            'late_arrival_grace_minutes' => Setting::getValue('late_arrival_grace_minutes', 10),
            'auto_no_show_minutes' => Setting::getValue('auto_no_show_minutes', 30),
            'require_check_in_confirmation' => Setting::getValue('require_check_in_confirmation', true),
            'send_check_in_notifications' => Setting::getValue('send_check_in_notifications', true),
            'allow_early_check_in' => Setting::getValue('allow_early_check_in', true),
            'early_check_in_minutes' => Setting::getValue('early_check_in_minutes', 15),
        ];

        return view('admin.settings.check-in-rules', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update check-in rules settings.
     */
    public function updateCheckInRules(Request $request)
    {
        $validated = $request->validate([
            'check_in_window_minutes' => 'required|integer|min:1|max:120',
            'late_arrival_grace_minutes' => 'required|integer|min:0|max:60',
            'auto_no_show_minutes' => 'required|integer|min:5|max:180',
            'early_check_in_minutes' => 'nullable|integer|min:1|max:60',
        ]);

        // Handle boolean fields manually (checkboxes send value only when checked)
        $booleanFields = [
            'auto_check_in_enabled',
            'require_check_in_confirmation',
            'send_check_in_notifications',
            'allow_early_check_in',
        ];

        foreach ($booleanFields as $field) {
            $validated[$field] = $request->has($field) ? 1 : 0;
        }

        // Set default value for early_check_in_minutes if not provided
        if (!isset($validated['early_check_in_minutes']) || $validated['early_check_in_minutes'] === null) {
            $validated['early_check_in_minutes'] = 15; // Default value
        }

        // Save all settings
        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        return redirect()->route('admin.settings.check-in-rules')
                        ->with('success', 'Check-in rules updated successfully.');
    }

    /**
     * Display no-show management settings.
     */
    public function noShowManagement(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'no_show_fee_enabled' => (bool) Setting::getValue('no_show_fee_enabled', false),
            'no_show_fee_amount' => (float) Setting::getValue('no_show_fee_amount', 0),
            'no_show_fee_percentage' => (int) Setting::getValue('no_show_fee_percentage', 0),
            'no_show_blacklist_enabled' => (bool) Setting::getValue('no_show_blacklist_enabled', false),
            'no_show_blacklist_threshold' => (int) Setting::getValue('no_show_blacklist_threshold', 3),
            'no_show_blacklist_period_days' => (int) Setting::getValue('no_show_blacklist_period_days', 90),
            'send_no_show_notifications' => (bool) Setting::getValue('send_no_show_notifications', true),
            'no_show_follow_up_enabled' => (bool) Setting::getValue('no_show_follow_up_enabled', true),
            'no_show_follow_up_delay_hours' => (int) Setting::getValue('no_show_follow_up_delay_hours', 24),
        ];

        return view('admin.settings.no-show', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update no-show management settings.
     */
    public function updateNoShowManagement(Request $request)
    {
        $validated = $request->validate([
            'no_show_fee_amount' => 'required|numeric|min:0|max:1000',
            'no_show_fee_percentage' => 'required|integer|min:0|max:100',
            'no_show_blacklist_threshold' => 'required|integer|min:1|max:10',
            'no_show_blacklist_period_days' => 'required|integer|min:1|max:365',
            'no_show_follow_up_delay_hours' => 'required|integer|min:1|max:168',
        ]);

        // Handle boolean fields manually (checkboxes send value only when checked)
        $booleanFields = [
            'no_show_fee_enabled',
            'no_show_blacklist_enabled',
            'send_no_show_notifications',
            'no_show_follow_up_enabled',
        ];

        foreach ($booleanFields as $field) {
            $validated[$field] = $request->has($field) ? 1 : 0;
        }

        // Save all settings
        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        return redirect()->route('admin.settings.no-show')
                        ->with('success', 'No-show management settings updated successfully.');
    }

    /**
     * Display email templates settings.
     */
    public function emailTemplates(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $templates = [
            'booking_confirmation' => Setting::getValue('email_template_booking_confirmation', ''),
            'booking_reminder' => Setting::getValue('email_template_booking_reminder', ''),
            'booking_cancellation' => Setting::getValue('email_template_booking_cancellation', ''),
            'waiting_list_notification' => Setting::getValue('email_template_waiting_list_notification', ''),
            'check_in_confirmation' => Setting::getValue('email_template_check_in_confirmation', ''),
            'no_show_follow_up' => Setting::getValue('email_template_no_show_follow_up', ''),
        ];

        return view('admin.settings.email-templates', compact('business', 'businesses', 'templates'));
    }

    /**
     * Update email templates.
     */
    public function updateEmailTemplates(Request $request)
    {
        $validated = $request->validate([
            'booking_confirmation' => 'nullable|string',
            'booking_reminder' => 'nullable|string',
            'booking_cancellation' => 'nullable|string',
            'waiting_list_notification' => 'nullable|string',
            'check_in_confirmation' => 'nullable|string',
            'no_show_follow_up' => 'nullable|string',
        ]);

        foreach ($validated as $template => $content) {
            Setting::setValue("email_template_{$template}", $content);
        }

        return redirect()->route('admin.settings.email-templates')
                        ->with('success', 'Email templates updated successfully.');
    }

    /**
     * Display SMS settings.
     */
    public function smsSettings(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'sms_enabled' => Setting::getValue('sms_enabled', false),
            'sms_provider' => Setting::getValue('sms_provider', 'twilio'),
            'sms_api_key' => Setting::getValue('sms_api_key', ''),
            'sms_api_secret' => Setting::getValue('sms_api_secret', ''),
            'sms_from_number' => Setting::getValue('sms_from_number', ''),
            'aws_region' => Setting::getValue('aws_region', 'us-east-1'),
            'sms_booking_confirmation' => Setting::getValue('sms_booking_confirmation', true),
            'sms_booking_reminder' => Setting::getValue('sms_booking_reminder', true),
            'sms_waiting_list_notification' => Setting::getValue('sms_waiting_list_notification', true),
        ];

        return view('admin.settings.sms', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update SMS settings.
     */
    public function updateSmsSettings(Request $request)
    {
        // Base validation rules
        $rules = [
            'sms_enabled' => 'required|in:0,1',
            'sms_provider' => 'required|in:twilio,nexmo,aws_sns',
            'sms_booking_confirmation' => 'required|in:0,1',
            'sms_booking_reminder' => 'required|in:0,1',
            'sms_waiting_list_notification' => 'required|in:0,1',
        ];

        // Add provider-specific validation if SMS is enabled
        if ($request->input('sms_enabled') == '1') {
            $provider = $request->input('sms_provider');

            switch ($provider) {
                case 'twilio':
                    $rules['sms_api_key'] = 'required|string|max:255'; // Account SID
                    $rules['sms_api_secret'] = 'required|string|max:255'; // Auth Token
                    $rules['sms_from_number'] = 'required|string|max:20'; // Twilio Phone Number
                    break;
                case 'nexmo':
                    $rules['sms_api_key'] = 'required|string|max:255'; // API Key
                    $rules['sms_api_secret'] = 'required|string|max:255'; // API Secret
                    $rules['sms_from_number'] = 'required|string|max:20'; // Sender ID
                    break;
                case 'aws_sns':
                    $rules['sms_api_key'] = 'required|string|max:255'; // Access Key ID
                    $rules['sms_api_secret'] = 'required|string|max:255'; // Secret Access Key
                    $rules['sms_from_number'] = 'nullable|string|max:20'; // Sender ID (optional for AWS)
                    $rules['aws_region'] = 'required|string|max:50'; // AWS Region
                    break;
            }
        } else {
            // If SMS is disabled, make credentials optional
            $rules['sms_api_key'] = 'nullable|string|max:255';
            $rules['sms_api_secret'] = 'nullable|string|max:255';
            $rules['sms_from_number'] = 'nullable|string|max:20';
            $rules['aws_region'] = 'nullable|string|max:50';
        }

        $validated = $request->validate($rules);

        // Convert string values to boolean for storage
        $validated['sms_enabled'] = (bool) $validated['sms_enabled'];
        $validated['sms_booking_confirmation'] = (bool) $validated['sms_booking_confirmation'];
        $validated['sms_booking_reminder'] = (bool) $validated['sms_booking_reminder'];
        $validated['sms_waiting_list_notification'] = (bool) $validated['sms_waiting_list_notification'];

        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        return redirect()->route('admin.settings.sms')
                        ->with('success', 'SMS settings updated successfully.');
    }

    /**
     * Send test SMS to verify SMS configuration.
     */
    public function sendTestSms(Request $request, SmsService $smsService)
    {
        $request->validate([
            'test_phone' => 'required|string|max:20',
        ]);

        try {
            // Check if SMS is enabled
            if (!Setting::getValue('sms_enabled', false)) {
                return response()->json([
                    'success' => false,
                    'message' => 'SMS notifications are disabled. Please enable them first.'
                ], 422);
            }

            // Create test SMS content
            $testMessage = "Hello! This is a test SMS from " . config('app.name') . " to verify your SMS configuration is working correctly. If you received this message, your SMS integration is working properly!";

            // Send test SMS
            $result = $smsService->testSms($request->test_phone, $testMessage);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => 'Test SMS sent successfully to ' . $request->test_phone,
                    'data' => $result['data'] ?? null
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 422);
            }

        } catch (\Exception $e) {
            \Log::error('Test SMS failed: ' . $e->getMessage(), [
                'phone' => $request->test_phone,
                'settings' => [
                    'sms_provider' => Setting::getValue('sms_provider'),
                    'sms_enabled' => Setting::getValue('sms_enabled'),
                ]
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test SMS: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Display reminder rules settings.
     */
    public function reminderRules(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'reminder_enabled' => Setting::getValue('reminder_enabled', true),
            'reminder_email_enabled' => Setting::getValue('reminder_email_enabled', true),
            'reminder_sms_enabled' => Setting::getValue('reminder_sms_enabled', false),
            'reminder_24h_enabled' => Setting::getValue('reminder_24h_enabled', true),
            'reminder_2h_enabled' => Setting::getValue('reminder_2h_enabled', true),
            'reminder_30m_enabled' => Setting::getValue('reminder_30m_enabled', false),
            'reminder_custom_times' => Setting::getValue('reminder_custom_times', []),
        ];

        return view('admin.settings.reminders', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update reminder rules.
     */
    public function updateReminderRules(Request $request)
    {
        $validated = $request->validate([
            'reminder_enabled' => 'boolean',
            'reminder_email_enabled' => 'boolean',
            'reminder_sms_enabled' => 'boolean',
            'reminder_24h_enabled' => 'boolean',
            'reminder_2h_enabled' => 'boolean',
            'reminder_30m_enabled' => 'boolean',
            'custom_reminder_times' => 'nullable|array',
            'custom_reminder_times.*' => 'integer|min:1|max:10080', // Max 1 week in minutes
        ]);

        // Handle custom reminder times
        if (isset($validated['custom_reminder_times'])) {
            $validated['reminder_custom_times'] = $validated['custom_reminder_times'];
            unset($validated['custom_reminder_times']);
        }

        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        return redirect()->route('admin.settings.reminders')
                        ->with('success', 'Reminder rules updated successfully.');
    }

    /**
     * Display waiting list alerts settings.
     */
    public function waitingListAlerts(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'waiting_list_auto_notify' => Setting::getValue('waiting_list_auto_notify', true),
            'waiting_list_notification_delay' => Setting::getValue('waiting_list_notification_delay', 5),
            'waiting_list_max_notifications' => Setting::getValue('waiting_list_max_notifications', 3),
            'waiting_list_notification_interval' => Setting::getValue('waiting_list_notification_interval', 24),
            'waiting_list_priority_boost' => Setting::getValue('waiting_list_priority_boost', true),
            'waiting_list_expire_days' => Setting::getValue('waiting_list_expire_days', 30),
        ];

        return view('admin.settings.waiting-list-alerts', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update waiting list alerts settings.
     */
    public function updateWaitingListAlerts(Request $request)
    {
        $validated = $request->validate([
            'waiting_list_auto_notify' => 'boolean',
            'waiting_list_notification_delay' => 'required|integer|min:0|max:60',
            'waiting_list_max_notifications' => 'required|integer|min:1|max:10',
            'waiting_list_notification_interval' => 'required|integer|min:1|max:168',
            'waiting_list_priority_boost' => 'boolean',
            'waiting_list_expire_days' => 'required|integer|min:1|max:365',
        ]);

        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        return redirect()->route('admin.settings.waiting-list-alerts')
                        ->with('success', 'Waiting list alerts settings updated successfully.');
    }

    /**
     * Display email/SMTP settings.
     */
    public function emailSettings(Request $request)
    {
        $businessId = $request->get('business_id');
        $business = $businessId ? Business::findOrFail($businessId) : null;
        $businesses = Business::active()->get();

        $settings = [
            'mail_mailer' => Setting::getValue('mail_mailer', config('mail.default', 'smtp')),
            'mail_host' => Setting::getValue('mail_host', config('mail.mailers.smtp.host', '')),
            'mail_port' => Setting::getValue('mail_port', config('mail.mailers.smtp.port', 587)),
            'mail_username' => Setting::getValue('mail_username', config('mail.mailers.smtp.username', '')),
            'mail_password' => Setting::getValue('mail_password', config('mail.mailers.smtp.password', '')),
            'mail_encryption' => Setting::getValue('mail_encryption', 'tls'),
            'mail_from_address' => Setting::getValue('mail_from_address', config('mail.from.address', '')),
            'mail_from_name' => Setting::getValue('mail_from_name', config('mail.from.name', '')),
            'mail_enabled' => Setting::getValue('mail_enabled', true),

            // Mailgun settings
            'mailgun_domain' => Setting::getValue('mailgun_domain', ''),
            'mailgun_secret' => Setting::getValue('mailgun_secret', ''),
            'mailgun_endpoint' => Setting::getValue('mailgun_endpoint', 'api.mailgun.net'),

            // Amazon SES settings
            'aws_access_key_id' => Setting::getValue('aws_access_key_id', ''),
            'aws_secret_access_key' => Setting::getValue('aws_secret_access_key', ''),
            'aws_default_region' => Setting::getValue('aws_default_region', 'us-east-1'),
            'ses_configuration_set' => Setting::getValue('ses_configuration_set', ''),

            // Postmark settings
            'postmark_token' => Setting::getValue('postmark_token', ''),
            'postmark_message_stream_id' => Setting::getValue('postmark_message_stream_id', ''),
        ];

        return view('admin.settings.email', compact('business', 'businesses', 'settings'));
    }

    /**
     * Update email/SMTP settings.
     */
    public function updateEmailSettings(Request $request)
    {
        $mailDriver = $request->input('mail_mailer');

        // Build validation rules dynamically based on mail driver
        $rules = [
            'mail_mailer' => 'required|in:smtp,sendmail,mailgun,ses,postmark,log',
            'mail_from_address' => 'required|email|max:255',
            'mail_from_name' => 'required|string|max:255',
            'mail_encryption' => 'nullable|in:tls,ssl,null',
        ];

        // Add provider-specific validation rules
        if ($mailDriver === 'smtp') {
            $rules['mail_host'] = 'required|string|max:255';
            $rules['mail_port'] = 'required|integer|min:1|max:65535';
            $rules['mail_username'] = 'nullable|string|max:255';
            $rules['mail_password'] = 'nullable|string|max:255';
        } elseif ($mailDriver === 'mailgun') {
            $rules['mailgun_domain'] = 'required|string|max:255';
            $rules['mailgun_secret'] = 'required|string|max:255';
            $rules['mailgun_endpoint'] = 'required|in:api.mailgun.net,api.eu.mailgun.net';
        } elseif ($mailDriver === 'ses') {
            $rules['aws_access_key_id'] = 'required|string|max:255';
            $rules['aws_secret_access_key'] = 'required|string|max:255';
            $rules['aws_default_region'] = 'required|string|max:50';
            $rules['ses_configuration_set'] = 'nullable|string|max:255';
        } elseif ($mailDriver === 'postmark') {
            $rules['postmark_token'] = 'required|string|max:255';
            $rules['postmark_message_stream_id'] = 'nullable|string|max:255';
        }

        // Add optional fields for other drivers
        if ($mailDriver !== 'smtp') {
            $rules['mail_host'] = 'nullable|string|max:255';
            $rules['mail_port'] = 'nullable|integer|min:1|max:65535';
            $rules['mail_username'] = 'nullable|string|max:255';
            $rules['mail_password'] = 'nullable|string|max:255';
        }

        if ($mailDriver !== 'mailgun') {
            $rules['mailgun_domain'] = 'nullable|string|max:255';
            $rules['mailgun_secret'] = 'nullable|string|max:255';
            $rules['mailgun_endpoint'] = 'nullable|in:api.mailgun.net,api.eu.mailgun.net';
        }

        if ($mailDriver !== 'ses') {
            $rules['aws_access_key_id'] = 'nullable|string|max:255';
            $rules['aws_secret_access_key'] = 'nullable|string|max:255';
            $rules['aws_default_region'] = 'nullable|string|max:50';
            $rules['ses_configuration_set'] = 'nullable|string|max:255';
        }

        if ($mailDriver !== 'postmark') {
            $rules['postmark_token'] = 'nullable|string|max:255';
            $rules['postmark_message_stream_id'] = 'nullable|string|max:255';
        }

        $validated = $request->validate($rules);

        // Handle boolean fields - with hidden input, we get the value directly
        $validated['mail_enabled'] = $request->input('mail_enabled', 0);

        // Save all settings
        foreach ($validated as $key => $value) {
            Setting::setValue($key, $value);
        }

        // Update environment file for mail settings
        $this->updateMailEnvironmentFile($validated);

        return redirect()->route('admin.settings.email')
                        ->with('success', 'Email settings updated successfully.');
    }

    /**
     * Send test email to verify SMTP configuration.
     */
    public function sendTestEmail(Request $request)
    {
        $request->validate([
            'test_email' => 'required|email',
        ]);

        try {
            // Check if email is enabled
            if (!Setting::getValue('mail_enabled', true)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Email notifications are disabled. Please enable them first.'
                ], 422);
            }

            // Temporarily update mail configuration
            $this->updateMailConfig();

            // Create test email content
            $testContent = "
Hello,

This is a test email to verify your SMTP configuration is working correctly.

Email Settings:
- Mail Driver: " . Setting::getValue('mail_mailer', 'smtp') . "
- SMTP Host: " . Setting::getValue('mail_host', 'Not Set') . "
- SMTP Port: " . Setting::getValue('mail_port', 'Not Set') . "
- Encryption: " . Setting::getValue('mail_encryption', 'Not Set') . "
- From Address: " . Setting::getValue('mail_from_address', 'Not Set') . "

If you received this email, your SMTP configuration is working properly!

Best regards,
" . config('app.name') . " Team
            ";

            // Send test email
            Mail::raw($testContent, function ($message) use ($request) {
                $message->to($request->test_email)
                        ->subject('SMTP Configuration Test - ' . config('app.name'))
                        ->from(
                            Setting::getValue('mail_from_address', config('mail.from.address')),
                            Setting::getValue('mail_from_name', config('mail.from.name'))
                        );
            });

            return response()->json([
                'success' => true,
                'message' => 'Test email sent successfully to ' . $request->test_email
            ]);

        } catch (\Exception $e) {
            \Log::error('Test email failed: ' . $e->getMessage(), [
                'email' => $request->test_email,
                'settings' => [
                    'mail_mailer' => Setting::getValue('mail_mailer'),
                    'mail_host' => Setting::getValue('mail_host'),
                    'mail_port' => Setting::getValue('mail_port'),
                ]
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to send test email: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Update mail configuration from database settings.
     */
    private function updateMailConfig()
    {
        $mailer = Setting::getValue('mail_mailer', 'smtp');
        $fromAddress = Setting::getValue('mail_from_address', '');
        $fromName = Setting::getValue('mail_from_name', '');

        // Update Laravel's mail configuration
        Config::set('mail.default', $mailer);
        Config::set('mail.from.address', $fromAddress);
        Config::set('mail.from.name', $fromName);

        // Configure based on mail driver
        if ($mailer === 'smtp') {
            $host = Setting::getValue('mail_host', '');
            $port = Setting::getValue('mail_port', 587);
            $username = Setting::getValue('mail_username', '');
            $password = Setting::getValue('mail_password', '');
            $encryption = Setting::getValue('mail_encryption', 'tls');

            Config::set('mail.mailers.smtp.host', $host);
            Config::set('mail.mailers.smtp.port', $port);
            Config::set('mail.mailers.smtp.username', $username);
            Config::set('mail.mailers.smtp.password', $password);
            Config::set('mail.mailers.smtp.encryption', $encryption === 'null' ? null : $encryption);
        } elseif ($mailer === 'mailgun') {
            $domain = Setting::getValue('mailgun_domain', '');
            $secret = Setting::getValue('mailgun_secret', '');
            $endpoint = Setting::getValue('mailgun_endpoint', 'api.mailgun.net');

            Config::set('services.mailgun.domain', $domain);
            Config::set('services.mailgun.secret', $secret);
            Config::set('services.mailgun.endpoint', $endpoint);
        } elseif ($mailer === 'ses') {
            $accessKey = Setting::getValue('aws_access_key_id', '');
            $secretKey = Setting::getValue('aws_secret_access_key', '');
            $region = Setting::getValue('aws_default_region', 'us-east-1');
            $configSet = Setting::getValue('ses_configuration_set', '');

            Config::set('services.ses.key', $accessKey);
            Config::set('services.ses.secret', $secretKey);
            Config::set('services.ses.region', $region);
            if ($configSet) {
                Config::set('services.ses.options.ConfigurationSetName', $configSet);
            }
        } elseif ($mailer === 'postmark') {
            $token = Setting::getValue('postmark_token', '');
            $messageStreamId = Setting::getValue('postmark_message_stream_id', '');

            Config::set('services.postmark.token', $token);
            if ($messageStreamId) {
                Config::set('mail.mailers.postmark.message_stream_id', $messageStreamId);
            }
        }
    }

    /**
     * Update environment file with mail settings.
     */
    private function updateMailEnvironmentFile(array $settings)
    {
        try {
            $envFile = app()->environmentFilePath();
            $envContents = file_get_contents($envFile);

            // Update common mail environment variables
            $envContents = $this->setEnvValue('MAIL_MAILER', $settings['mail_mailer'], $envContents);
            $envContents = $this->setEnvValue('MAIL_FROM_ADDRESS', $settings['mail_from_address'], $envContents);
            $envContents = $this->setEnvValue('MAIL_FROM_NAME', '"' . $settings['mail_from_name'] . '"', $envContents);

            // Update provider-specific environment variables
            if ($settings['mail_mailer'] === 'smtp') {
                $envContents = $this->setEnvValue('MAIL_HOST', $settings['mail_host'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAIL_PORT', $settings['mail_port'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAIL_USERNAME', $settings['mail_username'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAIL_PASSWORD', $settings['mail_password'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAIL_ENCRYPTION', $settings['mail_encryption'] ?? '', $envContents);
            } elseif ($settings['mail_mailer'] === 'mailgun') {
                $envContents = $this->setEnvValue('MAILGUN_DOMAIN', $settings['mailgun_domain'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAILGUN_SECRET', $settings['mailgun_secret'] ?? '', $envContents);
                $envContents = $this->setEnvValue('MAILGUN_ENDPOINT', $settings['mailgun_endpoint'] ?? 'api.mailgun.net', $envContents);
            } elseif ($settings['mail_mailer'] === 'ses') {
                $envContents = $this->setEnvValue('AWS_ACCESS_KEY_ID', $settings['aws_access_key_id'] ?? '', $envContents);
                $envContents = $this->setEnvValue('AWS_SECRET_ACCESS_KEY', $settings['aws_secret_access_key'] ?? '', $envContents);
                $envContents = $this->setEnvValue('AWS_DEFAULT_REGION', $settings['aws_default_region'] ?? 'us-east-1', $envContents);
                if (!empty($settings['ses_configuration_set'])) {
                    $envContents = $this->setEnvValue('SES_CONFIGURATION_SET', $settings['ses_configuration_set'], $envContents);
                }
            } elseif ($settings['mail_mailer'] === 'postmark') {
                $envContents = $this->setEnvValue('POSTMARK_TOKEN', $settings['postmark_token'] ?? '', $envContents);
                if (!empty($settings['postmark_message_stream_id'])) {
                    $envContents = $this->setEnvValue('POSTMARK_MESSAGE_STREAM_ID', $settings['postmark_message_stream_id'], $envContents);
                }
            }

            file_put_contents($envFile, $envContents);

            // Clear config cache
            Artisan::call('config:clear');

        } catch (\Exception $e) {
            \Log::error('Failed to update environment file: ' . $e->getMessage());
        }
    }

    /**
     * Set environment variable value.
     */
    private function setEnvValue($key, $value, $envContents)
    {
        $pattern = "/^{$key}=.*/m";
        $replacement = "{$key}={$value}";

        if (preg_match($pattern, $envContents)) {
            return preg_replace($pattern, $replacement, $envContents);
        } else {
            return $envContents . "\n{$replacement}";
        }
    }
}
