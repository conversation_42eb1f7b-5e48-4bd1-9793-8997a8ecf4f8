<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Handle the incoming request and redirect to appropriate dashboard based on user role.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Check user roles in order of priority and redirect accordingly
        
        // 1. Super Admin - highest priority
        if ($user->hasRole('Super Admin')) {
            return redirect()->route('admin.dashboard');
        }

        // 2. Admin - administrative access
        if ($user->hasRole('Admin')) {
            return redirect()->route('admin.dashboard');
        }

        // 3. Business Owner - business management access
        if ($user->hasRole('Business Owner')) {
            // Check if they have a business, if not redirect to business creation
            $business = $user->ownedBusinesses()->active()->first();
            if (!$business) {
                return redirect()->route('owner.business.create')
                    ->with('info', 'Welcome! Please create your business to get started.');
            }
            return redirect()->route('owner.dashboard');
        }

        // 4. Manager - limited business management
        if ($user->hasRole('Manager')) {
            return redirect()->route('owner.dashboard');
        }

        // 5. Staff - operational access
        if ($user->hasRole('Staff')) {
            return redirect()->route('owner.dashboard');
        }

        // 6. Customer - customer-specific access
        if ($user->hasRole('Customer') || $user->hasRole('customer')) {
            return redirect()->route('customer.dashboard');
        }

        // Default: If user has no specific role, treat as customer
        // This handles cases where users register but haven't been assigned roles yet
        
        // Auto-assign Customer role for new users
        $customerRole = \Spatie\Permission\Models\Role::where('name', 'Customer')->first();
        if ($customerRole) {
            $user->assignRole($customerRole);
            return redirect()->route('customer.dashboard')
                ->with('info', 'Welcome! You have been set up as a customer.');
        }

        // Fallback: If no Customer role exists, redirect to admin dashboard
        // This shouldn't happen in normal operation
        return redirect()->route('admin.dashboard')
            ->with('warning', 'No appropriate role found. Please contact administrator.');
    }
}
