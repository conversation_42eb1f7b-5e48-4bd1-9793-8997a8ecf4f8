<?php

namespace App\Http\Controllers;

use App\Models\Business;
use App\Models\BusinessLandingPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\View;

class LandingPageController extends Controller
{
    /**
     * Display the business landing page
     */
    public function show(Request $request, $slug)
    {
        // Try to find the landing page by slug
        $landingPage = BusinessLandingPage::where('custom_slug', $slug)
            ->where('is_published', true)
            ->with([
                'business.services' => function ($query) {
                    $query->where('is_active', true)->orderBy('sort_order');
                },
                'business.branches' => function ($query) {
                    $query->where('is_active', true);
                },
                'business.operatingHours',
                'business.seoSettings',
                'sections' => function ($query) {
                    $query->where('is_visible', true)->orderBy('sort_order');
                }
            ])
            ->first();

        if (!$landingPage) {
            abort(404, 'Business not found');
        }

        $business = $landingPage->business;
        
        // Check if business is active
        if (!$business->is_active) {
            abort(404, 'Business not found');
        }

        // Get cached data or generate it
        $cacheKey = "landing_page_{$landingPage->id}_data";
        $cacheTime = $landingPage->cache_enabled ? $landingPage->cache_duration : 0;
        
        $pageData = $cacheTime > 0 
            ? Cache::remember($cacheKey, $cacheTime, function () use ($landingPage, $business) {
                return $this->generatePageData($landingPage, $business);
            })
            : $this->generatePageData($landingPage, $business);

        // Track page view (implement analytics here)
        $this->trackPageView($landingPage, $request);

        // Set SEO meta tags
        $this->setSeoMetaTags($landingPage, $business);

        return view('landing-page.show', compact('landingPage', 'business', 'pageData'));
    }

    /**
     * Display services page
     */
    public function services($slug)
    {
        $landingPage = $this->getLandingPageBySlug($slug);
        $business = $landingPage->business;
        $services = $business->services()->where('is_active', true)->with('category')->get();

        $this->setSeoMetaTags($landingPage, $business, 'services');

        return view('landing-page.services', compact('landingPage', 'business', 'services'));
    }

    /**
     * Display booking page
     */
    public function booking($slug)
    {
        $landingPage = $this->getLandingPageBySlug($slug);
        $business = $landingPage->business;

        if (!$landingPage->booking_enabled || !$business->online_booking_enabled) {
            abort(404, 'Booking not available');
        }

        $services = $business->services()->where('is_active', true)->where('online_booking_enabled', true)->get();
        $branches = $business->branches()->where('is_active', true)->get();

        $this->setSeoMetaTags($landingPage, $business, 'booking');

        return view('landing-page.booking', compact('landingPage', 'business', 'services', 'branches'));
    }

    /**
     * Handle contact form submission
     */
    public function contact(Request $request, $slug)
    {
        $landingPage = $this->getLandingPageBySlug($slug);
        $business = $landingPage->business;

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:1000',
        ]);

        // Store the contact message (implement your contact storage logic)
        $this->storeContactMessage($business, $validated);

        // Send notification to business owner (implement email notification)
        $this->notifyBusinessOwner($business, $validated);

        return redirect()->back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }

    /**
     * Generate sitemap for the business
     */
    public function sitemap($slug)
    {
        $landingPage = $this->getLandingPageBySlug($slug);
        $business = $landingPage->business;
        $seoSettings = $business->seoSettings;

        if (!$seoSettings || !$seoSettings->sitemap_enabled) {
            abort(404);
        }

        $urls = $seoSettings->generateSitemap();

        return response()->view('landing-page.sitemap', compact('urls'))
            ->header('Content-Type', 'application/xml');
    }

    /**
     * Get landing page by slug
     */
    private function getLandingPageBySlug($slug)
    {
        $landingPage = BusinessLandingPage::where('custom_slug', $slug)
            ->where('is_published', true)
            ->with(['business', 'sections'])
            ->first();

        if (!$landingPage || !$landingPage->business->is_active) {
            abort(404, 'Business not found');
        }

        return $landingPage;
    }

    /**
     * Generate page data
     */
    private function generatePageData($landingPage, $business)
    {
        $sections = $landingPage->sections()->visible()->ordered()->get();
        $processedSections = [];

        foreach ($sections as $section) {
            $processedSections[] = [
                'type' => $section->section_type,
                'name' => $section->section_name,
                'content' => $section->renderContent(),
                'layout_config' => $section->layout_config,
                'style_config' => $section->style_config,
                'animation_config' => $section->animation_config,
            ];
        }

        return [
            'sections' => $processedSections,
            'theme_config' => $landingPage->theme_config,
            'branding_config' => $landingPage->branding_config,
            'booking_config' => $landingPage->booking_config,
            'services' => $business->services()->where('is_active', true)->get(),
            'main_branch' => $business->branches()->where('is_main_branch', true)->first(),
            'operating_hours' => $business->operatingHours,
        ];
    }

    /**
     * Set SEO meta tags
     */
    private function setSeoMetaTags($landingPage, $business, $page = 'home')
    {
        $seoSettings = $business->seoSettings;
        
        if (!$seoSettings) {
            return;
        }

        $metaTags = $seoSettings->getMetaTags();
        
        // Adjust meta tags based on page
        switch ($page) {
            case 'services':
                $metaTags['title'] = 'Services - ' . $business->name;
                $metaTags['description'] = 'Explore our services at ' . $business->name;
                break;
            case 'booking':
                $metaTags['title'] = 'Book Appointment - ' . $business->name;
                $metaTags['description'] = 'Book your appointment with ' . $business->name;
                break;
        }

        // Share meta tags with all views
        View::share('metaTags', $metaTags);
        View::share('businessSchema', $seoSettings->generateBusinessSchema());
    }

    /**
     * Track page view for analytics
     */
    private function trackPageView($landingPage, $request)
    {
        // Implement your analytics tracking here
        // This could integrate with Google Analytics, custom analytics, etc.
        
        // Example: Store in database for internal analytics
        // PageView::create([
        //     'business_landing_page_id' => $landingPage->id,
        //     'ip_address' => $request->ip(),
        //     'user_agent' => $request->userAgent(),
        //     'referrer' => $request->header('referer'),
        //     'visited_at' => now(),
        // ]);
    }

    /**
     * Store contact message
     */
    private function storeContactMessage($business, $data)
    {
        // Implement contact message storage
        // ContactMessage::create([
        //     'business_id' => $business->id,
        //     'name' => $data['name'],
        //     'email' => $data['email'],
        //     'phone' => $data['phone'],
        //     'subject' => $data['subject'],
        //     'message' => $data['message'],
        //     'source' => 'landing_page',
        // ]);
    }

    /**
     * Notify business owner of new contact message
     */
    private function notifyBusinessOwner($business, $data)
    {
        // Implement email notification to business owner
        // Mail::to($business->email)->send(new ContactFormSubmitted($business, $data));
    }

    /**
     * Handle subdomain routing
     */
    public function subdomain(Request $request, $subdomain)
    {
        $landingPage = BusinessLandingPage::where('custom_slug', $subdomain)
            ->where('domain_type', 'subdomain')
            ->where('is_published', true)
            ->first();

        if (!$landingPage) {
            abort(404, 'Business not found');
        }

        // Forward to the main show method
        return $this->show($request, $subdomain);
    }

    /**
     * Handle custom domain routing
     */
    public function customDomain(Request $request)
    {
        $domain = $request->getHost();
        
        $landingPage = BusinessLandingPage::where('custom_domain', $domain)
            ->where('domain_type', 'custom')
            ->where('is_published', true)
            ->first();

        if (!$landingPage) {
            abort(404, 'Business not found');
        }

        // Forward to the main show method
        return $this->show($request, $landingPage->custom_slug);
    }
}
