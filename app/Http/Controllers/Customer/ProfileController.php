<?php

namespace App\Http\Controllers\Customer;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Display the customer profile.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        return view('customer.profile.index', compact('customer'));
    }

    /**
     * Show the form for editing the customer profile.
     *
     * @return \Illuminate\Http\Response
     */
    public function edit()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        return view('customer.profile.edit', compact('customer'));
    }

    /**
     * Update the customer profile.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        try {
            $customer = $this->getCustomer();

            if (!$customer) {
                Log::warning('Customer profile update attempted without authentication');
                return redirect()->route('login')->with('error', 'Please log in to continue.');
            }

            Log::info('Customer profile update started', [
                'customer_id' => $customer->id,
                'customer_email' => $customer->email,
                'request_data' => $request->except(['profile_image', '_token'])
            ]);

            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users,email,' . $customer->id,
                'phone' => 'nullable|string|max:20',
                'date_of_birth' => 'nullable|date',
                'gender' => 'nullable|in:male,female,other,prefer_not_to_say',
                'timezone' => 'nullable|string|max:50',
                'language' => 'nullable|string|max:10',
                'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            Log::info('Validation passed', ['validated_data' => collect($validated)->except(['profile_image'])->toArray()]);

            // Handle profile image upload
            if ($request->hasFile('profile_image')) {
                Log::info('Processing profile image upload');

                // Delete old image if exists
                if ($customer->profile_image) {
                    Storage::disk('public')->delete($customer->profile_image);
                    Log::info('Deleted old profile image', ['old_image' => $customer->profile_image]);
                }

                $imagePath = $request->file('profile_image')->store('profile-images', 'public');
                $validated['profile_image'] = $imagePath;
                Log::info('New profile image uploaded', ['new_image' => $imagePath]);
            }

            // Update the customer
            $updateResult = $customer->update($validated);

            if ($updateResult) {
                Log::info('Customer profile updated successfully', [
                    'customer_id' => $customer->id,
                    'updated_fields' => array_keys($validated)
                ]);

                return redirect()->route('customer.profile.index')
                    ->with('success', 'Profile updated successfully.');
            } else {
                Log::error('Failed to update customer profile', [
                    'customer_id' => $customer->id,
                    'validated_data' => collect($validated)->except(['profile_image'])->toArray()
                ]);

                return redirect()->back()
                    ->withInput()
                    ->with('error', 'Failed to update profile. Please try again.');
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Validation failed during profile update', [
                'customer_id' => $customer->id ?? 'unknown',
                'errors' => $e->errors()
            ]);

            return redirect()->back()
                ->withErrors($e->errors())
                ->withInput()
                ->with('error', 'Please correct the errors below.');

        } catch (\Exception $e) {
            Log::error('Unexpected error during profile update', [
                'customer_id' => $customer->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'An unexpected error occurred. Please try again.');
        }
    }

    /**
     * Show the form for changing password.
     *
     * @return \Illuminate\Http\Response
     */
    public function showPasswordForm()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        return view('customer.profile.password', compact('customer'));
    }

    /**
     * Update the customer password.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Verify current password
        if (!Hash::check($validated['current_password'], $customer->password)) {
            return redirect()->back()
                ->withErrors(['current_password' => 'The current password is incorrect.'])
                ->withInput();
        }

        $customer->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('customer.profile.index')
            ->with('success', 'Password updated successfully.');
    }

    /**
     * Show customer preferences.
     *
     * @return \Illuminate\Http\Response
     */
    public function preferences()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        return view('customer.profile.preferences', compact('customer'));
    }

    /**
     * Update customer preferences.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePreferences(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'booking_reminders' => 'boolean',
            'promotional_emails' => 'boolean',
            'newsletter' => 'boolean',
            'reminder_time' => 'nullable|integer|min:1|max:48', // hours before appointment
            'default_booking_duration' => 'nullable|integer|min:15|max:480', // minutes
            'preferred_contact_method' => 'nullable|in:email,sms,phone',
            'auto_confirm_bookings' => 'boolean',
        ]);

        // Store preferences as JSON in a preferences column or separate table
        $preferences = $customer->preferences ?? [];
        $preferences = array_merge($preferences, $validated);

        $customer->update(['preferences' => $preferences]);

        return redirect()->route('customer.preferences')
            ->with('success', 'Preferences updated successfully.');
    }

    /**
     * Show customer privacy settings.
     *
     * @return \Illuminate\Http\Response
     */
    public function privacy()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        return view('customer.profile.privacy', compact('customer'));
    }

    /**
     * Update customer privacy settings.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePrivacy(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'profile_visibility' => 'required|in:public,private,friends',
            'show_booking_history' => 'boolean',
            'allow_reviews' => 'boolean',
            'data_sharing' => 'boolean',
            'analytics_tracking' => 'boolean',
        ]);

        $privacy = $customer->privacy_settings ?? [];
        $privacy = array_merge($privacy, $validated);

        $customer->update(['privacy_settings' => $privacy]);

        return redirect()->route('customer.privacy')
            ->with('success', 'Privacy settings updated successfully.');
    }

    /**
     * Delete customer account.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deleteAccount(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $request->validate([
            'password' => 'required|current_password',
            'confirmation' => 'required|in:DELETE',
        ]);

        // Cancel all future bookings
        $customer->bookings()
            ->where('start_datetime', '>', now())
            ->where('status', '!=', 'cancelled')
            ->update(['status' => 'cancelled']);

        // Delete profile image
        if ($customer->profile_image) {
            Storage::disk('public')->delete($customer->profile_image);
        }

        // Soft delete or permanently delete the user
        $customer->delete();

        // Logout
        Auth::logout();

        return redirect()->route('welcome')
            ->with('success', 'Your account has been successfully deleted.');
    }

    /**
     * Export customer data.
     *
     * @return \Illuminate\Http\Response
     */
    public function exportData()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $data = [
            'profile' => $customer->toArray(),
            'bookings' => $customer->bookings()->with(['services', 'business'])->get()->toArray(),
            'notifications' => $customer->notifications()->get()->toArray(),
            'preferences' => $customer->preferences ?? [],
            'privacy_settings' => $customer->privacy_settings ?? [],
        ];

        $filename = 'customer_data_' . $customer->id . '_' . now()->format('Y-m-d') . '.json';

        return response()->json($data)
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->header('Content-Type', 'application/json');
    }
}
