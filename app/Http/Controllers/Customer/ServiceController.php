<?php

namespace App\Http\Controllers\Customer;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\ServiceCategory;
use App\Models\Business;
use App\Models\ServiceAvailability;
use Carbon\Carbon;

class ServiceController extends Controller
{
    /**
     * Display a listing of services.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $query = Service::where('is_active', true)
            ->where('online_booking_enabled', true)
            ->with(['category', 'business']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('short_description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('category')) {
            $query->where('service_category_id', $request->get('category'));
        }

        if ($request->filled('price_range')) {
            $priceRange = $request->get('price_range');
            switch ($priceRange) {
                case '0-50':
                    $query->where('base_price', '<=', 50);
                    break;
                case '50-100':
                    $query->whereBetween('base_price', [50, 100]);
                    break;
                case '100-200':
                    $query->whereBetween('base_price', [100, 200]);
                    break;
                case '200+':
                    $query->where('base_price', '>', 200);
                    break;
            }
        }

        if ($request->filled('duration')) {
            $duration = $request->get('duration');
            switch ($duration) {
                case 'under_30':
                    $query->where('duration_minutes', '<', 30);
                    break;
                case '30_60':
                    $query->whereBetween('duration_minutes', [30, 60]);
                    break;
                case '60_120':
                    $query->whereBetween('duration_minutes', [60, 120]);
                    break;
                case 'over_120':
                    $query->where('duration_minutes', '>', 120);
                    break;
            }
        }

        if ($request->filled('business')) {
            $query->where('business_id', $request->get('business'));
        }

        $services = $query->orderBy('sort_order')
            ->orderBy('name')
            ->paginate(12);

        // Get categories for filter dropdown
        $categories = ServiceCategory::where('is_active', true)
            ->withCount(['services' => function($query) {
                $query->where('is_active', true)
                      ->where('online_booking_enabled', true);
            }])
            ->having('services_count', '>', 0)
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Get businesses for filter dropdown
        $businesses = Business::where('is_active', true)
            ->whereHas('services', function($query) {
                $query->where('is_active', true)
                      ->where('online_booking_enabled', true);
            })
            ->orderBy('name')
            ->get();

        // Get featured services (top services by sort order)
        $featuredServices = Service::where('is_active', true)
            ->where('online_booking_enabled', true)
            ->with(['category', 'business'])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit(6)
            ->get();

        return view('customer.services.index', compact(
            'services',
            'categories',
            'businesses',
            'featuredServices'
        ));
    }

    /**
     * Display service categories.
     *
     * @return \Illuminate\Http\Response
     */
    public function categories()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $categories = ServiceCategory::where('is_active', true)
            ->withCount(['services' => function($query) {
                $query->where('is_active', true)
                      ->where('online_booking_enabled', true);
            }])
            ->having('services_count', '>', 0)
            ->with(['services' => function($query) {
                $query->where('is_active', true)
                      ->where('online_booking_enabled', true)
                      ->orderBy('base_price')
                      ->limit(4);
            }])
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();

        // Get overall statistics
        $stats = [
            'total_services' => Service::where('is_active', true)
                ->where('online_booking_enabled', true)
                ->count(),
            'total_categories' => $categories->count(),
            'min_price' => Service::where('is_active', true)
                ->where('online_booking_enabled', true)
                ->min('base_price') ?? 0,
            'avg_rating' => 4.8, // Placeholder - would come from reviews
        ];

        return view('customer.services.categories', compact('categories', 'stats'));
    }

    /**
     * Display the specified service.
     *
     * @param Service $service
     * @return \Illuminate\Http\Response
     */
    public function show(Service $service)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        // Check if service is available for online booking
        if (!$service->is_active || !$service->online_booking_enabled) {
            abort(404, 'Service not found or not available for booking.');
        }

        $service->load(['category', 'business', 'resources', 'approvedReviews.user']);

        // Get related services from the same category or business
        $relatedServices = Service::where('is_active', true)
            ->where('online_booking_enabled', true)
            ->where('id', '!=', $service->id)
            ->where(function($query) use ($service) {
                $query->where('service_category_id', $service->service_category_id)
                      ->orWhere('business_id', $service->business_id);
            })
            ->with(['category', 'business'])
            ->orderBy('base_price')
            ->limit(6)
            ->get();

        // Get service reviews/ratings (real data)
        $reviews = $service->approvedReviews()
            ->with('user')
            ->orderBy('is_featured', 'desc')
            ->orderBy('reviewed_at', 'desc')
            ->limit(10)
            ->get();

        // Check if service is in customer's favorites
        $isFavorite = $customer->hasFavorited($service->id);

        return view('customer.services.show', compact(
            'service',
            'relatedServices',
            'reviews',
            'isFavorite'
        ));
    }

    /**
     * Search services.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $query = $request->get('q', '');

        if (strlen($query) < 2) {
            return response()->json(['services' => []]);
        }

        $services = Service::where('is_active', true)
            ->where('online_booking_enabled', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('short_description', 'like', "%{$query}%");
            })
            ->with(['category', 'business'])
            ->orderBy('name')
            ->limit(10)
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'business_name' => $service->business->name,
                    'category' => $service->category->name ?? 'General',
                    'price' => $service->base_price,
                    'duration' => $service->duration_minutes,
                    'url' => route('customer.services.show', $service),
                ];
            });

        return response()->json(['services' => $services]);
    }

    /**
     * Get available time slots for a service.
     *
     * @param Request $request
     * @param Service $service
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableSlots(Request $request, Service $service)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'date' => 'required|date|after_or_equal:today',
            'duration' => 'nullable|integer|min:15|max:480', // 15 minutes to 8 hours
        ]);

        $date = $request->get('date');
        $requestedDuration = $request->get('duration', $service->duration_minutes);

        // Check if service is active and available for booking
        if (!$service->is_active || !$service->online_booking_enabled) {
            return response()->json([
                'error' => 'Service not available for booking',
                'slots' => []
            ], 404);
        }

        // Get available slots for the requested date
        $availableSlots = $service->getAvailableSlotsForDate($date);

        // If no slots exist, try to generate them
        if ($availableSlots->isEmpty()) {
            $service->generateAvailabilitySlots(7); // Generate for next 7 days
            $availableSlots = $service->getAvailableSlotsForDate($date);
        }

        // Format slots for frontend
        $formattedSlots = $availableSlots->map(function ($slot) use ($service) {
            return [
                'id' => $slot->id,
                'time' => Carbon::parse($slot->start_time)->format('H:i'),
                'end_time' => Carbon::parse($slot->end_time)->format('H:i'),
                'time_range' => $slot->time_range,
                'price' => $slot->effective_price,
                'formatted_price' => '$' . number_format($slot->effective_price, 2),
                'remaining_capacity' => $slot->remaining_capacity,
                'is_bookable' => $slot->isBookable(),
                'formatted_datetime' => $slot->formatted_date_time,
            ];
        })->filter(function ($slot) {
            return $slot['is_bookable'];
        })->values();

        // Get next available date if no slots for requested date
        $nextAvailableSlot = null;
        if ($formattedSlots->isEmpty()) {
            $nextSlot = $service->getNextAvailableSlot();
            if ($nextSlot) {
                $nextAvailableSlot = [
                    'date' => $nextSlot->date->format('Y-m-d'),
                    'formatted_date' => $nextSlot->date->format('M j, Y'),
                    'time' => Carbon::parse($nextSlot->start_time)->format('H:i'),
                    'formatted_datetime' => $nextSlot->formatted_date_time,
                ];
            }
        }

        return response()->json([
            'success' => true,
            'date' => $date,
            'formatted_date' => Carbon::parse($date)->format('M j, Y'),
            'service' => [
                'id' => $service->id,
                'name' => $service->name,
                'duration' => $service->duration_minutes,
                'base_price' => $service->base_price,
            ],
            'slots' => $formattedSlots,
            'slots_count' => $formattedSlots->count(),
            'next_available' => $nextAvailableSlot,
            'message' => $formattedSlots->isEmpty()
                ? 'No available slots for this date'
                : $formattedSlots->count() . ' slots available'
        ]);
    }

    /**
     * Check real-time availability for multiple dates.
     *
     * @param Request $request
     * @param Service $service
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkAvailability(Request $request, Service $service)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'days' => 'nullable|integer|min:1|max:30',
        ]);

        $startDate = Carbon::parse($request->get('start_date'));
        $endDate = $request->get('end_date')
            ? Carbon::parse($request->get('end_date'))
            : $startDate->copy()->addDays($request->get('days', 7));

        $availability = [];
        $currentDate = $startDate->copy();

        while ($currentDate->lte($endDate)) {
            $dateString = $currentDate->toDateString();
            $slots = $service->getAvailableSlotsForDate($dateString);

            $availability[] = [
                'date' => $dateString,
                'formatted_date' => $currentDate->format('M j, Y'),
                'day_name' => $currentDate->format('l'),
                'is_available' => $slots->isNotEmpty(),
                'slots_count' => $slots->count(),
                'earliest_slot' => $slots->isNotEmpty()
                    ? Carbon::parse($slots->first()->start_time)->format('H:i')
                    : null,
                'latest_slot' => $slots->isNotEmpty()
                    ? Carbon::parse($slots->last()->start_time)->format('H:i')
                    : null,
            ];

            $currentDate->addDay();
        }

        return response()->json([
            'success' => true,
            'service' => [
                'id' => $service->id,
                'name' => $service->name,
                'duration' => $service->duration_minutes,
            ],
            'availability' => $availability,
            'summary' => [
                'total_days' => count($availability),
                'available_days' => collect($availability)->where('is_available', true)->count(),
                'total_slots' => collect($availability)->sum('slots_count'),
            ]
        ]);
    }
}
