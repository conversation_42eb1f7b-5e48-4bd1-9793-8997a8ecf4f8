<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller as BaseController;
use Illuminate\Support\Facades\Auth;

class Controller extends BaseController
{
    /**
     * Get the authenticated customer
     *
     * @return \App\Models\User
     */
    protected function getCustomer()
    {
        return Auth::user();
    }

    /**
     * Check if user has customer access
     *
     * @return bool
     */
    protected function hasCustomerAccess()
    {
        $user = $this->getCustomer();

        // Add logic here to determine if user has customer access
        // For now, we'll assume all authenticated users are customers
        return $user && ($user->hasRole('customer') || $user->hasRole('Customer'));
    }

    /**
     * Get customer's bookings
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getCustomerBookings()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return collect();
        }

        return $customer->bookings()
                       ->with(['services', 'business'])
                       ->orderBy('start_datetime', 'desc')
                       ->get();
    }

    /**
     * Get customer's favorite services
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    protected function getCustomerFavorites()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return collect();
        }

        // Assuming there's a favorites relationship or method
        return $customer->favoriteServices ?? collect();
    }
}
