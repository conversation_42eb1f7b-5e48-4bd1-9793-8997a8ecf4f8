<?php

namespace App\Http\Controllers\Customer;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Business;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Display the customer dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $customer = $this->getCustomer();
        $today = Carbon::today();

        // Get dashboard metrics for this customer
        $metrics = $this->getDashboardMetrics($customer, $today);

        return view('customer.dashboard.index', compact('metrics', 'today', 'customer'));
    }

    /**
     * Get dashboard metrics for the customer
     *
     * @param \App\Models\User $customer
     * @param Carbon $date
     * @return array
     */
    private function getDashboardMetrics($customer, $date)
    {
        if (!$customer) {
            return $this->getDefaultMetrics();
        }

        $startOfMonth = $date->copy()->startOfMonth();
        $endOfMonth = $date->copy()->endOfMonth();

        // Total bookings
        $totalBookings = Booking::where('customer_email', $customer->email)
            ->count();

        // Upcoming bookings
        $upcomingBookings = Booking::where('customer_email', $customer->email)
            ->where('start_datetime', '>', now())
            ->where('status', '!=', 'cancelled')
            ->count();

        // This month's bookings
        $monthlyBookings = Booking::where('customer_email', $customer->email)
            ->whereBetween('start_datetime', [$startOfMonth, $endOfMonth])
            ->count();

        // Total spent
        $totalSpent = Booking::where('customer_email', $customer->email)
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        // This month's spending
        $monthlySpent = Booking::where('customer_email', $customer->email)
            ->whereBetween('start_datetime', [$startOfMonth, $endOfMonth])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        // Completed bookings
        $completedBookings = Booking::where('customer_email', $customer->email)
            ->where('status', 'completed')
            ->count();

        // Loyalty points (if implemented)
        $loyaltyPoints = $customer->loyalty_points ?? 0;

        // Favorite businesses
        $favoriteBusinesses = $this->getFavoriteBusinesses($customer);

        return [
            'total_bookings' => $totalBookings,
            'upcoming_bookings' => $upcomingBookings,
            'monthly_bookings' => $monthlyBookings,
            'total_spent' => $totalSpent ?: 0,
            'monthly_spent' => $monthlySpent ?: 0,
            'completed_bookings' => $completedBookings,
            'loyalty_points' => $loyaltyPoints,
            'favorite_businesses' => $favoriteBusinesses,
            'recent_bookings' => $this->getRecentBookings($customer),
            'upcoming_appointments' => $this->getUpcomingAppointments($customer, $date),
            'booking_history_chart' => $this->getBookingHistoryChart($customer),
            'spending_chart' => $this->getSpendingChart($customer),
            'recommended_services' => $this->getRecommendedServices($customer),
            'recent_notifications' => $this->getRecentNotifications($customer),
        ];
    }

    /**
     * Get default metrics for guests or new customers
     *
     * @return array
     */
    private function getDefaultMetrics()
    {
        return [
            'total_bookings' => 0,
            'upcoming_bookings' => 0,
            'monthly_bookings' => 0,
            'total_spent' => 0,
            'monthly_spent' => 0,
            'completed_bookings' => 0,
            'loyalty_points' => 0,
            'favorite_businesses' => collect(),
            'recent_bookings' => collect(),
            'upcoming_appointments' => collect(),
            'booking_history_chart' => [],
            'spending_chart' => [],
            'recommended_services' => collect(),
            'recent_notifications' => collect(),
        ];
    }

    /**
     * Get customer's recent bookings
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecentBookings($customer)
    {
        return Booking::where('customer_email', $customer->email)
            ->with(['services', 'business'])
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'business_name' => $booking->business->name ?? 'N/A',
                    'service' => $booking->services->first()->name ?? 'N/A',
                    'date' => $booking->start_datetime->format('M d, Y'),
                    'time' => $booking->start_datetime->format('g:i A'),
                    'status' => $booking->status,
                    'amount' => $booking->total_amount,
                ];
            });
    }

    /**
     * Get customer's upcoming appointments
     *
     * @param \App\Models\User $customer
     * @param Carbon $date
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getUpcomingAppointments($customer, $date)
    {
        return Booking::where('customer_email', $customer->email)
            ->with(['services', 'business'])
            ->where('start_datetime', '>', now())
            ->where('status', '!=', 'cancelled')
            ->orderBy('start_datetime')
            ->limit(5)
            ->get()
            ->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'business_name' => $booking->business->name ?? 'N/A',
                    'service' => $booking->services->first()->name ?? 'N/A',
                    'date' => $booking->start_datetime->format('M d, Y'),
                    'time' => $booking->start_datetime->format('g:i A'),
                    'duration' => $booking->total_duration_minutes . ' min',
                    'status' => $booking->status,
                    'can_cancel' => $booking->start_datetime->diffInHours(now()) > 2, // 2 hour cancellation policy
                ];
            });
    }

    /**
     * Get customer's booking history chart data
     *
     * @param \App\Models\User $customer
     * @return array
     */
    private function getBookingHistoryChart($customer)
    {
        $labels = [];
        $data = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $labels[] = $date->format('M Y');

            $bookings = Booking::where('customer_email', $customer->email)
                ->whereYear('start_datetime', $date->year)
                ->whereMonth('start_datetime', $date->month)
                ->count();

            $data[] = $bookings;
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Bookings',
                    'data' => $data,
                    'backgroundColor' => 'rgba(23, 162, 184, 0.2)',
                    'borderColor' => 'rgba(23, 162, 184, 1)',
                    'borderWidth' => 2,
                    'fill' => true
                ]
            ]
        ];
    }

    /**
     * Get customer's spending chart data
     *
     * @param \App\Models\User $customer
     * @return array
     */
    private function getSpendingChart($customer)
    {
        $labels = [];
        $data = [];

        for ($i = 5; $i >= 0; $i--) {
            $date = Carbon::now()->subMonths($i);
            $labels[] = $date->format('M Y');

            $spending = Booking::where('customer_email', $customer->email)
                ->whereYear('start_datetime', $date->year)
                ->whereMonth('start_datetime', $date->month)
                ->where('status', '!=', 'cancelled')
                ->sum('total_amount');

            $data[] = $spending ?: 0;
        }

        return [
            'labels' => $labels,
            'datasets' => [
                [
                    'label' => 'Spending',
                    'data' => $data,
                    'backgroundColor' => 'rgba(40, 167, 69, 0.2)',
                    'borderColor' => 'rgba(40, 167, 69, 1)',
                    'borderWidth' => 2,
                    'fill' => true
                ]
            ]
        ];
    }

    /**
     * Get customer's favorite businesses
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getFavoriteBusinesses($customer)
    {
        // Get businesses the customer has booked with most frequently
        return Booking::where('customer_email', $customer->email)
            ->select('business_id', DB::raw('count(*) as booking_count'))
            ->groupBy('business_id')
            ->orderBy('booking_count', 'desc')
            ->with('business')
            ->limit(3)
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->business->id,
                    'name' => $item->business->name,
                    'booking_count' => $item->booking_count,
                    'category' => $item->business->category->name ?? 'General',
                ];
            });
    }

    /**
     * Get recommended services for the customer
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecommendedServices($customer)
    {
        // Get services from businesses the customer has booked with
        $businessIds = Booking::where('customer_email', $customer->email)
            ->distinct('business_id')
            ->pluck('business_id');

        return Service::whereIn('business_id', $businessIds)
            ->where('is_active', true)
            ->with(['business', 'category'])
            ->inRandomOrder()
            ->limit(6)
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'business_name' => $service->business->name,
                    'price' => $service->price,
                    'duration' => $service->duration_minutes,
                    'category' => $service->category->name ?? 'General',
                ];
            });
    }

    /**
     * Get customer's recent notifications
     *
     * @param \App\Models\User $customer
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRecentNotifications($customer)
    {
        return $customer->notifications()
            ->latest()
            ->limit(5)
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->data['title'] ?? 'Notification',
                    'message' => $notification->data['message'] ?? '',
                    'created_at' => $notification->created_at->diffForHumans(),
                    'read_at' => $notification->read_at,
                ];
            });
    }

    /**
     * Get quick stats for AJAX calls
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuickStats(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $stats = [
            'upcoming_bookings' => Booking::where('customer_email', $customer->email)
                ->where('start_datetime', '>', now())
                ->where('status', '!=', 'cancelled')
                ->count(),
            'unread_notifications' => $customer->unreadNotifications()->count(),
            'loyalty_points' => $customer->loyalty_points ?? 0,
        ];

        return response()->json($stats);
    }
}
