<?php

namespace App\Http\Controllers\Customer;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Business;

class BookingController extends Controller
{
    /**
     * Display customer's bookings.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $status = $request->get('status', 'all');
        $query = Booking::where('customer_email', $customer->email)
            ->with(['services', 'business']);

        // Filter by status
        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Filter upcoming bookings by default
        if ($status === 'upcoming') {
            $query->where('start_datetime', '>', now());
        }

        $bookings = $query->orderBy('start_datetime', 'desc')->paginate(10);

        $stats = [
            'total' => Booking::where('customer_email', $customer->email)->count(),
            'upcoming' => Booking::where('customer_email', $customer->email)
                ->where('start_datetime', '>', now())
                ->where('status', '!=', 'cancelled')
                ->count(),
            'completed' => Booking::where('customer_email', $customer->email)
                ->where('status', 'completed')
                ->count(),
            'cancelled' => Booking::where('customer_email', $customer->email)
                ->where('status', 'cancelled')
                ->count(),
        ];

        return view('customer.bookings.index', compact('bookings', 'stats', 'status'));
    }

    /**
     * Show the form for creating a new booking.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $businesses = Business::where('is_active', true)
            ->with(['services' => function($query) {
                $query->where('is_active', true);
            }])
            ->get();

        return view('customer.bookings.create', compact('businesses'));
    }

    /**
     * Store a newly created booking.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $validated = $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'service_ids' => 'required|array|min:1',
            'service_ids.*' => 'exists:services,id',
            'start_datetime' => 'required|date|after:now',
            'notes' => 'nullable|string|max:500',
            'special_requests' => 'nullable|string|max:500',
        ]);

        // Calculate total duration and amount
        $services = Service::whereIn('id', $validated['service_ids'])->get();
        $totalDuration = $services->sum('duration_minutes');
        $totalAmount = $services->sum('price');

        $booking = Booking::create([
            'business_id' => $validated['business_id'],
            'customer_name' => $customer->name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone ?? '',
            'start_datetime' => $validated['start_datetime'],
            'end_datetime' => Carbon::parse($validated['start_datetime'])->addMinutes($totalDuration),
            'total_duration_minutes' => $totalDuration,
            'subtotal' => $totalAmount,
            'total_amount' => $totalAmount,
            'status' => 'pending',
            'notes' => $validated['notes'],
            'special_requests' => $validated['special_requests'],
        ]);

        // Attach services to booking with pivot data
        $currentTime = Carbon::parse($validated['start_datetime']);
        foreach ($services as $service) {
            $booking->services()->attach($service->id, [
                'quantity' => 1,
                'unit_price' => $service->price,
                'total_price' => $service->price,
                'duration_minutes' => $service->duration_minutes,
                'start_datetime' => $currentTime,
                'end_datetime' => $currentTime->copy()->addMinutes($service->duration_minutes),
            ]);
            $currentTime->addMinutes($service->duration_minutes);
        }

        return redirect()->route('customer.bookings.show', $booking)
            ->with('success', 'Booking created successfully! We will confirm your appointment shortly.');
    }

    /**
     * Display the specified booking.
     *
     * @param Booking $booking
     * @return \Illuminate\Http\Response
     */
    public function show(Booking $booking)
    {
        $customer = $this->getCustomer();

        if (!$customer || $booking->customer_email !== $customer->email) {
            abort(403, 'Unauthorized access to booking.');
        }

        $booking->load(['services', 'business']);

        return view('customer.bookings.show', compact('booking'));
    }

    /**
     * Show the form for editing the specified booking.
     *
     * @param Booking $booking
     * @return \Illuminate\Http\Response
     */
    public function edit(Booking $booking)
    {
        $customer = $this->getCustomer();

        if (!$customer || $booking->customer_email !== $customer->email) {
            abort(403, 'Unauthorized access to booking.');
        }

        // Only allow editing of future bookings that are not confirmed
        if ($booking->start_datetime <= now() || $booking->status === 'confirmed') {
            return redirect()->route('customer.bookings.show', $booking)
                ->with('error', 'This booking cannot be edited.');
        }

        $booking->load(['services', 'business']);
        $businesses = Business::where('is_active', true)
            ->with(['services' => function($query) {
                $query->where('is_active', true);
            }])
            ->get();

        return view('customer.bookings.edit', compact('booking', 'businesses'));
    }

    /**
     * Update the specified booking.
     *
     * @param Request $request
     * @param Booking $booking
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, Booking $booking)
    {
        $customer = $this->getCustomer();

        if (!$customer || $booking->customer_email !== $customer->email) {
            abort(403, 'Unauthorized access to booking.');
        }

        // Only allow editing of future bookings that are not confirmed
        if ($booking->start_datetime <= now() || $booking->status === 'confirmed') {
            return redirect()->route('customer.bookings.show', $booking)
                ->with('error', 'This booking cannot be edited.');
        }

        $validated = $request->validate([
            'service_ids' => 'required|array|min:1',
            'service_ids.*' => 'exists:services,id',
            'start_datetime' => 'required|date|after:now',
            'notes' => 'nullable|string|max:500',
            'special_requests' => 'nullable|string|max:500',
        ]);

        // Calculate new total duration and amount
        $services = Service::whereIn('id', $validated['service_ids'])->get();
        $totalDuration = $services->sum('duration_minutes');
        $totalAmount = $services->sum('price');

        $booking->update([
            'start_datetime' => $validated['start_datetime'],
            'end_datetime' => Carbon::parse($validated['start_datetime'])->addMinutes($totalDuration),
            'total_duration_minutes' => $totalDuration,
            'subtotal' => $totalAmount,
            'total_amount' => $totalAmount,
            'notes' => $validated['notes'],
            'special_requests' => $validated['special_requests'],
            'status' => 'pending', // Reset to pending after modification
        ]);

        // Update services with pivot data
        $booking->services()->detach(); // Remove all existing services
        $currentTime = Carbon::parse($validated['start_datetime']);
        foreach ($services as $service) {
            $booking->services()->attach($service->id, [
                'quantity' => 1,
                'unit_price' => $service->price,
                'total_price' => $service->price,
                'duration_minutes' => $service->duration_minutes,
                'start_datetime' => $currentTime,
                'end_datetime' => $currentTime->copy()->addMinutes($service->duration_minutes),
            ]);
            $currentTime->addMinutes($service->duration_minutes);
        }

        return redirect()->route('customer.bookings.show', $booking)
            ->with('success', 'Booking updated successfully!');
    }

    /**
     * Cancel the specified booking.
     *
     * @param Booking $booking
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Booking $booking)
    {
        $customer = $this->getCustomer();

        if (!$customer || $booking->customer_email !== $customer->email) {
            abort(403, 'Unauthorized access to booking.');
        }

        // Check cancellation policy (e.g., 2 hours before)
        $cancellationHours = 2;
        if ($booking->start_datetime->diffInHours(now()) < $cancellationHours) {
            return redirect()->route('customer.bookings.show', $booking)
                ->with('error', "Bookings can only be cancelled at least {$cancellationHours} hours before the appointment.");
        }

        $booking->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => 'Cancelled by customer',
        ]);

        return redirect()->route('customer.bookings.index')
            ->with('success', 'Booking cancelled successfully.');
    }

    /**
     * Display booking history.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function history(Request $request)
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');
        $businessId = $request->get('business_id');

        $query = Booking::where('customer_email', $customer->email)
            ->with(['services', 'business']);

        // Date filtering
        if ($dateFrom) {
            $query->whereDate('start_datetime', '>=', $dateFrom);
        }
        if ($dateTo) {
            $query->whereDate('start_datetime', '<=', $dateTo);
        }

        // Business filtering
        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        $bookings = $query->orderBy('start_datetime', 'desc')->paginate(15);

        // Get businesses for filter dropdown
        $businesses = Business::whereHas('bookings', function($query) use ($customer) {
            $query->where('customer_email', $customer->email);
        })->get();

        // Calculate summary statistics
        $summary = [
            'total_bookings' => $query->count(),
            'total_spent' => $query->where('status', '!=', 'cancelled')->sum('total_amount'),
            'most_booked_business' => $this->getMostBookedBusiness($customer),
            'favorite_service' => $this->getFavoriteService($customer),
        ];

        return view('customer.bookings.history', compact('bookings', 'businesses', 'summary', 'dateFrom', 'dateTo', 'businessId'));
    }

    /**
     * Get available time slots for a service.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvailableSlots(Request $request)
    {
        $request->validate([
            'business_id' => 'required|exists:businesses,id',
            'service_ids' => 'required|array',
            'date' => 'required|date|after:today',
        ]);

        $business = Business::findOrFail($request->business_id);
        $services = Service::whereIn('id', $request->service_ids)->get();
        $totalDuration = $services->sum('duration_minutes');
        $date = Carbon::parse($request->date);

        // Get business operating hours for the day
        $dayOfWeek = $date->dayOfWeek;
        $operatingHours = $business->operatingHours()
            ->where('day_of_week', $dayOfWeek)
            ->where('is_open', true)
            ->first();

        if (!$operatingHours) {
            return response()->json(['slots' => []]);
        }

        // Generate time slots
        $slots = [];
        $startTime = Carbon::parse($date->format('Y-m-d') . ' ' . $operatingHours->open_time);
        $endTime = Carbon::parse($date->format('Y-m-d') . ' ' . $operatingHours->close_time);

        while ($startTime->addMinutes(30)->lte($endTime->subMinutes($totalDuration))) {
            // Check if slot is available (no conflicts with existing bookings)
            $isAvailable = !Booking::where('business_id', $business->id)
                ->where('status', '!=', 'cancelled')
                ->where(function($query) use ($startTime, $totalDuration) {
                    $slotEnd = $startTime->copy()->addMinutes($totalDuration);
                    $query->whereBetween('start_datetime', [$startTime, $slotEnd])
                        ->orWhereBetween('end_datetime', [$startTime, $slotEnd])
                        ->orWhere(function($q) use ($startTime, $slotEnd) {
                            $q->where('start_datetime', '<=', $startTime)
                              ->where('end_datetime', '>=', $slotEnd);
                        });
                })
                ->exists();

            if ($isAvailable) {
                $slots[] = [
                    'time' => $startTime->format('H:i'),
                    'display' => $startTime->format('g:i A'),
                ];
            }
        }

        return response()->json(['slots' => $slots]);
    }

    /**
     * Get most booked business for customer.
     *
     * @param \App\Models\User $customer
     * @return string|null
     */
    private function getMostBookedBusiness($customer)
    {
        $result = Booking::where('customer_email', $customer->email)
            ->select('business_id', DB::raw('count(*) as booking_count'))
            ->groupBy('business_id')
            ->orderBy('booking_count', 'desc')
            ->with('business')
            ->first();

        return $result ? $result->business->name : null;
    }

    /**
     * Get favorite service for customer.
     *
     * @param \App\Models\User $customer
     * @return string|null
     */
    private function getFavoriteService($customer)
    {
        $result = DB::table('booking_services')
            ->join('bookings', 'booking_services.booking_id', '=', 'bookings.id')
            ->join('services', 'booking_services.service_id', '=', 'services.id')
            ->where('bookings.customer_email', $customer->email)
            ->select('services.name', DB::raw('count(*) as service_count'))
            ->groupBy('services.id', 'services.name')
            ->orderBy('service_count', 'desc')
            ->first();

        return $result ? $result->name : null;
    }
}
