<?php

namespace App\Http\Controllers\Customer;

use Illuminate\Http\Request;
use App\Models\Service;
use App\Models\CustomerFavorite;
use Illuminate\Http\JsonResponse;

class FavoriteController extends Controller
{
    /**
     * Display customer's favorite services.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return redirect()->route('login');
        }

        $favorites = $customer->favoriteServices()
            ->with(['category', 'business'])
            ->paginate(12);

        return view('customer.favorites.index', compact('favorites'));
    }

    /**
     * Toggle favorite status for a service.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function toggle(Request $request): JsonResponse
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'service_id' => 'required|exists:services,id'
        ]);

        $serviceId = $request->get('service_id');

        // Check if service is active and available for booking
        $service = Service::where('id', $serviceId)
            ->where('is_active', true)
            ->where('online_booking_enabled', true)
            ->first();

        if (!$service) {
            return response()->json([
                'error' => 'Service not found or not available for booking'
            ], 404);
        }

        $result = $customer->toggleFavorite($serviceId);

        return response()->json([
            'success' => true,
            'favorited' => $result['favorited'],
            'action' => $result['action'],
            'message' => $result['action'] === 'added'
                ? 'Service added to favorites'
                : 'Service removed from favorites',
            'favorites_count' => $customer->favoriteServices()->count()
        ]);
    }

    /**
     * Add a service to favorites.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'service_id' => 'required|exists:services,id'
        ]);

        $serviceId = $request->get('service_id');

        // Check if service is active and available for booking
        $service = Service::where('id', $serviceId)
            ->where('is_active', true)
            ->where('online_booking_enabled', true)
            ->first();

        if (!$service) {
            return response()->json([
                'error' => 'Service not found or not available for booking'
            ], 404);
        }

        if ($customer->hasFavorited($serviceId)) {
            return response()->json([
                'error' => 'Service is already in favorites'
            ], 409);
        }

        $customer->addToFavorites($serviceId);

        return response()->json([
            'success' => true,
            'message' => 'Service added to favorites',
            'favorites_count' => $customer->favoriteServices()->count()
        ]);
    }

    /**
     * Remove a service from favorites.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function destroy(Request $request): JsonResponse
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'service_id' => 'required|exists:services,id'
        ]);

        $serviceId = $request->get('service_id');

        if (!$customer->hasFavorited($serviceId)) {
            return response()->json([
                'error' => 'Service is not in favorites'
            ], 404);
        }

        $customer->removeFromFavorites($serviceId);

        return response()->json([
            'success' => true,
            'message' => 'Service removed from favorites',
            'favorites_count' => $customer->favoriteServices()->count()
        ]);
    }

    /**
     * Check if a service is favorited by the customer.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function check(Request $request): JsonResponse
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $request->validate([
            'service_id' => 'required|exists:services,id'
        ]);

        $serviceId = $request->get('service_id');
        $isFavorited = $customer->hasFavorited($serviceId);

        return response()->json([
            'favorited' => $isFavorited,
            'favorites_count' => $customer->favoriteServices()->count()
        ]);
    }

    /**
     * Get customer's favorites count.
     *
     * @return JsonResponse
     */
    public function count(): JsonResponse
    {
        $customer = $this->getCustomer();

        if (!$customer) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return response()->json([
            'favorites_count' => $customer->favoriteServices()->count()
        ]);
    }
}
