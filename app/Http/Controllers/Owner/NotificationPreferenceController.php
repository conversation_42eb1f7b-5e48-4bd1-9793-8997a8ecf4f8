<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\OwnerNotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationPreferenceController extends Controller
{
    /**
     * Display notification preferences.
     */
    public function index()
    {
        $business = $this->getUserBusiness();
        
        // Get or create default preferences for all notification types
        $preferences = collect(OwnerNotificationPreference::getNotificationTypes())
            ->map(function ($label, $type) use ($business) {
                return OwnerNotificationPreference::firstOrCreate([
                    'owner_id' => auth()->id(),
                    'business_id' => $business->id,
                    'notification_type' => $type
                ], array_merge(
                    OwnerNotificationPreference::getDefaultPreferences(),
                    ['notification_type' => $type]
                ));
            });

        return view('owner.notifications.preferences', [
            'preferences' => $preferences,
            'notificationTypes' => OwnerNotificationPreference::getNotificationTypes(),
            'business' => $business
        ]);
    }

    /**
     * Update notification preferences.
     */
    public function update(Request $request)
    {
        $business = $this->getUserBusiness();
        
        // Check if this is a direct save request
        if ($request->has('direct')) {
            // For direct save, we'll just save the current state from the database
            // This is a simplified approach that just keeps the current settings
            try {
                // Begin a database transaction
                \DB::beginTransaction();
                
                // Get current preferences
                $currentPreferences = OwnerNotificationPreference::where('owner_id', auth()->id())
                    ->where('business_id', $business->id)
                    ->get();
                
                // If we don't have any preferences yet, create defaults
                if ($currentPreferences->isEmpty()) {
                    OwnerNotificationPreference::createDefaultPreferences(auth()->id(), $business->id);
                }
                
                // Commit the transaction
                \DB::commit();
                
                return redirect()->route('owner.notification-preferences.index')
                    ->with('success', 'Notification preferences saved successfully');
                
            } catch (\Exception $e) {
                // Rollback the transaction on error
                \DB::rollBack();
                
                \Log::error('Failed to save notification preferences directly', [
                    'owner_id' => auth()->id(),
                    'business_id' => $business->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return redirect()->back()->with('error', 'Failed to save preferences: ' . $e->getMessage());
            }
        }
        
        // Log the incoming request data for debugging
        \Log::debug('Notification preferences update request', [
            'content_type' => $request->header('Content-Type'),
            'raw_content' => $request->getContent(),
            'all_data' => $request->all()
        ]);
        
        // Get the preferences data, handling both JSON and form data
        $preferences = null;
        
        // Check if we have JSON data
        if ($request->isJson()) {
            $data = $request->json()->all();
            $preferences = $data['preferences'] ?? null;
        } 
        // Check if we have form-encoded data with JSON string
        else if ($request->has('preferences')) {
            $preferencesInput = $request->input('preferences');
            if (is_string($preferencesInput)) {
                // Try to decode the JSON string
                try {
                    $preferences = json_decode($preferencesInput, true);
                } catch (\Exception $e) {
                    \Log::error('Failed to decode preferences JSON', [
                        'error' => $e->getMessage(),
                        'input' => $preferencesInput
                    ]);
                }
            } else {
                $preferences = $preferencesInput;
            }
        }
        
        if (!$preferences) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No preferences data provided'
                ], 422);
            }
            return redirect()->back()->with('error', 'No preferences data provided');
        }
        
        // Validate the preferences data
        $validator = Validator::make(['preferences' => $preferences], [
            'preferences' => 'required|array',
            'preferences.*.notification_type' => 'required|string',
            'preferences.*.email_enabled' => 'boolean',
            'preferences.*.sms_enabled' => 'boolean',
            'preferences.*.push_enabled' => 'boolean',
            'preferences.*.in_app_enabled' => 'boolean',
            'preferences.*.sound_enabled' => 'boolean',
            'preferences.*.priority_filter' => 'nullable|in:low,normal,high,urgent',
            'preferences.*.quiet_hours_start' => 'nullable|date_format:H:i',
            'preferences.*.quiet_hours_end' => 'nullable|date_format:H:i',
            'preferences.*.weekend_notifications' => 'boolean',
            'preferences.*.digest_frequency' => 'nullable|in:never,daily,weekly,monthly',
            'preferences.*.auto_mark_read' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Begin a database transaction
            \DB::beginTransaction();
            
            foreach ($preferences as $preferenceData) {
                OwnerNotificationPreference::updateOrCreate([
                    'owner_id' => auth()->id(),
                    'business_id' => $business->id,
                    'notification_type' => $preferenceData['notification_type']
                ], [
                    'email_enabled' => $preferenceData['email_enabled'] ?? false,
                    'sms_enabled' => $preferenceData['sms_enabled'] ?? false,
                    'push_enabled' => $preferenceData['push_enabled'] ?? false,
                    'in_app_enabled' => $preferenceData['in_app_enabled'] ?? false,
                    'sound_enabled' => $preferenceData['sound_enabled'] ?? false,
                    'priority_filter' => $preferenceData['priority_filter'] ?? null,
                    'quiet_hours_start' => $preferenceData['quiet_hours_start'] ?? null,
                    'quiet_hours_end' => $preferenceData['quiet_hours_end'] ?? null,
                    'weekend_notifications' => $preferenceData['weekend_notifications'] ?? true,
                    'digest_frequency' => $preferenceData['digest_frequency'] ?? 'daily',
                    'auto_mark_read' => $preferenceData['auto_mark_read'] ?? false,
                ]);
            }
            
            // Commit the transaction
            \DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification preferences updated successfully'
                ]);
            }
            
            return redirect()->route('owner.notification-preferences.index')
                ->with('success', 'Notification preferences updated successfully');

        } catch (\Exception $e) {
            // Rollback the transaction on error
            \DB::rollBack();
            
            \Log::error('Failed to update notification preferences', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification preferences: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset preferences to defaults.
     */
    public function reset(Request $request)
    {
        $business = $this->getUserBusiness();
        
        try {
            // Delete existing preferences
            OwnerNotificationPreference::where('owner_id', auth()->id())
                ->where('business_id', $business->id)
                ->delete();

            // Create default preferences
            OwnerNotificationPreference::createDefaultPreferences(auth()->id(), $business->id);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Notification preferences reset to defaults'
                ]);
            }
            
            return redirect()->route('owner.notification-preferences.index')
                ->with('success', 'Notification preferences reset to defaults');

        } catch (\Exception $e) {
            \Log::error('Failed to reset notification preferences', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to reset notification preferences'
            ], 500);
        }
    }

    /**
     * Update global notification settings.
     */
    public function updateGlobalSettings(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $validator = Validator::make($request->all(), [
            'global_email_enabled' => 'boolean',
            'global_sms_enabled' => 'boolean',
            'global_push_enabled' => 'boolean',
            'global_sound_enabled' => 'boolean',
            'global_quiet_hours_start' => 'nullable|date_format:H:i',
            'global_quiet_hours_end' => 'nullable|date_format:H:i',
            'global_weekend_notifications' => 'boolean',
            'global_digest_frequency' => 'required|in:never,daily,weekly,monthly',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Update all preferences for this owner/business
            OwnerNotificationPreference::where('owner_id', auth()->id())
                ->where('business_id', $business->id)
                ->update([
                    'email_enabled' => $request->global_email_enabled ?? false,
                    'sms_enabled' => $request->global_sms_enabled ?? false,
                    'push_enabled' => $request->global_push_enabled ?? false,
                    'sound_enabled' => $request->global_sound_enabled ?? false,
                    'quiet_hours_start' => $request->global_quiet_hours_start,
                    'quiet_hours_end' => $request->global_quiet_hours_end,
                    'weekend_notifications' => $request->global_weekend_notifications ?? true,
                    'digest_frequency' => $request->global_digest_frequency ?? 'daily',
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Global notification settings updated successfully'
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to update global notification settings', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update global notification settings'
            ], 500);
        }
    }

    /**
     * Test notification delivery.
     */
    public function testNotification(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $validator = Validator::make($request->all(), [
            'notification_type' => 'required|string',
            'channels' => 'required|array',
            'channels.*' => 'in:email,sms,push,in_app'
        ]);

        if ($validator->fails()) {
            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->with('error', 'Please select a notification type and at least one channel.');
        }

        try {
            // Create a test notification
            $testNotification = [
                'title' => 'Test Notification',
                'message' => 'This is a test notification to verify your settings are working correctly.',
                'type' => $request->notification_type,
                'priority' => 'normal',
                'data' => [
                    'test' => true,
                    'timestamp' => now()->toISOString()
                ]
            ];

            // Send test notification through requested channels
            $results = [];
            foreach ($request->channels as $channel) {
                $results[$channel] = $this->sendTestNotification($testNotification, $channel, $business);
            }

            // Log the test notification
            \Log::info('Test notification sent', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'notification_type' => $request->notification_type,
                'channels' => $request->channels
            ]);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Test notification sent successfully',
                    'results' => $results
                ]);
            }
            
            return redirect()->route('owner.notification-preferences.index')
                ->with('success', 'Test notification sent successfully!');

        } catch (\Exception $e) {
            \Log::error('Failed to send test notification', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to send test notification'
                ], 500);
            }
            
            return redirect()->back()->with('error', 'Failed to send test notification: ' . $e->getMessage());
        }
    }

    /**
     * Send test notification through specific channel.
     */
    private function sendTestNotification($notification, $channel, $business)
    {
        switch ($channel) {
            case 'email':
                // Send test email notification
                return ['status' => 'sent', 'message' => 'Test email sent'];
                
            case 'sms':
                // Send test SMS notification
                return ['status' => 'sent', 'message' => 'Test SMS sent'];
                
            case 'push':
                // Send test push notification
                return ['status' => 'sent', 'message' => 'Test push notification sent'];
                
            case 'in_app':
                // Create in-app notification
                app(\App\Services\OwnerNotificationService::class)->createNotification(
                    $business->id,
                    auth()->id(),
                    $notification['type'],
                    $notification['title'],
                    $notification['message'],
                    $notification['data'],
                    $notification['priority']
                );
                return ['status' => 'sent', 'message' => 'Test in-app notification created'];
                
            default:
                return ['status' => 'error', 'message' => 'Unknown channel'];
        }
    }

    /**
     * Get notification statistics.
     */
    public function getStats(Request $request)
    {
        $business = $this->getUserBusiness();
        
        try {
            $stats = [
                'total_preferences' => OwnerNotificationPreference::where('owner_id', auth()->id())
                    ->where('business_id', $business->id)
                    ->count(),
                'enabled_channels' => [
                    'email' => OwnerNotificationPreference::where('owner_id', auth()->id())
                        ->where('business_id', $business->id)
                        ->where('email_enabled', true)
                        ->count(),
                    'sms' => OwnerNotificationPreference::where('owner_id', auth()->id())
                        ->where('business_id', $business->id)
                        ->where('sms_enabled', true)
                        ->count(),
                    'push' => OwnerNotificationPreference::where('owner_id', auth()->id())
                        ->where('business_id', $business->id)
                        ->where('push_enabled', true)
                        ->count(),
                    'in_app' => OwnerNotificationPreference::where('owner_id', auth()->id())
                        ->where('business_id', $business->id)
                        ->where('in_app_enabled', true)
                        ->count(),
                ],
                'quiet_hours_configured' => OwnerNotificationPreference::where('owner_id', auth()->id())
                    ->where('business_id', $business->id)
                    ->whereNotNull('quiet_hours_start')
                    ->whereNotNull('quiet_hours_end')
                    ->count(),
                'priority_filters' => OwnerNotificationPreference::where('owner_id', auth()->id())
                    ->where('business_id', $business->id)
                    ->whereNotNull('priority_filter')
                    ->count(),
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get notification preference stats', [
                'owner_id' => auth()->id(),
                'business_id' => $business->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get notification statistics'
            ], 500);
        }
    }
}
