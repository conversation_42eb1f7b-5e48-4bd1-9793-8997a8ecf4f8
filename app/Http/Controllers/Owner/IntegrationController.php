<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Services\BookingIntegrationService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class IntegrationController extends Controller
{
    protected $integrationService;

    public function __construct(BookingIntegrationService $integrationService)
    {
        $this->integrationService = $integrationService;
    }

    /**
     * Get cross-section notifications
     */
    public function getNotifications(Request $request)
    {
        $business = $this->getUserBusiness();
        $notifications = $this->integrationService->getCrossSectionNotifications($business);

        return response()->json([
            'success' => true,
            'notifications' => $notifications
        ]);
    }

    /**
     * Get integrated dashboard stats
     */
    public function getDashboardStats(Request $request)
    {
        $business = $this->getUserBusiness();
        $date = $request->get('date') ? Carbon::parse($request->get('date')) : null;
        
        $stats = $this->integrationService->getDashboardStats($business, $date);

        return response()->json([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * Get related data for cross-section display
     */
    public function getRelatedData(Request $request, string $section, $id)
    {
        $business = $this->getUserBusiness();
        $relatedData = $this->integrationService->getRelatedData($section, $id, $business);

        return response()->json([
            'success' => true,
            'related_data' => $relatedData
        ]);
    }

    /**
     * Handle booking status change with integration
     */
    public function handleBookingStatusChange(Request $request)
    {
        $business = $this->getUserBusiness();
        
        $request->validate([
            'booking_id' => 'required|exists:bookings,id',
            'old_status' => 'required|string',
            'new_status' => 'required|string',
        ]);

        $booking = $business->bookings()->findOrFail($request->booking_id);
        
        $result = $this->integrationService->handleBookingStatusChange(
            $booking,
            $request->old_status,
            $request->new_status
        );

        return response()->json($result);
    }

    /**
     * Get quick actions for a specific section
     */
    public function getQuickActions(Request $request, string $section)
    {
        $business = $this->getUserBusiness();
        $actions = [];

        switch ($section) {
            case 'bookings':
                $actions = $this->getBookingQuickActions($business);
                break;
            case 'check-in':
                $actions = $this->getCheckInQuickActions($business);
                break;
            case 'waiting-lists':
                $actions = $this->getWaitingListQuickActions($business);
                break;
        }

        return response()->json([
            'success' => true,
            'actions' => $actions
        ]);
    }

    /**
     * Get booking quick actions
     */
    protected function getBookingQuickActions($business): array
    {
        $actions = [];

        // Check for customers ready to check in
        $readyToCheckIn = $business->bookings()
            ->where('status', 'confirmed')
            ->whereNull('checked_in_at')
            ->whereDate('start_datetime', now())
            ->where('start_datetime', '<=', now()->addMinutes(15))
            ->count();

        if ($readyToCheckIn > 0) {
            $actions[] = [
                'type' => 'check_in_ready',
                'title' => 'Customers Ready for Check-in',
                'description' => "{$readyToCheckIn} customers are ready to check in",
                'count' => $readyToCheckIn,
                'url' => route('owner.check-in.index'),
                'icon' => 'fas fa-sign-in-alt',
                'color' => 'info'
            ];
        }

        // Check for overdue bookings
        $overdueBookings = $business->bookings()
            ->where('status', 'confirmed')
            ->where('start_datetime', '<', now()->subMinutes(15))
            ->whereNull('checked_in_at')
            ->count();

        if ($overdueBookings > 0) {
            $actions[] = [
                'type' => 'overdue_bookings',
                'title' => 'Overdue Bookings',
                'description' => "{$overdueBookings} bookings are overdue",
                'count' => $overdueBookings,
                'url' => route('owner.bookings.index', ['status' => 'confirmed', 'overdue' => 1]),
                'icon' => 'fas fa-exclamation-triangle',
                'color' => 'warning'
            ];
        }

        return $actions;
    }

    /**
     * Get check-in quick actions
     */
    protected function getCheckInQuickActions($business): array
    {
        $actions = [];

        // Check for waiting list opportunities
        $cancelledToday = $business->bookings()
            ->whereDate('start_datetime', now())
            ->where('status', 'cancelled')
            ->count();

        if ($cancelledToday > 0) {
            $actions[] = [
                'type' => 'cancelled_slots',
                'title' => 'Available Slots from Cancellations',
                'description' => "{$cancelledToday} slots available from today's cancellations",
                'count' => $cancelledToday,
                'url' => route('owner.waiting-lists.index'),
                'icon' => 'fas fa-calendar-plus',
                'color' => 'success'
            ];
        }

        return $actions;
    }

    /**
     * Get waiting list quick actions
     */
    protected function getWaitingListQuickActions($business): array
    {
        $actions = [];

        // Check for auto-match opportunities
        $activeWaitingLists = $business->waitingLists()
            ->where('status', 'active')
            ->count();

        if ($activeWaitingLists > 0) {
            $actions[] = [
                'type' => 'auto_match',
                'title' => 'Auto-Match Available',
                'description' => "Find matches for {$activeWaitingLists} waiting list entries",
                'count' => $activeWaitingLists,
                'url' => '#',
                'action' => 'auto-match',
                'icon' => 'fas fa-magic',
                'color' => 'primary'
            ];
        }

        // Check for expired notifications
        $expiredNotifications = $business->waitingLists()
            ->where('status', 'notified')
            ->where('expires_at', '<', now())
            ->count();

        if ($expiredNotifications > 0) {
            $actions[] = [
                'type' => 'expired_notifications',
                'title' => 'Expired Notifications',
                'description' => "{$expiredNotifications} notifications have expired",
                'count' => $expiredNotifications,
                'url' => route('owner.waiting-lists.index', ['status' => 'notified']),
                'icon' => 'fas fa-clock',
                'color' => 'danger'
            ];
        }

        return $actions;
    }

    /**
     * Get cross-section summary for dashboard
     */
    public function getCrossSectionSummary(Request $request)
    {
        $business = $this->getUserBusiness();
        $date = $request->get('date') ? Carbon::parse($request->get('date')) : now();

        $summary = [
            'bookings' => [
                'today_total' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->where('status', '!=', 'cancelled')
                    ->count(),
                'checked_in' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->whereNotNull('checked_in_at')
                    ->whereNull('checked_out_at')
                    ->count(),
                'completed' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->where('status', 'completed')
                    ->count(),
            ],
            'waiting_lists' => [
                'active' => $business->waitingLists()
                    ->where('status', 'active')
                    ->count(),
                'urgent' => $business->waitingLists()
                    ->where('status', 'active')
                    ->where('priority', '>=', 8)
                    ->count(),
                'converted_today' => $business->waitingLists()
                    ->where('status', 'booked')
                    ->whereDate('updated_at', $date)
                    ->count(),
            ],
            'opportunities' => [
                'available_slots' => $business->bookings()
                    ->whereDate('start_datetime', $date)
                    ->whereIn('status', ['cancelled', 'no_show'])
                    ->count(),
                'ready_to_check_in' => $business->bookings()
                    ->where('status', 'confirmed')
                    ->whereNull('checked_in_at')
                    ->whereDate('start_datetime', $date)
                    ->where('start_datetime', '<=', now()->addMinutes(15))
                    ->count(),
            ]
        ];

        return response()->json([
            'success' => true,
            'summary' => $summary,
            'date' => $date->format('Y-m-d')
        ]);
    }
}
