<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\ResourceType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class ResourceTypeController extends Controller
{
    /**
     * Display a listing of resource types.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();

        // Get resource types for this business only
        $resourceTypes = $business->resourceTypes()
            ->withCount('resources')
            ->when($request->get('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('name')
            ->paginate(12);

        // Get statistics
        $stats = $this->getResourceTypeStats($business);

        return view('owner.resource-types.index', compact('resourceTypes', 'stats'));
    }

    /**
     * Store a newly created resource type.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('resource_types')->where(function ($query) use ($business) {
                    return $query->where('business_id', $business->id);
                }),
            ],
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
        ], [
            'name.unique' => 'A resource type with this name already exists in your business.',
            'color.regex' => 'Color must be a valid hex color (e.g., #28a745).',
        ]);

        // Auto-generate slug unique within business
        $slug = Str::slug($request->name);
        $originalSlug = $slug;
        $counter = 1;
        while (ResourceType::where('business_id', $business->id)->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $resourceType = ResourceType::create([
            'business_id' => $business->id,
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'icon' => $request->icon ?: 'fas fa-cube',
            'color' => $request->color ?: '#007bff',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Resource type created successfully.',
            'resource_type' => $resourceType,
        ]);
    }

    /**
     * Update the specified resource type.
     */
    public function update(Request $request, ResourceType $resourceType)
    {
        $business = $this->getUserBusiness();

        // Verify resource type belongs to business
        if ($resourceType->business_id !== $business->id) {
            abort(403, 'Unauthorized access to resource type.');
        }

        $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('resource_types')->where(function ($query) use ($business) {
                    return $query->where('business_id', $business->id);
                })->ignore($resourceType->id),
            ],
            'description' => 'nullable|string|max:1000',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
        ], [
            'name.unique' => 'A resource type with this name already exists in your business.',
            'color.regex' => 'Color must be a valid hex color (e.g., #28a745).',
        ]);

        // Update slug if name changed
        $slug = $resourceType->slug;
        if ($resourceType->name !== $request->name) {
            $slug = Str::slug($request->name);
            $originalSlug = $slug;
            $counter = 1;
            while (ResourceType::where('business_id', $business->id)->where('slug', $slug)->where('id', '!=', $resourceType->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
        }

        $resourceType->update([
            'name' => $request->name,
            'slug' => $slug,
            'description' => $request->description,
            'icon' => $request->icon ?: 'fas fa-cube',
            'color' => $request->color ?: '#007bff',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Resource type updated successfully.',
            'resource_type' => $resourceType,
        ]);
    }

    /**
     * Remove the specified resource type from storage.
     */
    public function destroy(ResourceType $resourceType)
    {
        $business = $this->getUserBusiness();

        // Verify resource type belongs to business
        if ($resourceType->business_id !== $business->id) {
            abort(403, 'Unauthorized access to resource type.');
        }

        // Check if resource type has resources
        $resourceCount = $resourceType->resources()->count();

        if ($resourceCount > 0) {
            return response()->json([
                'success' => false,
                'message' => "Cannot delete resource type. It has {$resourceCount} resource(s).",
            ], 422);
        }

        try {
            DB::transaction(function () use ($resourceType) {
                // Additional safety check for any related data
                $hasRelatedData = $this->checkForRelatedData($resourceType);

                if ($hasRelatedData['has_data']) {
                    throw new \Exception($hasRelatedData['message']);
                }

                // Perform the deletion
                $deleted = $resourceType->delete();

                if (!$deleted) {
                    throw new \Exception('Failed to delete resource type from database.');
                }
            });

            // Log successful deletion
            Log::info('Resource type deleted successfully', [
                'resource_type_id' => $resourceType->id,
                'resource_type_name' => $resourceType->name,
                'business_id' => $business->id,
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Resource type deleted successfully.',
            ]);

        } catch (\Exception $e) {
            // Log the error
            Log::error('Failed to delete resource type', [
                'resource_type_id' => $resourceType->id,
                'resource_type_name' => $resourceType->name,
                'business_id' => $business->id,
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Could not delete resource type. Please try again.',
                'debug_message' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Check for any related data that would prevent deletion.
     */
    protected function checkForRelatedData(ResourceType $resourceType)
    {
        // Check for resources (already checked in main method, but double-check)
        $resourceCount = $resourceType->resources()->count();
        if ($resourceCount > 0) {
            return [
                'has_data' => true,
                'message' => "Resource type has {$resourceCount} associated resource(s)."
            ];
        }

        // Check for any service-resource relationships through resources
        $serviceResourceCount = DB::table('service_resources')
            ->whereIn('resource_id', function($query) use ($resourceType) {
                $query->select('id')
                      ->from('resources')
                      ->where('resource_type_id', $resourceType->id);
            })
            ->count();

        if ($serviceResourceCount > 0) {
            return [
                'has_data' => true,
                'message' => 'Resource type has resources that are linked to services.'
            ];
        }

        // Check for any booking-service-resource relationships
        $bookingResourceCount = DB::table('booking_service_resources')
            ->whereIn('resource_id', function($query) use ($resourceType) {
                $query->select('id')
                      ->from('resources')
                      ->where('resource_type_id', $resourceType->id);
            })
            ->count();

        if ($bookingResourceCount > 0) {
            return [
                'has_data' => true,
                'message' => 'Resource type has resources with existing bookings.'
            ];
        }

        return [
            'has_data' => false,
            'message' => 'No related data found.'
        ];
    }

    /**
     * Get resource type statistics for the business.
     */
    protected function getResourceTypeStats($business)
    {
        $totalTypes = $business->resourceTypes()->count();
        $usedTypes = $business->resourceTypes()->whereHas('resources')->count();

        $totalResources = $business->resources()->count();
        $activeResources = $business->resources()->where('is_active', true)->count();

        return [
            'total_types' => $totalTypes,
            'used_types' => $usedTypes,
            'unused_types' => $totalTypes - $usedTypes,
            'total_resources' => $totalResources,
            'active_resources' => $activeResources,
        ];
    }

    /**
     * Get available icons for resource types.
     */
    public function getAvailableIcons()
    {
        $icons = [
            // Furniture & Equipment
            'fas fa-chair' => 'Chair',
            'fas fa-couch' => 'Couch',
            'fas fa-bed' => 'Bed',
            'fas fa-desk' => 'Desk',
            'fas fa-table' => 'Table',

            // Tools & Equipment
            'fas fa-tools' => 'Tools',
            'fas fa-wrench' => 'Wrench',
            'fas fa-hammer' => 'Hammer',
            'fas fa-screwdriver' => 'Screwdriver',
            'fas fa-cogs' => 'Gears',

            // Medical & Health
            'fas fa-stethoscope' => 'Stethoscope',
            'fas fa-syringe' => 'Syringe',
            'fas fa-pills' => 'Pills',
            'fas fa-thermometer' => 'Thermometer',
            'fas fa-heartbeat' => 'Heartbeat',

            // Beauty & Salon
            'fas fa-cut' => 'Scissors',
            'fas fa-spray-can' => 'Spray',
            'fas fa-mirror' => 'Mirror',
            'fas fa-palette' => 'Palette',

            // Rooms & Spaces
            'fas fa-door-open' => 'Room',
            'fas fa-home' => 'House',
            'fas fa-building' => 'Building',
            'fas fa-warehouse' => 'Warehouse',

            // Vehicles
            'fas fa-car' => 'Car',
            'fas fa-truck' => 'Truck',
            'fas fa-motorcycle' => 'Motorcycle',
            'fas fa-bicycle' => 'Bicycle',

            // Technology
            'fas fa-laptop' => 'Laptop',
            'fas fa-desktop' => 'Desktop',
            'fas fa-tablet' => 'Tablet',
            'fas fa-mobile' => 'Mobile',
            'fas fa-camera' => 'Camera',

            // Sports & Fitness
            'fas fa-dumbbell' => 'Dumbbell',
            'fas fa-running' => 'Running',
            'fas fa-swimmer' => 'Swimming',
            'fas fa-bicycle' => 'Cycling',

            // General
            'fas fa-cube' => 'Cube',
            'fas fa-box' => 'Box',
            'fas fa-archive' => 'Archive',
            'fas fa-clipboard' => 'Clipboard',
            'fas fa-bookmark' => 'Bookmark',
        ];

        return response()->json($icons);
    }

    /**
     * Get predefined colors for resource types.
     */
    public function getPredefinedColors()
    {
        $colors = [
            '#007bff' => 'Blue',
            '#28a745' => 'Green',
            '#dc3545' => 'Red',
            '#ffc107' => 'Yellow',
            '#17a2b8' => 'Cyan',
            '#6f42c1' => 'Purple',
            '#e83e8c' => 'Pink',
            '#fd7e14' => 'Orange',
            '#20c997' => 'Teal',
            '#6c757d' => 'Gray',
            '#343a40' => 'Dark',
            '#f8f9fa' => 'Light',
        ];

        return response()->json($colors);
    }

    /**
     * Get resource types for dropdown/select options.
     */
    public function getForSelect(Request $request)
    {
        $business = $this->getUserBusiness();

        $resourceTypes = $business->resourceTypes()
            ->withCount('resources')
            ->orderBy('name')
            ->get()
            ->map(function ($type) {
                return [
                    'id' => $type->id,
                    'name' => $type->name,
                    'slug' => $type->slug,
                    'icon' => $type->icon,
                    'color' => $type->color,
                    'resources_count' => $type->resources_count,
                ];
            });

        return response()->json($resourceTypes);
    }

    /**
     * Get usage analytics for resource types.
     */
    public function getUsageAnalytics(Request $request)
    {
        $business = $this->getUserBusiness();

        $analytics = $business->resourceTypes()
            ->withCount('resources')
            ->get()
            ->map(function ($type) {
                $totalBookings = $type->resources()
                    ->withCount('bookingServiceResources')
                    ->get()
                    ->sum('booking_service_resources_count');

                return [
                    'name' => $type->name,
                    'resources_count' => $type->resources_count,
                    'total_bookings' => $totalBookings,
                    'color' => $type->color,
                ];
            });

        return response()->json($analytics);
    }

    /**
     * Export resource types to CSV.
     */
    public function export(Request $request)
    {
        $business = $this->getUserBusiness();

        $resourceTypes = $business->resourceTypes()
            ->withCount('resources')
            ->when($request->get('search'), function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('name')
            ->get();

        $filename = 'resource_types_' . $business->slug . '_' . now()->format('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($resourceTypes) {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, [
                'Name',
                'Description',
                'Icon',
                'Color',
                'Resources Count',
                'Created Date'
            ]);

            // Add data rows
            foreach ($resourceTypes as $type) {
                fputcsv($file, [
                    $type->name,
                    $type->description ?: 'No description',
                    $type->icon,
                    $type->color,
                    $type->resources_count,
                    $type->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
