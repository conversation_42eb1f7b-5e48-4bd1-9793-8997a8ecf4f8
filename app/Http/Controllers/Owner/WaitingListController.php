<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\WaitingList;
use App\Models\Service;
use App\Models\User;
use App\Models\Booking;
use App\Services\BusinessRulesEngine;
use App\Services\BookingIntegrationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Activitylog\Facades\Activity;

class WaitingListController extends Controller
{
    protected $businessRulesEngine;
    protected $integrationService;

    public function __construct(BusinessRulesEngine $businessRulesEngine, BookingIntegrationService $integrationService)
    {
        $this->businessRulesEngine = $businessRulesEngine;
        $this->integrationService = $integrationService;
    }

    /**
     * Display the waiting lists dashboard.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();

        // Get waiting lists for this business
        $waitingListsQuery = $business->waitingLists()
            ->with(['service', 'customer']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->search;
            $waitingListsQuery->where(function ($query) use ($search) {
                $query->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('customer_email', 'like', "%{$search}%")
                      ->orWhere('customer_phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('service_id')) {
            $waitingListsQuery->where('service_id', $request->service_id);
        }

        if ($request->filled('status')) {
            $waitingListsQuery->where('status', $request->status);
        }

        if ($request->filled('priority')) {
            $priority = $request->priority;
            if ($priority === 'high') {
                $waitingListsQuery->where('priority', '>=', 7);
            } elseif ($priority === 'medium') {
                $waitingListsQuery->whereBetween('priority', [3, 6]);
            } elseif ($priority === 'low') {
                $waitingListsQuery->where('priority', '<=', 2);
            }
        }

        if ($request->filled('date_added')) {
            $waitingListsQuery->whereDate('created_at', $request->date_added);
        }

        $waitingLists = $waitingListsQuery->byPriority()->get();

        // Calculate statistics
        $stats = [
            'active' => $business->waitingLists()->where('status', 'active')->count(),
            'converted_today' => $business->waitingLists()
                ->where('status', 'booked')
                ->whereDate('updated_at', today())
                ->count(),
            'urgent' => $business->waitingLists()
                ->where('status', 'active')
                ->where('preferred_date', '<=', now()->addDay())
                ->count(),
            'expired' => $business->waitingLists()->where('status', 'expired')->count(),
        ];

        // Get services for filter dropdown
        $services = $business->services()->active()->get();

        return view('owner.waiting-lists.index', compact(
            'waitingLists',
            'stats',
            'services'
        ));
    }

    /**
     * Store a new waiting list entry.
     */
    public function store(Request $request)
    {
        $business = $this->getUserBusiness();

        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'service_id' => 'required|exists:services,id',
            'preferred_date' => 'required|date|after_or_equal:today',
            'preferred_time_start' => 'nullable|date_format:H:i',
            'preferred_time_end' => 'nullable|date_format:H:i|after:preferred_time_start',
            'preferred_days_of_week' => 'nullable|array',
            'preferred_days_of_week.*' => 'integer|between:0,6',
            'participant_count' => 'required|integer|min:1|max:10',
            'priority' => 'required|integer|min:0|max:10',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $validator->validated();

        // Verify service belongs to business
        $service = $business->services()->findOrFail($validated['service_id']);

        // Check if customer already exists
        $customer = User::where('email', $validated['customer_email'])->first();

        $waitingList = WaitingList::create([
            'business_id' => $business->id,
            'service_id' => $service->id,
            'customer_id' => $customer?->id,
            'customer_name' => $validated['customer_name'],
            'customer_email' => $validated['customer_email'],
            'customer_phone' => $validated['customer_phone'],
            'preferred_date' => $validated['preferred_date'],
            'preferred_time_start' => $validated['preferred_time_start'],
            'preferred_time_end' => $validated['preferred_time_end'],
            'preferred_days_of_week' => $validated['preferred_days_of_week'],
            'participant_count' => $validated['participant_count'],
            'priority' => $validated['priority'],
            'notes' => $validated['notes'],
            'status' => 'active',
        ]);

        // Log the activity
        activity()
            ->performedOn($waitingList)
            ->withProperties([
                'action' => 'created',
                'customer_name' => $validated['customer_name'],
                'service_name' => $service->name
            ])
            ->log('Customer added to waiting list');

        return response()->json([
            'success' => true,
            'message' => 'Customer added to waiting list successfully!',
            'waiting_list' => $waitingList->load(['service', 'customer'])
        ]);
    }

    /**
     * Update waiting list entry.
     */
    public function update(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $waitingList = $business->waitingLists()->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'service_id' => 'required|exists:services,id',
            'preferred_date' => 'required|date|after_or_equal:today',
            'preferred_time_start' => 'nullable|date_format:H:i',
            'preferred_time_end' => 'nullable|date_format:H:i|after:preferred_time_start',
            'preferred_days_of_week' => 'nullable|array',
            'preferred_days_of_week.*' => 'integer|between:0,6',
            'participant_count' => 'required|integer|min:1|max:10',
            'priority' => 'required|integer|min:0|max:10',
            'notes' => 'nullable|string|max:1000',
            'status' => 'required|in:active,notified,booked,expired,cancelled',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $validator->validated();

        // Verify service belongs to business
        $service = $business->services()->findOrFail($validated['service_id']);

        $waitingList->update($validated);

        // Log the activity
        activity()
            ->performedOn($waitingList)
            ->withProperties([
                'action' => 'updated',
                'changes' => $waitingList->getChanges()
            ])
            ->log('Waiting list entry updated');

        return response()->json([
            'success' => true,
            'message' => 'Waiting list entry updated successfully!',
            'waiting_list' => $waitingList->load(['service', 'customer'])
        ]);
    }

    /**
     * Delete waiting list entry.
     */
    public function destroy($id)
    {
        $business = $this->getUserBusiness();
        $waitingList = $business->waitingLists()->findOrFail($id);

        // Log the activity before deletion
        activity()
            ->performedOn($waitingList)
            ->withProperties([
                'action' => 'deleted',
                'customer_name' => $waitingList->customer_name,
                'service_name' => $waitingList->service->name
            ])
            ->log('Customer removed from waiting list');

        $waitingList->delete();

        return response()->json([
            'success' => true,
            'message' => 'Customer removed from waiting list successfully!'
        ]);
    }

    /**
     * Update priority order.
     */
    public function updatePriority(Request $request)
    {
        $business = $this->getUserBusiness();

        $validator = Validator::make($request->all(), [
            'waiting_list_ids' => 'required|array',
            'waiting_list_ids.*' => 'integer|exists:waiting_lists,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $waitingListIds = $request->waiting_list_ids;

        DB::beginTransaction();
        try {
            foreach ($waitingListIds as $index => $waitingListId) {
                $waitingList = $business->waitingLists()->find($waitingListId);
                if ($waitingList) {
                    $waitingList->update(['priority' => count($waitingListIds) - $index]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Priority order updated successfully!'
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update priority order.'
            ], 500);
        }
    }

    /**
     * Find matching available slots.
     */
    public function findMatches(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $waitingList = $business->waitingLists()->with('service')->findOrFail($id);

        $service = $waitingList->service;
        $preferredDate = Carbon::parse($waitingList->preferred_date);
        $searchDays = $request->get('search_days', 14); // Search next 14 days by default

        $availableSlots = [];

        // Search for available slots
        for ($i = 0; $i < $searchDays; $i++) {
            $checkDate = $preferredDate->copy()->addDays($i);

            // Skip if not in preferred days of week
            if ($waitingList->preferred_days_of_week &&
                !in_array($checkDate->dayOfWeek, $waitingList->preferred_days_of_week)) {
                continue;
            }

            // Get business operating hours for this day
            $operatingHours = $business->operatingHours()
                ->where('day_of_week', $checkDate->dayOfWeek)
                ->where('is_open', true)
                ->first();

            if (!$operatingHours) {
                continue;
            }

            // Generate time slots
            $startTime = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $operatingHours->open_time);
            $endTime = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $operatingHours->close_time);

            while ($startTime->copy()->addMinutes($service->duration_minutes)->lte($endTime)) {
                $slotEnd = $startTime->copy()->addMinutes($service->duration_minutes);

                // Check availability
                $availabilityCheck = $this->businessRulesEngine->checkAvailability(
                    $business,
                    $startTime,
                    $slotEnd,
                    [$service->id]
                );

                if ($availabilityCheck['available']) {
                    $availableSlots[] = [
                        'date' => $startTime->format('Y-m-d'),
                        'time' => $startTime->format('H:i'),
                        'datetime' => $startTime->format('Y-m-d H:i:s'),
                        'formatted_date' => $startTime->format('M j, Y'),
                        'formatted_time' => $startTime->format('g:i A'),
                        'day_name' => $startTime->format('l'),
                    ];
                }

                $startTime->addMinutes(30); // 30-minute intervals
            }
        }

        return response()->json([
            'success' => true,
            'available_slots' => array_slice($availableSlots, 0, 10), // Limit to 10 slots
            'total_found' => count($availableSlots)
        ]);
    }

    /**
     * Notify customer about available slot.
     */
    public function notify(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $waitingList = $business->waitingLists()->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'notification_type' => 'required|in:available_slot,reminder,custom',
            'available_datetime' => 'nullable|date|after:now',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:2000',
            'send_method' => 'required|in:email,sms,both',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $request->validated();

        // Mark as notified if it's an available slot notification
        if ($validated['notification_type'] === 'available_slot') {
            $waitingList->markAsNotified();
        }

        // Here you would integrate with your notification service
        // For now, we'll simulate sending the notification

        // Log the activity
        activity()
            ->performedOn($waitingList)
            ->withProperties([
                'action' => 'notified',
                'notification_type' => $validated['notification_type'],
                'send_method' => $validated['send_method'],
                'message' => $validated['message']
            ])
            ->log('Customer notified');

        return response()->json([
            'success' => true,
            'message' => 'Customer has been notified successfully!'
        ]);
    }

    /**
     * Convert waiting list entry to booking.
     */
    public function convertToBooking(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $waitingList = $business->waitingLists()->with('service')->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'booking_datetime' => 'required|date|after:now',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $request->validated();

        // Use the integration service to handle the conversion
        $result = $this->integrationService->convertWaitingListToBooking($waitingList, [
            'start_datetime' => $validated['booking_datetime'],
            'notes' => $validated['notes'] ?? null,
        ]);

        if (!$result['success']) {
            return response()->json([
                'success' => false,
                'message' => $result['message']
            ], 422);
        }

        $booking = $result['booking'];

        // Log the activity
        activity()
            ->performedOn($waitingList)
            ->withProperties([
                'action' => 'converted_to_booking',
                'booking_id' => $booking->id,
                'booking_datetime' => $booking->start_datetime->format('Y-m-d H:i:s')
            ])
            ->log('Waiting list entry converted to booking');

        return response()->json([
            'success' => true,
            'message' => $result['message'],
            'booking_id' => $booking->id,
            'booking_url' => route('owner.bookings.show', $booking),
            'waiting_list' => $waitingList,
            'booking' => $booking
        ]);
    }

    /**
     * Get waiting list statistics.
     */
    public function getStats(Request $request)
    {
        $business = $this->getUserBusiness();

        $stats = [
            'active' => $business->waitingLists()->where('status', 'active')->count(),
            'converted_today' => $business->waitingLists()
                ->where('status', 'booked')
                ->whereDate('updated_at', today())
                ->count(),
            'urgent' => $business->waitingLists()
                ->where('status', 'active')
                ->where('preferred_date', '<=', now()->addDay())
                ->count(),
            'expired' => $business->waitingLists()->where('status', 'expired')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Auto-match waiting list entries with available slots.
     */
    public function autoMatch(Request $request)
    {
        $business = $this->getUserBusiness();
        $searchDays = $request->get('search_days', 7);

        $activeWaitingLists = $business->waitingLists()
            ->with('service')
            ->where('status', 'active')
            ->byPriority()
            ->get();

        $matches = [];

        foreach ($activeWaitingLists as $waitingList) {
            $service = $waitingList->service;
            $preferredDate = Carbon::parse($waitingList->preferred_date);

            // Search for available slots
            for ($i = 0; $i < $searchDays; $i++) {
                $checkDate = $preferredDate->copy()->addDays($i);

                // Skip if not in preferred days of week
                if ($waitingList->preferred_days_of_week &&
                    !in_array($checkDate->dayOfWeek, $waitingList->preferred_days_of_week)) {
                    continue;
                }

                // Get business operating hours for this day
                $operatingHours = $business->operatingHours()
                    ->where('day_of_week', $checkDate->dayOfWeek)
                    ->where('is_open', true)
                    ->first();

                if (!$operatingHours) {
                    continue;
                }

                // Generate time slots
                $startTime = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $operatingHours->open_time);
                $endTime = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $operatingHours->close_time);

                // Filter by preferred time if specified
                if ($waitingList->preferred_time_start) {
                    $preferredStart = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $waitingList->preferred_time_start);
                    $startTime = $startTime->max($preferredStart);
                }

                if ($waitingList->preferred_time_end) {
                    $preferredEnd = Carbon::parse($checkDate->format('Y-m-d') . ' ' . $waitingList->preferred_time_end);
                    $endTime = $endTime->min($preferredEnd);
                }

                while ($startTime->copy()->addMinutes($service->duration_minutes)->lte($endTime)) {
                    $slotEnd = $startTime->copy()->addMinutes($service->duration_minutes);

                    // Check availability
                    $availabilityCheck = $this->businessRulesEngine->checkAvailability(
                        $business,
                        $startTime,
                        $slotEnd,
                        [$service->id]
                    );

                    if ($availabilityCheck['available']) {
                        $matches[] = [
                            'waiting_list_id' => $waitingList->id,
                            'customer_name' => $waitingList->customer_name,
                            'service_name' => $service->name,
                            'priority' => $waitingList->priority,
                            'available_slot' => [
                                'datetime' => $startTime->format('Y-m-d H:i:s'),
                                'formatted' => $startTime->format('M j, Y g:i A'),
                            ]
                        ];
                        break 2; // Found a match, move to next waiting list entry
                    }

                    $startTime->addMinutes(30); // 30-minute intervals
                }
            }
        }

        return response()->json([
            'success' => true,
            'matches' => $matches,
            'total_matches' => count($matches)
        ]);
    }
}
