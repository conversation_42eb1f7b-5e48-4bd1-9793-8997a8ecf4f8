<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Booking;
use App\Models\Service;
use App\Models\Resource;
use App\Models\Business;
use App\Models\BusinessBranch;
use App\Models\BusinessCategory;
use App\Models\BusinessOperatingHour;
use App\Models\BusinessHoliday;
use App\Models\LandingServiceSettings;
use App\Services\GoogleMyBusinessService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class BusinessController extends Controller
{
    /**
     * Display business overview.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $businessModel = $this->getUserBusiness();
        $businessStats = $this->getBusinessStats($businessModel);

        // Format business data for the view
        $business = [
            'name' => $businessModel->name,
            'category' => $businessModel->categories->first()->name ?? 'Uncategorized',
            'phone' => $businessModel->phone ?? 'Not provided',
            'email' => $businessModel->email ?? 'Not provided',
            'website' => $businessModel->website ?? 'Not provided',
            'established' => $businessModel->created_at,
            'rating' => 4.5, // Placeholder - would come from reviews system
            'total_reviews' => 127, // Placeholder - would come from reviews system
            'address' => '123 Main Street, Downtown, City, State 12345', // Placeholder - would come from business branches
            'description' => $businessModel->description ?? 'No description provided',
        ];

        return view('owner.business.index', compact('business', 'businessStats'));
    }

    /**
     * Show the form for creating a new business.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $user = Auth::user();

        // Check if user already has a business
        if ($user->business) {
            return redirect()->route('owner.dashboard')
                ->with('info', 'You already have a business. You can manage it from the dashboard.');
        }

        $categories = BusinessCategory::all();

        return view('owner.business.create', compact('categories'));
    }

    /**
     * Store a newly created business in storage.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        // Check if user already has a business
        if ($user->business) {
            return redirect()->route('owner.dashboard')
                ->with('info', 'You already have a business.');
        }

        $validated = $request->validate([
            // Business information
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category_id' => 'required|exists:business_categories,id',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'timezone' => 'required|string',
            'currency' => 'required|string|max:3',
            'language' => 'required|string|max:2',

            // Landing page information
            'landing_page_slug' => [
                'required',
                'string',
                'min:3',
                'max:50',
                'regex:/^[a-z0-9-]+$/',
                'unique:business_landing_pages,custom_slug',
                function ($attribute, $value, $fail) {
                    $reservedSlugs = ['admin', 'api', 'www', 'mail', 'ftp', 'blog', 'shop', 'store', 'app', 'dashboard'];
                    if (in_array($value, $reservedSlugs)) {
                        $fail('This slug is reserved and cannot be used.');
                    }
                }
            ],
            'page_title' => 'required|string|max:255',
            'page_description' => 'nullable|string|max:500',
            'landing_page_theme' => 'required|string|in:default,modern,elegant,minimal,creative',
            'domain_type' => 'required|in:subdirectory,subdomain,custom',
            'custom_domain' => 'nullable|required_if:domain_type,custom|string|max:255',
            'booking_enabled' => 'boolean',

            // Service Display Configuration
            'service_display_type' => 'required|in:grid,list,carousel,masonry,featured',
            'service_count' => 'required|integer|min:3|max:20',
            'grid_columns' => 'nullable|integer|in:2,3,4',
            'service_card_style' => 'nullable|in:modern,classic,minimal,creative',
            'show_service_pricing' => 'boolean',
            'show_service_duration' => 'boolean',
            'show_service_descriptions' => 'boolean',
            'show_service_images' => 'boolean',
            'show_service_categories' => 'boolean',
            'enable_service_search' => 'boolean',
            'enable_service_filtering' => 'boolean',
            'quick_booking_enabled' => 'boolean',
            'show_availability_status' => 'boolean',
            'show_reviews_rating' => 'boolean',

            // Advanced Service Features
            'enable_service_seo' => 'boolean',
            'generate_service_sitemap' => 'boolean',
            'group_by_category' => 'boolean',
            'track_service_analytics' => 'boolean',
            'mobile_optimized' => 'boolean',
            'show_booking_calendar' => 'boolean',

            // Booking Configuration
            'booking_button_text' => 'nullable|string|max:50',
            'booking_button_style' => 'nullable|in:primary,secondary,success,warning,outline',

            // SEO information
            'meta_title' => 'nullable|string|max:60',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string|max:255',

            // Advanced SEO Features
            'enable_open_graph' => 'boolean',
            'enable_twitter_cards' => 'boolean',
            'enable_schema_markup' => 'boolean',
            'enable_local_seo' => 'boolean',
            'enable_amp' => 'boolean',
            'enable_lazy_loading' => 'boolean',

            // Analytics Integration
            'google_analytics_id' => 'nullable|string|max:50',
            'google_tag_manager_id' => 'nullable|string|max:50',
            'facebook_pixel_id' => 'nullable|string|max:50',
            'enable_conversion_tracking' => 'boolean',

            // Advanced Service Management
            'enable_service_packages' => 'boolean',
            'enable_add_on_services' => 'boolean',
            'enable_staff_assignments' => 'boolean',
            'enable_seasonal_services' => 'boolean',
            'enable_service_prerequisites' => 'boolean',
            'enable_equipment_requirements' => 'boolean',
            'enable_service_faqs' => 'boolean',
            'enable_before_after_gallery' => 'boolean',

            // Visual Page Builder
            'enable_visual_editor' => 'boolean',
            'enable_live_preview' => 'boolean',
            'enable_modular_sections' => 'boolean',
            'enable_wysiwyg_editor' => 'boolean',
            'enable_multimedia_management' => 'boolean',
            'enable_pre_designed_blocks' => 'boolean',

            // Default Sections
            'include_hero_section' => 'boolean',
            'include_about_section' => 'boolean',
            'include_services_section' => 'boolean',
            'include_team_section' => 'boolean',
            'include_testimonials_section' => 'boolean',
            'include_gallery_section' => 'boolean',
            'include_contact_section' => 'boolean',
            'include_booking_section' => 'boolean',
        ]);

        DB::transaction(function () use ($validated, $user) {
            // Create business
            $business = Business::create([
                'owner_id' => $user->id,
                'name' => $validated['name'],
                'description' => $validated['description'],
                'phone' => $validated['phone'],
                'email' => $validated['email'],
                'website' => $validated['website'],
                'timezone' => $validated['timezone'],
                'currency' => $validated['currency'],
                'language' => $validated['language'],
                'landing_page_enabled' => true,
                'landing_page_slug' => $validated['landing_page_slug'],
                'landing_page_status' => 'draft',
                'landing_page_theme' => $validated['landing_page_theme'],
                'domain_type' => $validated['domain_type'],
                'custom_domain' => $validated['custom_domain'] ?? null,
                'is_active' => true,
            ]);

            // Attach the category using the many-to-many relationship
            if ($validated['category_id']) {
                $business->categories()->attach($validated['category_id']);
            }

            // Create landing page
            $landingPage = $business->landingPage()->create([
                'custom_slug' => $validated['landing_page_slug'],
                'page_title' => $validated['page_title'],
                'page_description' => $validated['page_description'],
                'theme' => $validated['landing_page_theme'],
                'domain_type' => $validated['domain_type'],
                'custom_domain' => $validated['custom_domain'] ?? null,
                'booking_enabled' => $validated['booking_enabled'] ?? true,
                'meta_title' => $validated['meta_title'],
                'meta_description' => $validated['meta_description'],
                'meta_keywords' => $validated['meta_keywords'],
                'is_published' => false, // Start as draft
            ]);

            // Generate default sections for the landing page
            $landingPage->generateDefaultSections();

            // Create comprehensive SEO settings
            $business->seoSettings()->create([
                'meta_title' => $validated['meta_title'] ?? $validated['page_title'],
                'meta_description' => $validated['meta_description'] ?? $validated['page_description'],
                'meta_keywords' => $validated['meta_keywords'],
                'business_type' => $this->getBusinessTypeFromCategory($business),
                'sitemap_enabled' => true,
                'google_analytics_id' => $validated['google_analytics_id'],
                'google_tag_manager_id' => $validated['google_tag_manager_id'],
                'facebook_pixel_id' => $validated['facebook_pixel_id'],
                'amp_enabled' => $validated['enable_amp'] ?? false,
                'lazy_loading_enabled' => $validated['enable_lazy_loading'] ?? true,
                'og_title' => $validated['meta_title'] ?? $validated['page_title'],
                'og_description' => $validated['meta_description'] ?? $validated['page_description'],
                'og_type' => 'business.business',
                'twitter_card' => 'summary_large_image',
                'business_schema' => $this->generateBusinessSchema($business, $validated),
                'service_schema' => $validated['enable_service_seo'] ? [] : null,
            ]);

            // Create comprehensive landing service settings with user preferences
            $serviceSettings = LandingServiceSettings::getDefaultSettings();
            $serviceSettings['layout_type'] = $validated['service_display_type'];
            $serviceSettings['homepage_display_count'] = $validated['service_count'];
            $serviceSettings['service_card_style'] = $validated['service_card_style'] ?? 'modern';
            $serviceSettings['grid_columns'] = $validated['grid_columns'] ?? 3;
            $serviceSettings['show_pricing'] = $validated['show_service_pricing'] ?? true;
            $serviceSettings['show_duration'] = $validated['show_service_duration'] ?? true;
            $serviceSettings['show_description'] = $validated['show_service_descriptions'] ?? true;
            $serviceSettings['show_images'] = $validated['show_service_images'] ?? true;
            $serviceSettings['show_categories'] = $validated['show_service_categories'] ?? true;
            $serviceSettings['enable_search'] = $validated['enable_service_search'] ?? true;
            $serviceSettings['enable_filtering'] = $validated['enable_service_filtering'] ?? true;
            $serviceSettings['group_by_category'] = $validated['group_by_category'] ?? true;
            $serviceSettings['show_availability_status'] = $validated['show_availability_status'] ?? true;
            $serviceSettings['show_reviews_rating'] = $validated['show_reviews_rating'] ?? true;
            $serviceSettings['enable_quick_booking'] = $validated['quick_booking_enabled'] ?? true;
            $serviceSettings['booking_button_style'] = $validated['booking_button_style'] ?? 'primary';
            $serviceSettings['booking_button_text'] = $validated['booking_button_text'] ?? 'Book Now';
            $serviceSettings['show_booking_calendar'] = $validated['show_booking_calendar'] ?? false;
            $serviceSettings['enable_service_seo'] = $validated['enable_service_seo'] ?? true;
            $serviceSettings['generate_service_sitemap'] = $validated['generate_service_sitemap'] ?? true;
            $serviceSettings['track_service_analytics'] = $validated['track_service_analytics'] ?? true;
            $serviceSettings['mobile_optimized'] = $validated['mobile_optimized'] ?? true;

            // Advanced Service Management Features
            $serviceSettings['enable_service_packages'] = $validated['enable_service_packages'] ?? true;
            $serviceSettings['enable_add_on_services'] = $validated['enable_add_on_services'] ?? true;
            $serviceSettings['enable_staff_assignments'] = $validated['enable_staff_assignments'] ?? true;
            $serviceSettings['enable_seasonal_services'] = $validated['enable_seasonal_services'] ?? false;
            $serviceSettings['enable_service_prerequisites'] = $validated['enable_service_prerequisites'] ?? false;
            $serviceSettings['enable_equipment_requirements'] = $validated['enable_equipment_requirements'] ?? false;
            $serviceSettings['enable_service_faqs'] = $validated['enable_service_faqs'] ?? true;
            $serviceSettings['enable_before_after_gallery'] = $validated['enable_before_after_gallery'] ?? true;

            // Visual Page Builder Features
            $serviceSettings['enable_visual_editor'] = $validated['enable_visual_editor'] ?? true;
            $serviceSettings['enable_live_preview'] = $validated['enable_live_preview'] ?? true;
            $serviceSettings['enable_modular_sections'] = $validated['enable_modular_sections'] ?? true;
            $serviceSettings['enable_wysiwyg_editor'] = $validated['enable_wysiwyg_editor'] ?? true;
            $serviceSettings['enable_multimedia_management'] = $validated['enable_multimedia_management'] ?? true;
            $serviceSettings['enable_pre_designed_blocks'] = $validated['enable_pre_designed_blocks'] ?? true;

            $business->landingServiceSettings()->create($serviceSettings);

            // Create landing page with default sections
            $landingPage = $business->landingPage()->create([
                'custom_slug' => $validated['landing_page_slug'],
                'custom_domain' => $validated['custom_domain'],
                'domain_type' => $validated['domain_type'],
                'page_title' => $validated['page_title'],
                'page_description' => $validated['page_description'],
                'theme' => $validated['landing_page_theme'],
                'booking_enabled' => true,
                'is_published' => false,
            ]);

            // Generate default sections based on user selections
            $this->generateSelectedSections($landingPage, $validated);

            // Create default services based on business category
            $this->createDefaultServices($business, $validated['category_id']);

            // Update business with landing page timestamp
            $business->update([
                'landing_page_last_updated' => now(),
            ]);
        });

        return redirect()->route('owner.dashboard')
            ->with('success', 'Business and landing page created successfully! You can now customize your landing page and publish it when ready.');
    }

    /**
     * Show general business information form.
     *
     * @return \Illuminate\Http\Response
     */
    public function general()
    {
        $businessModel = $this->getUserBusiness();

        // Get main branch address if available
        $mainBranch = $businessModel->branches()->where('is_main_branch', true)->first();
        $address = $mainBranch ?
            trim($mainBranch->address . ', ' . $mainBranch->city . ', ' . $mainBranch->state . ' ' . $mainBranch->postal_code, ', ') :
            'No address provided';

        // Format business data for the view
        $business = [
            'name' => $businessModel->name ?? '',
            'category' => $businessModel->categories->first()->name ?? 'Uncategorized',
            'phone' => $businessModel->phone ?? '',
            'email' => $businessModel->email ?? '',
            'website' => $businessModel->website ?? '',
            'established' => $businessModel->created_at,
            'rating' => 4.5, // Placeholder - would come from reviews system
            'total_reviews' => 127, // Placeholder - would come from reviews system
            'address' => $address,
            'description' => $businessModel->description ?? '',
            'timezone' => $businessModel->timezone ?? 'UTC',
            'currency' => $businessModel->currency ?? 'USD',
            'language' => $businessModel->language ?? 'en',
        ];

        return view('owner.business.general', compact('business'));
    }

    /**
     * Update general business information.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateGeneral(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'name' => 'required|string|max:255|min:2',
            'description' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'email' => 'nullable|email|max:255',
            'website' => 'nullable|url|max:255',
            'timezone' => 'nullable|string|max:50',
            'currency' => 'nullable|string|size:3|in:USD,EUR,GBP,CAD,AUD,JPY',
            'language' => 'nullable|string|size:2|in:en,es,fr,de,it,pt',
        ], [
            'name.required' => 'Business name is required.',
            'name.min' => 'Business name must be at least 2 characters.',
            'phone.regex' => 'Please enter a valid phone number.',
            'currency.in' => 'Please select a valid currency.',
            'language.in' => 'Please select a valid language.',
        ]);

        // Use database transaction for data integrity
        DB::transaction(function () use ($request, $business) {
            $business->update($request->only([
                'name', 'description', 'phone', 'email', 'website',
                'timezone', 'currency', 'language'
            ]));
        });

        return redirect()->route('owner.business.general')
            ->with('success', 'Business information updated successfully.');
    }

    /**
     * Show operating hours management.
     *
     * @return \Illuminate\Http\Response
     */
    public function operatingHours()
    {
        $businessModel = $this->getUserBusiness();
        $operatingHours = $this->getOperatingHours();

        return view('owner.business.operating-hours', compact('operatingHours'));
    }

    /**
     * Update operating hours.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateOperatingHours(Request $request)
    {
        $request->validate([
            'hours' => 'required|array',
            'hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'hours.*.is_open' => 'nullable|boolean',
            'hours.*.open_time' => 'nullable|date_format:H:i',
            'hours.*.close_time' => 'nullable|date_format:H:i',
        ]);

        $business = $this->getUserBusiness();

        // Map day names to day numbers (database format)
        $dayMapping = [
            'monday' => 1,
            'tuesday' => 2,
            'wednesday' => 3,
            'thursday' => 4,
            'friday' => 5,
            'saturday' => 6,
            'sunday' => 0, // Sunday is 0 in database
        ];

        DB::transaction(function () use ($request, $business, $dayMapping) {
            foreach ($request->hours as $dayData) {
                $dayName = $dayData['day'];
                $dayOfWeek = $dayMapping[$dayName];
                $isOpen = isset($dayData['is_open']) && $dayData['is_open'] == '1';

                // Validate times if the day is open
                if ($isOpen && (empty($dayData['open_time']) || empty($dayData['close_time']))) {
                    continue; // Skip invalid entries
                }

                // Update or create operating hours record
                BusinessOperatingHour::updateOrCreate(
                    [
                        'business_id' => $business->id,
                        'business_branch_id' => null, // Main business hours (not branch-specific)
                        'day_of_week' => $dayOfWeek,
                    ],
                    [
                        'is_closed' => !$isOpen,
                        'open_time' => $isOpen ? $dayData['open_time'] : null,
                        'close_time' => $isOpen ? $dayData['close_time'] : null,
                    ]
                );
            }
        });

        return redirect()->route('owner.business.operating-hours')
            ->with('success', 'Operating hours updated successfully.');
    }

    /**
     * Show holidays and closures management.
     *
     * @return \Illuminate\Http\Response
     */
    public function holidays()
    {
        $businessModel = $this->getUserBusiness();
        $holidays = $this->getBusinessHolidays();

        return view('owner.business.holidays', compact('holidays'));
    }

    /**
     * Store a new holiday.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeHoliday(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_recurring' => 'boolean',
            'recurrence_type' => 'nullable|in:yearly,monthly,weekly',
            'description' => 'nullable|string|max:1000',
        ]);

        $holiday = $business->holidays()->create([
            'name' => $request->name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date ?: $request->start_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurrence_type' => $request->boolean('is_recurring') ? $request->recurrence_type : null,
            'description' => $request->description,
            'is_active' => true,
        ]);

        return redirect()->route('owner.business.holidays')
            ->with('success', 'Holiday added successfully.');
    }

    /**
     * Update an existing holiday.
     *
     * @param Request $request
     * @param BusinessHoliday $holiday
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateHoliday(Request $request, BusinessHoliday $holiday)
    {
        $business = $this->getUserBusiness();

        // Ensure the holiday belongs to the user's business
        if ($holiday->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_recurring' => 'boolean',
            'recurrence_type' => 'nullable|in:yearly,monthly,weekly',
            'description' => 'nullable|string|max:1000',
        ]);

        $holiday->update([
            'name' => $request->name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date ?: $request->start_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurrence_type' => $request->boolean('is_recurring') ? $request->recurrence_type : null,
            'description' => $request->description,
        ]);

        return redirect()->route('owner.business.holidays')
            ->with('success', 'Holiday updated successfully.');
    }

    /**
     * Delete a holiday.
     *
     * @param BusinessHoliday $holiday
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyHoliday(BusinessHoliday $holiday)
    {
        $business = $this->getUserBusiness();

        // Ensure the holiday belongs to the user's business
        if ($holiday->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $holiday->delete();

        return redirect()->route('owner.business.holidays')
            ->with('success', 'Holiday deleted successfully.');
    }

    /**
     * Import common holidays.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function importCommonHolidays(Request $request)
    {
        $business = $this->getUserBusiness();
        $year = $request->get('year', date('Y'));

        $commonHolidays = $this->getCommonUSHolidays($year);
        $imported = 0;

        foreach ($commonHolidays as $holidayData) {
            // Check if holiday already exists
            $exists = $business->holidays()
                ->where('name', $holidayData['name'])
                ->where('start_date', $holidayData['start_date'])
                ->exists();

            if (!$exists) {
                $business->holidays()->create($holidayData);
                $imported++;
            }
        }

        $message = $imported > 0
            ? "Successfully imported {$imported} common holidays."
            : "No new holidays to import. All common holidays for {$year} already exist.";

        return redirect()->route('owner.business.holidays')
            ->with('success', $message);
    }

    /**
     * Show locations management.
     *
     * @return \Illuminate\Http\Response
     */
    public function locations()
    {
        $businessModel = $this->getUserBusiness();
        $locations = $this->getBusinessLocations();

        return view('owner.business.locations', compact('locations'));
    }

    /**
     * Show branding and themes settings.
     *
     * @return \Illuminate\Http\Response
     */
    public function branding()
    {
        $businessModel = $this->getUserBusiness();
        $brandingSettings = $this->getBrandingSettings();

        return view('owner.business.branding', compact('brandingSettings'));
    }

    /**
     * Update branding settings.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateBranding(Request $request)
    {
        $request->validate([
            'primary_color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'secondary_color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'theme' => 'nullable|in:light,dark,auto',
            'font_family' => 'nullable|string|in:Inter,Roboto,Open Sans,Lato,Poppins',
        ], [
            'primary_color.regex' => 'Primary color must be a valid hex color (e.g., #28a745).',
            'secondary_color.regex' => 'Secondary color must be a valid hex color (e.g., #6c757d).',
            'logo.image' => 'Logo must be an image file.',
            'logo.mimes' => 'Logo must be a JPEG, PNG, JPG, or GIF file.',
            'logo.max' => 'Logo file size must not exceed 2MB.',
            'theme.in' => 'Please select a valid theme option.',
            'font_family.in' => 'Please select a valid font family.',
        ]);

        $business = $this->getUserBusiness();

        // Use database transaction for data integrity
        DB::transaction(function () use ($request, $business) {
            // Get current branding settings
            $currentBranding = $business->branding ?? [];

            // Handle logo upload
            $logoUrl = $currentBranding['logo_url'] ?? null;
            if ($request->hasFile('logo')) {
                // Delete old logo if exists
                if ($logoUrl && Storage::disk('public')->exists($logoUrl)) {
                    Storage::disk('public')->delete($logoUrl);
                }

                // Store new logo
                $logoPath = $request->file('logo')->store('business-logos', 'public');
                $logoUrl = $logoPath;
            }

            // Prepare branding data
            $brandingData = [
                'primary_color' => $request->primary_color ?: ($currentBranding['primary_color'] ?? '#28a745'),
                'secondary_color' => $request->secondary_color ?: ($currentBranding['secondary_color'] ?? '#6c757d'),
                'theme' => $request->theme ?: ($currentBranding['theme'] ?? 'light'),
                'font_family' => $request->font_family ?: ($currentBranding['font_family'] ?? 'Inter'),
                'logo_url' => $logoUrl,
                'updated_at' => now()->toISOString(),
            ];

            // Update business branding
            $business->update([
                'branding' => $brandingData
            ]);
        });

        return redirect()->route('owner.business.branding')
            ->with('success', 'Branding settings updated successfully.');
    }

    /**
     * Get business statistics.
     *
     * @param \App\Models\Business $business
     * @return array
     */
    private function getBusinessStats($business)
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now()->endOfMonth();

        // Calculate real statistics
        $totalServices = Service::where('business_id', $business->id)->count();
        $totalResources = Resource::where('business_id', $business->id)->count();

        $monthlyBookings = Booking::where('business_id', $business->id)
            ->whereBetween('start_datetime', [$startOfMonth, $endOfMonth])
            ->count();

        $monthlyRevenue = Booking::where('business_id', $business->id)
            ->whereBetween('start_datetime', [$startOfMonth, $endOfMonth])
            ->where('status', '!=', 'cancelled')
            ->sum('total_amount');

        // Calculate average service time
        $avgServiceTime = Booking::where('business_id', $business->id)
            ->where('status', 'completed')
            ->avg('total_duration_minutes');

        // Get peak day
        $peakDay = Booking::where('business_id', $business->id)
            ->select(DB::raw('DAYNAME(start_datetime) as day'), DB::raw('COUNT(*) as count'))
            ->groupBy('day')
            ->orderBy('count', 'desc')
            ->first();

        // Get busiest hour
        $busiestHour = Booking::where('business_id', $business->id)
            ->select(DB::raw('HOUR(start_datetime) as hour'), DB::raw('COUNT(*) as count'))
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->first();

        // Calculate customer satisfaction (placeholder - would need reviews/ratings system)
        $customerSatisfaction = 85; // Default value

        // Calculate repeat customers percentage
        $totalCustomers = Booking::where('business_id', $business->id)
            ->distinct('customer_id')
            ->count('customer_id');

        $repeatCustomers = Booking::where('business_id', $business->id)
            ->select('customer_id', DB::raw('COUNT(*) as booking_count'))
            ->groupBy('customer_id')
            ->having('booking_count', '>', 1)
            ->count();

        $repeatCustomersPercentage = $totalCustomers > 0 ? round(($repeatCustomers / $totalCustomers) * 100) : 0;

        return [
            'total_services' => $totalServices,
            'total_resources' => $totalResources,
            'total_staff' => 0, // Placeholder - would count staff members when staff system is implemented
            'monthly_bookings' => $monthlyBookings,
            'monthly_revenue' => $monthlyRevenue ?: 0,
            'average_service_time' => $avgServiceTime ? round($avgServiceTime) : 0,
            'customer_satisfaction' => $customerSatisfaction,
            'repeat_customers' => $repeatCustomersPercentage,
            'peak_day' => $peakDay ? $peakDay->day : 'N/A',
            'busiest_hour' => $busiestHour ? sprintf('%02d:00', $busiestHour->hour) : 'N/A',
        ];
    }

    /**
     * Get operating hours.
     *
     * @return array
     */
    private function getOperatingHours()
    {
        $business = $this->getUserBusiness();

        // Get operating hours from database
        $operatingHours = $business->operatingHours()
            ->orderBy('day_of_week')
            ->get()
            ->keyBy('day_of_week');

        // Map day numbers to day names and format data for the view
        $dayMapping = [
            1 => 'monday',
            2 => 'tuesday',
            3 => 'wednesday',
            4 => 'thursday',
            5 => 'friday',
            6 => 'saturday',
            0 => 'sunday', // Sunday is 0 in database
        ];

        $formattedHours = [];

        foreach ($dayMapping as $dayNumber => $dayName) {
            if ($operatingHours->has($dayNumber)) {
                $hour = $operatingHours[$dayNumber];
                $formattedHours[$dayName] = [
                    'is_open' => !$hour->is_closed,
                    'open_time' => $hour->open_time ? (is_string($hour->open_time) ? $hour->open_time : $hour->open_time->format('H:i')) : null,
                    'close_time' => $hour->close_time ? (is_string($hour->close_time) ? $hour->close_time : $hour->close_time->format('H:i')) : null,
                ];
            } else {
                // Default to closed if no record exists
                $formattedHours[$dayName] = [
                    'is_open' => false,
                    'open_time' => null,
                    'close_time' => null,
                ];
            }
        }

        return $formattedHours;
    }

    /**
     * Get business holidays.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getBusinessHolidays()
    {
        $business = $this->getUserBusiness();

        return $business->holidays()
            ->active()
            ->orderBy('start_date')
            ->get();
    }

    /**
     * Store a new location.
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeLocation(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'manager_name' => 'nullable|string|max:255',
            'manager_email' => 'nullable|email|max:255',
            'manager_phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'description' => 'nullable|string|max:1000',
        ], [
            'name.required' => 'Location name is required.',
            'address.required' => 'Address is required.',
            'city.required' => 'City is required.',
            'country.required' => 'Country is required.',
            'phone.regex' => 'Please enter a valid phone number.',
            'manager_phone.regex' => 'Please enter a valid manager phone number.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
        ]);

        DB::transaction(function () use ($request, $business) {
            // Check if this is the first location - make it primary
            $isFirstLocation = !$business->branches()->exists();

            $location = $business->branches()->create([
                'name' => $request->name,
                'description' => $request->description,
                'address' => $request->address,
                'city' => $request->city,
                'state' => $request->state,
                'postal_code' => $request->postal_code,
                'country' => $request->country,
                'phone' => $request->phone,
                'email' => $request->email,
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'manager_name' => $request->manager_name,
                'manager_email' => $request->manager_email,
                'manager_phone' => $request->manager_phone,
                'is_main_branch' => $isFirstLocation,
                'is_active' => true,
            ]);

            // Copy business operating hours to this location if it's the first location
            if ($isFirstLocation) {
                $businessHours = $business->operatingHours()->whereNull('business_branch_id')->get();
                foreach ($businessHours as $hour) {
                    $location->operatingHours()->create([
                        'business_id' => $business->id,
                        'day_of_week' => $hour->day_of_week,
                        'open_time' => $hour->open_time,
                        'close_time' => $hour->close_time,
                        'is_closed' => $hour->is_closed,
                        'break_times' => $hour->break_times,
                    ]);
                }
            }
        });

        return redirect()->route('owner.business.locations')
            ->with('success', 'Location added successfully.');
    }

    /**
     * Update an existing location.
     *
     * @param Request $request
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateLocation(Request $request, BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'address' => 'required|string|max:255',
            'city' => 'required|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'required|string|max:100',
            'phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'manager_name' => 'nullable|string|max:255',
            'manager_email' => 'nullable|email|max:255',
            'manager_phone' => 'nullable|string|max:20|regex:/^[\+]?[0-9\s\-\(\)]+$/',
            'description' => 'nullable|string|max:1000',
        ], [
            'name.required' => 'Location name is required.',
            'address.required' => 'Address is required.',
            'city.required' => 'City is required.',
            'country.required' => 'Country is required.',
            'phone.regex' => 'Please enter a valid phone number.',
            'manager_phone.regex' => 'Please enter a valid manager phone number.',
            'latitude.between' => 'Latitude must be between -90 and 90.',
            'longitude.between' => 'Longitude must be between -180 and 180.',
        ]);

        $location->update($request->only([
            'name', 'description', 'address', 'city', 'state', 'postal_code', 'country',
            'phone', 'email', 'latitude', 'longitude', 'manager_name', 'manager_email', 'manager_phone'
        ]));

        return redirect()->route('owner.business.locations')
            ->with('success', 'Location updated successfully.');
    }

    /**
     * Delete a location.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyLocation(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        // Prevent deletion of the main branch if it's the only location
        if ($location->is_main_branch && $business->branches()->count() === 1) {
            return redirect()->route('owner.business.locations')
                ->with('error', 'Cannot delete the only location. Add another location first.');
        }

        // If deleting the main branch, set another location as main
        if ($location->is_main_branch && $business->branches()->count() > 1) {
            $newMainBranch = $business->branches()->where('id', '!=', $location->id)->first();
            $newMainBranch->update(['is_main_branch' => true]);
        }

        $location->delete();

        return redirect()->route('owner.business.locations')
            ->with('success', 'Location deleted successfully.');
    }

    /**
     * Set a location as primary.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function setPrimaryLocation(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        DB::transaction(function () use ($business, $location) {
            // Remove primary status from all other locations
            $business->branches()->update(['is_main_branch' => false]);

            // Set this location as primary
            $location->update(['is_main_branch' => true]);
        });

        return redirect()->route('owner.business.locations')
            ->with('success', 'Primary location updated successfully.');
    }

    /**
     * Show location-specific operating hours.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\Response
     */
    public function locationOperatingHours(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $operatingHours = $this->getLocationOperatingHours($location);

        return view('owner.business.location-operating-hours', compact('location', 'operatingHours'));
    }

    /**
     * Update location-specific operating hours.
     *
     * @param Request $request
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateLocationOperatingHours(Request $request, BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'hours' => 'required|array',
            'hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'hours.*.is_open' => 'nullable|boolean',
            'hours.*.open_time' => 'nullable|date_format:H:i',
            'hours.*.close_time' => 'nullable|date_format:H:i',
            'hours.*.break_times' => 'nullable|array',
            'hours.*.break_times.*.start' => 'nullable|date_format:H:i',
            'hours.*.break_times.*.end' => 'nullable|date_format:H:i',
        ]);

        // Map day names to day numbers (database format)
        $dayMapping = [
            'monday' => 1,
            'tuesday' => 2,
            'wednesday' => 3,
            'thursday' => 4,
            'friday' => 5,
            'saturday' => 6,
            'sunday' => 0, // Sunday is 0 in database
        ];

        DB::transaction(function () use ($request, $business, $location, $dayMapping) {
            foreach ($request->hours as $dayData) {
                $dayName = $dayData['day'];
                $dayOfWeek = $dayMapping[$dayName];
                $isOpen = isset($dayData['is_open']) && $dayData['is_open'] == '1';

                // Validate times if the day is open
                if ($isOpen && (empty($dayData['open_time']) || empty($dayData['close_time']))) {
                    continue; // Skip invalid entries
                }

                // Process break times
                $breakTimes = [];
                if ($isOpen && !empty($dayData['break_times'])) {
                    foreach ($dayData['break_times'] as $break) {
                        if (!empty($break['start']) && !empty($break['end'])) {
                            $breakTimes[] = $break;
                        }
                    }
                }

                // Update or create operating hours record for this location
                BusinessOperatingHour::updateOrCreate(
                    [
                        'business_id' => $business->id,
                        'business_branch_id' => $location->id,
                        'day_of_week' => $dayOfWeek,
                    ],
                    [
                        'is_closed' => !$isOpen,
                        'open_time' => $isOpen ? $dayData['open_time'] : null,
                        'close_time' => $isOpen ? $dayData['close_time'] : null,
                        'break_times' => empty($breakTimes) ? null : $breakTimes,
                    ]
                );
            }
        });

        return redirect()->route('owner.business.locations.operating-hours', $location)
            ->with('success', 'Operating hours updated successfully for ' . $location->name . '.');
    }

    /**
     * Copy business operating hours to a location.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function copyBusinessHours(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        DB::transaction(function () use ($business, $location) {
            // Get business operating hours (not location-specific)
            $businessHours = $business->operatingHours()->whereNull('business_branch_id')->get();

            foreach ($businessHours as $hour) {
                // Update or create operating hours for this location
                BusinessOperatingHour::updateOrCreate(
                    [
                        'business_id' => $business->id,
                        'business_branch_id' => $location->id,
                        'day_of_week' => $hour->day_of_week,
                    ],
                    [
                        'is_closed' => $hour->is_closed,
                        'open_time' => $hour->open_time,
                        'close_time' => $hour->close_time,
                        'break_times' => $hour->break_times,
                    ]
                );
            }
        });

        return redirect()->route('owner.business.locations.operating-hours', $location)
            ->with('success', 'Business operating hours copied to ' . $location->name . ' successfully.');
    }

    /**
     * Get business locations.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getBusinessLocations()
    {
        $business = $this->getUserBusiness();
        return $business->branches()->active()->with(['operatingHours', 'holidays'])->get();
    }

    /**
     * Get location-specific operating hours.
     *
     * @param BusinessBranch $location
     * @return array
     */
    private function getLocationOperatingHours(BusinessBranch $location)
    {
        // Get operating hours from database for this location
        $operatingHours = $location->operatingHours()
            ->orderBy('day_of_week')
            ->get()
            ->keyBy('day_of_week');

        // Map day numbers to day names and format data for the view
        $dayMapping = [
            1 => 'monday',
            2 => 'tuesday',
            3 => 'wednesday',
            4 => 'thursday',
            5 => 'friday',
            6 => 'saturday',
            0 => 'sunday', // Sunday is 0 in database
        ];

        $formattedHours = [];
        for ($day = 0; $day <= 6; $day++) {
            $dayName = $dayMapping[$day];
            $hour = $operatingHours->get($day);

            if ($hour) {
                $formattedHours[$dayName] = [
                    'day' => $dayName,
                    'is_open' => !$hour->is_closed,
                    'open_time' => $hour->open_time ? $hour->open_time->format('H:i') : '',
                    'close_time' => $hour->close_time ? $hour->close_time->format('H:i') : '',
                    'break_times' => $hour->break_times ?? [],
                ];
            } else {
                // Default to closed if no record exists
                $formattedHours[$dayName] = [
                    'day' => $dayName,
                    'is_open' => false,
                    'open_time' => '',
                    'close_time' => '',
                    'break_times' => [],
                ];
            }
        }

        return $formattedHours;
    }

    /**
     * Show location-specific holidays.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\Response
     */
    public function locationHolidays(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $holidays = $this->getLocationHolidays($location);

        return view('owner.business.location-holidays', compact('location', 'holidays'));
    }

    /**
     * Store a new location-specific holiday.
     *
     * @param Request $request
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function storeLocationHoliday(Request $request, BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_recurring' => 'boolean',
            'recurrence_type' => 'nullable|in:yearly,monthly,weekly',
            'description' => 'nullable|string|max:1000',
        ], [
            'start_date.after_or_equal' => 'Holiday start date must be today or in the future.',
            'end_date.after_or_equal' => 'Holiday end date must be on or after the start date.',
        ]);

        $holiday = $business->holidays()->create([
            'business_branch_id' => $location->id,
            'name' => $request->name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date ?: $request->start_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurrence_type' => $request->boolean('is_recurring') ? $request->recurrence_type : null,
            'description' => $request->description,
            'is_active' => true,
        ]);

        return redirect()->route('owner.business.locations.holidays', $location)
            ->with('success', 'Holiday added successfully to ' . $location->name . '.');
    }

    /**
     * Update a location-specific holiday.
     *
     * @param Request $request
     * @param BusinessBranch $location
     * @param BusinessHoliday $holiday
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateLocationHoliday(Request $request, BusinessBranch $location, BusinessHoliday $holiday)
    {
        $business = $this->getUserBusiness();

        // Ensure the location and holiday belong to the user's business
        if ($location->business_id !== $business->id || $holiday->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        // Ensure the holiday belongs to this location
        if ($holiday->business_branch_id !== $location->id) {
            abort(403, 'Holiday does not belong to this location.');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'is_recurring' => 'boolean',
            'recurrence_type' => 'nullable|in:yearly,monthly,weekly',
            'description' => 'nullable|string|max:1000',
        ], [
            'end_date.after_or_equal' => 'Holiday end date must be on or after the start date.',
        ]);

        $holiday->update([
            'name' => $request->name,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date ?: $request->start_date,
            'is_recurring' => $request->boolean('is_recurring'),
            'recurrence_type' => $request->boolean('is_recurring') ? $request->recurrence_type : null,
            'description' => $request->description,
        ]);

        return redirect()->route('owner.business.locations.holidays', $location)
            ->with('success', 'Holiday updated successfully.');
    }

    /**
     * Delete a location-specific holiday.
     *
     * @param BusinessBranch $location
     * @param BusinessHoliday $holiday
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroyLocationHoliday(BusinessBranch $location, BusinessHoliday $holiday)
    {
        $business = $this->getUserBusiness();

        // Ensure the location and holiday belong to the user's business
        if ($location->business_id !== $business->id || $holiday->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        // Ensure the holiday belongs to this location
        if ($holiday->business_branch_id !== $location->id) {
            abort(403, 'Holiday does not belong to this location.');
        }

        $holiday->delete();

        return redirect()->route('owner.business.locations.holidays', $location)
            ->with('success', 'Holiday deleted successfully.');
    }

    /**
     * Copy business holidays to a location.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function copyBusinessHolidays(BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        DB::transaction(function () use ($business, $location) {
            // Get business holidays (not location-specific)
            $businessHolidays = $business->holidays()->whereNull('business_branch_id')->get();

            $copiedCount = 0;
            foreach ($businessHolidays as $holiday) {
                // Check if holiday already exists for this location
                $exists = $business->holidays()
                    ->where('business_branch_id', $location->id)
                    ->where('name', $holiday->name)
                    ->where('start_date', $holiday->start_date)
                    ->exists();

                if (!$exists) {
                    // Create a copy for this location
                    $business->holidays()->create([
                        'business_branch_id' => $location->id,
                        'name' => $holiday->name,
                        'start_date' => $holiday->start_date,
                        'end_date' => $holiday->end_date,
                        'is_recurring' => $holiday->is_recurring,
                        'recurrence_type' => $holiday->recurrence_type,
                        'recurrence_data' => $holiday->recurrence_data,
                        'description' => $holiday->description,
                        'is_active' => $holiday->is_active,
                    ]);
                    $copiedCount++;
                }
            }

            if ($copiedCount === 0) {
                session()->flash('info', 'No new holidays to copy. All business holidays already exist for this location.');
            } else {
                session()->flash('success', "Successfully copied {$copiedCount} business holidays to {$location->name}.");
            }
        });

        return redirect()->route('owner.business.locations.holidays', $location);
    }

    /**
     * Import common holidays for a location.
     *
     * @param Request $request
     * @param BusinessBranch $location
     * @return \Illuminate\Http\RedirectResponse
     */
    public function importCommonLocationHolidays(Request $request, BusinessBranch $location)
    {
        $business = $this->getUserBusiness();

        // Ensure the location belongs to the user's business
        if ($location->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $year = $request->get('year', date('Y'));
        $country = $request->get('country', 'US');

        $commonHolidays = $this->getCommonHolidays($year, $country);
        $imported = 0;

        foreach ($commonHolidays as $holidayData) {
            // Check if holiday already exists for this location
            $exists = $business->holidays()
                ->where('business_branch_id', $location->id)
                ->where('name', $holidayData['name'])
                ->where('start_date', $holidayData['start_date'])
                ->exists();

            if (!$exists) {
                $business->holidays()->create(array_merge($holidayData, [
                    'business_branch_id' => $location->id,
                ]));
                $imported++;
            }
        }

        $message = $imported > 0
            ? "Successfully imported {$imported} common holidays for {$location->name}."
            : "No new holidays to import. All common holidays for {$year} already exist for this location.";

        return redirect()->route('owner.business.locations.holidays', $location)
            ->with('success', $message);
    }

    /**
     * Get location-specific holidays.
     *
     * @param BusinessBranch $location
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getLocationHolidays(BusinessBranch $location)
    {
        return $location->holidays()
            ->orderBy('start_date')
            ->get();
    }

    /**
     * Get common holidays for a specific country and year.
     *
     * @param int $year
     * @param string $country
     * @return array
     */
    private function getCommonHolidays($year, $country = 'US')
    {
        $holidays = [];

        switch ($country) {
            case 'US':
                $holidays = [
                    [
                        'name' => 'New Year\'s Day',
                        'start_date' => "{$year}-01-01",
                        'end_date' => "{$year}-01-01",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'Federal holiday',
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Independence Day',
                        'start_date' => "{$year}-07-04",
                        'end_date' => "{$year}-07-04",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'Federal holiday',
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Christmas Day',
                        'start_date' => "{$year}-12-25",
                        'end_date' => "{$year}-12-25",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'Federal holiday',
                        'is_active' => true,
                    ],
                ];
                break;

            case 'CA':
                $holidays = [
                    [
                        'name' => 'New Year\'s Day',
                        'start_date' => "{$year}-01-01",
                        'end_date' => "{$year}-01-01",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'National holiday',
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Canada Day',
                        'start_date' => "{$year}-07-01",
                        'end_date' => "{$year}-07-01",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'National holiday',
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Christmas Day',
                        'start_date' => "{$year}-12-25",
                        'end_date' => "{$year}-12-25",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'National holiday',
                        'is_active' => true,
                    ],
                ];
                break;

            default:
                $holidays = [
                    [
                        'name' => 'New Year\'s Day',
                        'start_date' => "{$year}-01-01",
                        'end_date' => "{$year}-01-01",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'Common holiday',
                        'is_active' => true,
                    ],
                    [
                        'name' => 'Christmas Day',
                        'start_date' => "{$year}-12-25",
                        'end_date' => "{$year}-12-25",
                        'is_recurring' => true,
                        'recurrence_type' => 'yearly',
                        'description' => 'Common holiday',
                        'is_active' => true,
                    ],
                ];
        }

        return $holidays;
    }

    /**
     * Get branding settings.
     *
     * @return array
     */
    private function getBrandingSettings()
    {
        $business = $this->getUserBusiness();

        // Get branding settings from database
        $branding = $business->branding ?? [];

        // Return formatted branding data with defaults
        return [
            'primary_color' => $branding['primary_color'] ?? '#28a745',
            'secondary_color' => $branding['secondary_color'] ?? '#6c757d',
            'logo_url' => $branding['logo_url'] ?? null,
            'theme' => $branding['theme'] ?? 'light',
            'font_family' => $branding['font_family'] ?? 'Inter',
        ];
    }

    /**
     * Get common US holidays for a given year.
     *
     * @param int $year
     * @return array
     */
    private function getCommonUSHolidays($year)
    {
        return [
            [
                'name' => 'New Year\'s Day',
                'start_date' => "{$year}-01-01",
                'end_date' => "{$year}-01-01",
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'New Year\'s Day celebration',
                'is_active' => true,
            ],
            [
                'name' => 'Martin Luther King Jr. Day',
                'start_date' => $this->getNthMondayOfMonth($year, 1, 3),
                'end_date' => $this->getNthMondayOfMonth($year, 1, 3),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Martin Luther King Jr. Day',
                'is_active' => true,
            ],
            [
                'name' => 'Presidents\' Day',
                'start_date' => $this->getNthMondayOfMonth($year, 2, 3),
                'end_date' => $this->getNthMondayOfMonth($year, 2, 3),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Presidents\' Day',
                'is_active' => true,
            ],
            [
                'name' => 'Memorial Day',
                'start_date' => $this->getLastMondayOfMonth($year, 5),
                'end_date' => $this->getLastMondayOfMonth($year, 5),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Memorial Day',
                'is_active' => true,
            ],
            [
                'name' => 'Independence Day',
                'start_date' => "{$year}-07-04",
                'end_date' => "{$year}-07-04",
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Independence Day',
                'is_active' => true,
            ],
            [
                'name' => 'Labor Day',
                'start_date' => $this->getNthMondayOfMonth($year, 9, 1),
                'end_date' => $this->getNthMondayOfMonth($year, 9, 1),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Labor Day',
                'is_active' => true,
            ],
            [
                'name' => 'Columbus Day',
                'start_date' => $this->getNthMondayOfMonth($year, 10, 2),
                'end_date' => $this->getNthMondayOfMonth($year, 10, 2),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Columbus Day',
                'is_active' => true,
            ],
            [
                'name' => 'Veterans Day',
                'start_date' => "{$year}-11-11",
                'end_date' => "{$year}-11-11",
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Veterans Day',
                'is_active' => true,
            ],
            [
                'name' => 'Thanksgiving Day',
                'start_date' => $this->getNthThursdayOfMonth($year, 11, 4),
                'end_date' => $this->getNthThursdayOfMonth($year, 11, 4),
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Thanksgiving Day',
                'is_active' => true,
            ],
            [
                'name' => 'Christmas Day',
                'start_date' => "{$year}-12-25",
                'end_date' => "{$year}-12-25",
                'is_recurring' => true,
                'recurrence_type' => 'yearly',
                'description' => 'Christmas Day',
                'is_active' => true,
            ],
        ];
    }

    /**
     * Get the nth Monday of a given month and year.
     *
     * @param int $year
     * @param int $month
     * @param int $n
     * @return string
     */
    private function getNthMondayOfMonth($year, $month, $n)
    {
        $date = Carbon::create($year, $month, 1);
        $firstMonday = $date->copy()->next(Carbon::MONDAY);
        return $firstMonday->addWeeks($n - 1)->format('Y-m-d');
    }

    /**
     * Get the last Monday of a given month and year.
     *
     * @param int $year
     * @param int $month
     * @return string
     */
    private function getLastMondayOfMonth($year, $month)
    {
        $date = Carbon::create($year, $month)->endOfMonth();
        return $date->previous(Carbon::MONDAY)->format('Y-m-d');
    }

    /**
     * Get the nth Thursday of a given month and year.
     *
     * @param int $year
     * @param int $month
     * @param int $n
     * @return string
     */
    private function getNthThursdayOfMonth($year, $month, $n)
    {
        $date = Carbon::create($year, $month, 1);
        $firstThursday = $date->copy()->next(Carbon::THURSDAY);
        return $firstThursday->addWeeks($n - 1)->format('Y-m-d');
    }

    /**
     * Import locations from Google My Business.
     *
     * @param Request $request
     * @param GoogleMyBusinessService $googleService
     * @return \Illuminate\Http\JsonResponse
     */
    public function importFromGoogle(Request $request, GoogleMyBusinessService $googleService)
    {
        $request->validate([
            'business_name' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'selected_locations' => 'nullable|array',
            'selected_locations.*' => 'required|array',
        ]);

        $business = $this->getUserBusiness();

        if ($request->has('search')) {
            // Search for locations
            $result = $googleService->searchBusinessLocations(
                $request->business_name,
                $request->address
            );

            return response()->json($result);
        }

        if ($request->has('selected_locations')) {
            // Import selected locations
            $result = $googleService->importLocations(
                $business,
                $request->selected_locations
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'redirect' => route('owner.business.locations')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'errors' => $result['errors'] ?? []
                ], 422);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid request'
        ], 400);
    }

    /**
     * Bulk update operating hours for multiple locations.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bulkUpdateHours(Request $request)
    {
        $request->validate([
            'location_ids' => 'required|array|min:1',
            'location_ids.*' => 'required|exists:business_branches,id',
            'hours' => 'required|array',
            'hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'hours.*.is_open' => 'nullable|boolean',
            'hours.*.open_time' => 'nullable|date_format:H:i',
            'hours.*.close_time' => 'nullable|date_format:H:i',
        ]);

        $business = $this->getUserBusiness();
        $locationIds = $request->location_ids;

        // Verify all locations belong to the user's business
        $locations = $business->branches()->whereIn('id', $locationIds)->get();

        if ($locations->count() !== count($locationIds)) {
            return response()->json([
                'success' => false,
                'message' => 'Some locations do not belong to your business.'
            ], 403);
        }

        // Map day names to day numbers
        $dayMapping = [
            'monday' => 1,
            'tuesday' => 2,
            'wednesday' => 3,
            'thursday' => 4,
            'friday' => 5,
            'saturday' => 6,
            'sunday' => 0,
        ];

        $updated = 0;

        DB::transaction(function () use ($request, $locations, $dayMapping, &$updated) {
            foreach ($locations as $location) {
                foreach ($request->hours as $dayData) {
                    $dayName = $dayData['day'];
                    $dayOfWeek = $dayMapping[$dayName];
                    $isOpen = isset($dayData['is_open']) && $dayData['is_open'] == '1';

                    // Skip if times are missing for open days
                    if ($isOpen && (empty($dayData['open_time']) || empty($dayData['close_time']))) {
                        continue;
                    }

                    // Update or create operating hours record
                    $location->operatingHours()->updateOrCreate(
                        ['day_of_week' => $dayOfWeek],
                        [
                            'is_closed' => !$isOpen,
                            'open_time' => $isOpen ? $dayData['open_time'] : null,
                            'close_time' => $isOpen ? $dayData['close_time'] : null,
                        ]
                    );
                }
                $updated++;
            }
        });

        return response()->json([
            'success' => true,
            'message' => "Successfully updated operating hours for {$updated} location" . ($updated > 1 ? 's' : '') . ".",
            'updated_count' => $updated
        ]);
    }

    /**
     * Export location data in various formats.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function exportLocations(Request $request)
    {
        $request->validate([
            'format' => 'required|in:csv,excel,pdf',
            'include_hours' => 'nullable|boolean',
            'include_holidays' => 'nullable|boolean',
        ]);

        $business = $this->getUserBusiness();
        $locations = $business->branches()->with(['operatingHours', 'holidays'])->get();
        $format = $request->format;
        $includeHours = $request->boolean('include_hours');
        $includeHolidays = $request->boolean('include_holidays');

        switch ($format) {
            case 'csv':
                return $this->exportLocationsCsv($locations, $includeHours, $includeHolidays);
            case 'excel':
                return $this->exportLocationsExcel($locations, $includeHours, $includeHolidays);
            case 'pdf':
                return $this->exportLocationsPdf($locations, $includeHours, $includeHolidays);
            default:
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid export format'
                ], 400);
        }
    }

    /**
     * Export locations as CSV.
     */
    private function exportLocationsCsv($locations, $includeHours, $includeHolidays)
    {
        $filename = 'business-locations-' . date('Y-m-d') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($locations, $includeHours, $includeHolidays) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            $csvHeaders = [
                'Name', 'Address', 'City', 'State', 'Postal Code', 'Country',
                'Phone', 'Email', 'Manager Name', 'Manager Email', 'Manager Phone',
                'Latitude', 'Longitude', 'Is Main Branch', 'Is Active'
            ];

            if ($includeHours) {
                $csvHeaders = array_merge($csvHeaders, [
                    'Mon Hours', 'Tue Hours', 'Wed Hours', 'Thu Hours',
                    'Fri Hours', 'Sat Hours', 'Sun Hours'
                ]);
            }

            fputcsv($file, $csvHeaders);

            foreach ($locations as $location) {
                $row = [
                    $location->name,
                    $location->address,
                    $location->city,
                    $location->state,
                    $location->postal_code,
                    $location->country,
                    $location->phone,
                    $location->email,
                    $location->manager_name,
                    $location->manager_email,
                    $location->manager_phone,
                    $location->latitude,
                    $location->longitude,
                    $location->is_main_branch ? 'Yes' : 'No',
                    $location->is_active ? 'Yes' : 'No'
                ];

                if ($includeHours) {
                    $dayMapping = [0 => 'Sun', 1 => 'Mon', 2 => 'Tue', 3 => 'Wed', 4 => 'Thu', 5 => 'Fri', 6 => 'Sat'];
                    $hours = [];

                    for ($day = 1; $day <= 6; $day++) {
                        $hours[$day] = 'Closed';
                    }
                    $hours[0] = 'Closed'; // Sunday

                    foreach ($location->operatingHours as $hour) {
                        if (!$hour->is_closed) {
                            $hours[$hour->day_of_week] = $hour->open_time . ' - ' . $hour->close_time;
                        }
                    }

                    $row = array_merge($row, [
                        $hours[1], $hours[2], $hours[3], $hours[4],
                        $hours[5], $hours[6], $hours[0]
                    ]);
                }

                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export locations as Excel (simplified CSV with .xlsx extension).
     */
    private function exportLocationsExcel($locations, $includeHours, $includeHolidays)
    {
        // For simplicity, we'll use CSV format with Excel headers
        // In a real implementation, you would use a library like PhpSpreadsheet
        $filename = 'business-locations-' . date('Y-m-d') . '.xlsx';

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        // For now, return CSV format with Excel headers
        return $this->exportLocationsCsv($locations, $includeHours, $includeHolidays);
    }

    /**
     * Export locations as PDF.
     */
    private function exportLocationsPdf($locations, $includeHours, $includeHolidays)
    {
        // For simplicity, we'll create a basic HTML to PDF conversion
        // In a real implementation, you would use a library like DomPDF or wkhtmltopdf
        $business = $this->getUserBusiness();

        $html = '<!DOCTYPE html>
        <html>
        <head>
            <title>Business Locations Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f8f9fa; font-weight: bold; }
                .location-header { background-color: #e9ecef; font-weight: bold; }
                .hours-section { margin-top: 10px; }
                .hours-day { margin: 2px 0; }
            </style>
        </head>
        <body>
            <h1>' . htmlspecialchars($business->name) . ' - Locations Report</h1>
            <p><strong>Generated:</strong> ' . date('F j, Y \a\t g:i A') . '</p>
            <p><strong>Total Locations:</strong> ' . $locations->count() . '</p>';

        foreach ($locations as $location) {
            $html .= '
            <div style="page-break-inside: avoid; margin-bottom: 30px;">
                <h2>' . htmlspecialchars($location->name) . '</h2>
                <table>
                    <tr><th>Address</th><td>' . htmlspecialchars($location->address) . '</td></tr>
                    <tr><th>City</th><td>' . htmlspecialchars($location->city) . '</td></tr>
                    <tr><th>State</th><td>' . htmlspecialchars($location->state) . '</td></tr>
                    <tr><th>Postal Code</th><td>' . htmlspecialchars($location->postal_code) . '</td></tr>
                    <tr><th>Country</th><td>' . htmlspecialchars($location->country) . '</td></tr>
                    <tr><th>Phone</th><td>' . htmlspecialchars($location->phone ?: 'N/A') . '</td></tr>
                    <tr><th>Email</th><td>' . htmlspecialchars($location->email ?: 'N/A') . '</td></tr>
                    <tr><th>Manager</th><td>' . htmlspecialchars($location->manager_name ?: 'N/A') . '</td></tr>
                    <tr><th>Main Branch</th><td>' . ($location->is_main_branch ? 'Yes' : 'No') . '</td></tr>
                    <tr><th>Status</th><td>' . ($location->is_active ? 'Active' : 'Inactive') . '</td></tr>
                </table>';

            if ($includeHours && $location->operatingHours->count() > 0) {
                $html .= '<h3>Operating Hours</h3><table>';
                $dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

                $hours = [];
                foreach ($location->operatingHours as $hour) {
                    $hours[$hour->day_of_week] = $hour;
                }

                for ($day = 0; $day <= 6; $day++) {
                    $dayName = $dayNames[$day];
                    if (isset($hours[$day]) && !$hours[$day]->is_closed) {
                        $timeRange = $hours[$day]->open_time . ' - ' . $hours[$day]->close_time;
                    } else {
                        $timeRange = 'Closed';
                    }
                    $html .= '<tr><th>' . $dayName . '</th><td>' . $timeRange . '</td></tr>';
                }
                $html .= '</table>';
            }

            $html .= '</div>';
        }

        $html .= '</body></html>';

        $filename = 'business-locations-' . date('Y-m-d') . '.pdf';

        // For now, return HTML content
        // In a real implementation, you would convert this to PDF
        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => 'attachment; filename="' . str_replace('.pdf', '.html', $filename) . '"',
        ]);
    }

    /**
     * Get business type from category for schema.org
     *
     * @param Business $business
     * @return string
     */
    private function getBusinessTypeFromCategory($business)
    {
        $category = $business->categories()->first();

        if (!$category) {
            return 'LocalBusiness';
        }

        $categoryMappings = [
            'salon' => 'BeautySalon',
            'spa' => 'DaySpa',
            'restaurant' => 'Restaurant',
            'clinic' => 'MedicalClinic',
            'fitness' => 'ExerciseGym',
            'automotive' => 'AutomotiveBusiness',
            'retail' => 'Store',
            'hotel' => 'LodgingBusiness',
            'dentist' => 'Dentist',
            'veterinary' => 'VeterinaryCare',
            'legal' => 'LegalService',
            'accounting' => 'AccountingService',
            'real-estate' => 'RealEstateAgent',
            'photography' => 'ProfessionalService',
            'consulting' => 'ProfessionalService',
        ];

        return $categoryMappings[strtolower($category->slug)] ?? 'LocalBusiness';
    }

    /**
     * Create default services based on business category
     *
     * @param Business $business
     * @param int $categoryId
     * @return void
     */
    private function createDefaultServices($business, $categoryId)
    {
        $category = BusinessCategory::find($categoryId);

        if (!$category) {
            return;
        }

        // Get default services based on category
        $defaultServices = $this->getDefaultServicesByCategory($category);

        // Create a service category for this business
        $serviceCategory = $business->serviceCategories()->create([
            'name' => 'General Services',
            'slug' => 'general-services',
            'description' => 'Our main service offerings',
            'sort_order' => 1,
            'is_active' => true,
        ]);

        // Create the default services
        foreach ($defaultServices as $index => $serviceData) {
            $service = $business->services()->create([
                'service_category_id' => $serviceCategory->id,
                'name' => $serviceData['name'],
                'slug' => Str::slug($serviceData['name']),
                'description' => $serviceData['description'],
                'short_description' => $serviceData['short_description'] ?? substr($serviceData['description'], 0, 100),
                'duration_minutes' => $serviceData['duration_minutes'],
                'base_price' => $serviceData['base_price'],
                'online_booking_enabled' => true,
                'is_active' => true,
                'sort_order' => $index + 1,
                // Landing page settings
                'is_public' => true,
                'featured_on_landing' => $index < 3, // Feature first 3 services
                'landing_display_order' => $index + 1,
                'show_price_on_landing' => true,
                'show_duration_on_landing' => true,
                'show_description_on_landing' => true,
                'show_image_on_landing' => true,
                'quick_booking_enabled' => true,
                'booking_button_text' => 'Book Now',
            ]);
        }
    }

    /**
     * Get default services based on business category
     *
     * @param BusinessCategory $category
     * @return array
     */
    private function getDefaultServicesByCategory($category)
    {
        $categorySlug = strtolower($category->slug ?? $category->name);

        $defaultServices = [
            'salon' => [
                ['name' => 'Haircut & Style', 'description' => 'Professional haircut and styling service', 'duration_minutes' => 60, 'base_price' => 50.00],
                ['name' => 'Hair Color', 'description' => 'Full hair coloring service with premium products', 'duration_minutes' => 120, 'base_price' => 80.00],
                ['name' => 'Highlights', 'description' => 'Hair highlighting and lowlighting service', 'duration_minutes' => 90, 'base_price' => 70.00],
                ['name' => 'Blowout', 'description' => 'Professional hair blowout and styling', 'duration_minutes' => 45, 'base_price' => 35.00],
                ['name' => 'Deep Conditioning', 'description' => 'Intensive hair treatment and conditioning', 'duration_minutes' => 30, 'base_price' => 25.00],
            ],
            'spa' => [
                ['name' => 'Swedish Massage', 'description' => 'Relaxing full-body Swedish massage', 'duration_minutes' => 60, 'base_price' => 90.00],
                ['name' => 'Deep Tissue Massage', 'description' => 'Therapeutic deep tissue massage', 'duration_minutes' => 60, 'base_price' => 100.00],
                ['name' => 'Facial Treatment', 'description' => 'Rejuvenating facial with premium products', 'duration_minutes' => 75, 'base_price' => 85.00],
                ['name' => 'Body Wrap', 'description' => 'Detoxifying full-body wrap treatment', 'duration_minutes' => 90, 'base_price' => 120.00],
                ['name' => 'Aromatherapy Session', 'description' => 'Relaxing aromatherapy treatment', 'duration_minutes' => 45, 'base_price' => 65.00],
            ],
            'fitness' => [
                ['name' => 'Personal Training', 'description' => 'One-on-one personal training session', 'duration_minutes' => 60, 'base_price' => 75.00],
                ['name' => 'Group Fitness Class', 'description' => 'High-energy group fitness class', 'duration_minutes' => 45, 'base_price' => 25.00],
                ['name' => 'Nutrition Consultation', 'description' => 'Personalized nutrition planning session', 'duration_minutes' => 30, 'base_price' => 50.00],
                ['name' => 'Fitness Assessment', 'description' => 'Comprehensive fitness evaluation', 'duration_minutes' => 45, 'base_price' => 40.00],
                ['name' => 'Yoga Class', 'description' => 'Relaxing and strengthening yoga session', 'duration_minutes' => 60, 'base_price' => 20.00],
            ],
            'clinic' => [
                ['name' => 'Consultation', 'description' => 'Medical consultation and examination', 'duration_minutes' => 30, 'base_price' => 100.00],
                ['name' => 'Follow-up Visit', 'description' => 'Follow-up appointment and check-in', 'duration_minutes' => 15, 'base_price' => 50.00],
                ['name' => 'Health Screening', 'description' => 'Comprehensive health screening', 'duration_minutes' => 45, 'base_price' => 150.00],
                ['name' => 'Vaccination', 'description' => 'Immunization and vaccination service', 'duration_minutes' => 15, 'base_price' => 30.00],
                ['name' => 'Lab Work', 'description' => 'Laboratory testing and analysis', 'duration_minutes' => 10, 'base_price' => 75.00],
            ],
            'restaurant' => [
                ['name' => 'Private Dining', 'description' => 'Exclusive private dining experience', 'duration_minutes' => 120, 'base_price' => 200.00],
                ['name' => 'Wine Tasting', 'description' => 'Curated wine tasting experience', 'duration_minutes' => 60, 'base_price' => 75.00],
                ['name' => 'Cooking Class', 'description' => 'Hands-on cooking instruction', 'duration_minutes' => 90, 'base_price' => 125.00],
                ['name' => 'Chef\'s Table', 'description' => 'Exclusive chef\'s table experience', 'duration_minutes' => 180, 'base_price' => 300.00],
                ['name' => 'Catering Service', 'description' => 'Professional catering for events', 'duration_minutes' => 240, 'base_price' => 500.00],
            ],
        ];

        // Return services for the category, or default generic services
        return $defaultServices[$categorySlug] ?? [
            ['name' => 'Standard Service', 'description' => 'Our main service offering', 'duration_minutes' => 60, 'base_price' => 50.00],
            ['name' => 'Premium Service', 'description' => 'Premium service with additional benefits', 'duration_minutes' => 90, 'base_price' => 75.00],
            ['name' => 'Consultation', 'description' => 'Initial consultation and planning session', 'duration_minutes' => 30, 'base_price' => 25.00],
        ];
    }



    /**
     * Generate business schema markup for SEO.
     */
    private function generateBusinessSchema($business, $validated)
    {
        $businessType = $this->getBusinessTypeFromCategory($business);

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => $businessType,
            'name' => $business->name,
            'description' => $business->description ?? $validated['page_description'],
            'url' => url('/' . $validated['landing_page_slug']),
            'telephone' => $business->phone,
            'email' => $business->email,
            'priceRange' => '$$', // Default price range
            'currenciesAccepted' => $business->currency ?? 'USD',
            'paymentAccepted' => 'Cash, Credit Card',
            'openingHours' => [], // Will be populated from operating hours
        ];

        // Add address if available
        $mainBranch = $business->branches()->where('is_main_branch', true)->first();
        if ($mainBranch) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $mainBranch->address,
                'addressLocality' => $mainBranch->city,
                'addressRegion' => $mainBranch->state,
                'postalCode' => $mainBranch->postal_code,
                'addressCountry' => $mainBranch->country ?? 'US',
            ];

            if ($mainBranch->latitude && $mainBranch->longitude) {
                $schema['geo'] = [
                    '@type' => 'GeoCoordinates',
                    'latitude' => $mainBranch->latitude,
                    'longitude' => $mainBranch->longitude,
                ];
            }
        }

        return $schema;
    }

    /**
     * Generate selected sections for landing page based on user choices.
     */
    private function generateSelectedSections($landingPage, $validated)
    {
        $business = $landingPage->business;
        $sortOrder = 1;

        // Hero Section
        if ($validated['include_hero_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'hero',
                'section_name' => 'Hero Section',
                'content_data' => [
                    'title' => $business->name,
                    'subtitle' => $business->description ?? 'Welcome to our business',
                    'background_image' => null,
                    'background_video' => null,
                    'cta_text' => 'Book Now',
                    'cta_url' => '#booking',
                    'secondary_cta_text' => 'Learn More',
                    'secondary_cta_url' => '#about',
                    'overlay_opacity' => 0.5,
                    'text_alignment' => 'center'
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // About Section
        if ($validated['include_about_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'about',
                'section_name' => 'About Us',
                'content_data' => [
                    'title' => 'About ' . $business->name,
                    'content' => $business->description ?? 'Learn more about our business',
                    'image' => null,
                    'features' => [],
                    'cta_button' => true,
                    'cta_text' => 'Contact Us',
                    'cta_url' => '#contact'
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Services Section
        if ($validated['include_services_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'services',
                'section_name' => 'Our Services',
                'content_data' => [
                    'title' => 'Our Services',
                    'subtitle' => 'Discover what we offer',
                    'layout' => $validated['service_display_type'] ?? 'grid',
                    'show_pricing' => $validated['show_service_pricing'] ?? true,
                    'show_duration' => $validated['show_service_duration'] ?? true,
                    'cta_button' => true,
                    'cta_text' => 'View All Services',
                    'cta_url' => '#services'
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Team Section
        if ($validated['include_team_section'] ?? false) {
            $landingPage->sections()->create([
                'section_type' => 'team',
                'section_name' => 'Our Team',
                'content_data' => [
                    'title' => 'Meet Our Team',
                    'subtitle' => 'Professional and experienced staff',
                    'layout' => 'grid',
                    'show_bio' => true,
                    'show_specialties' => true
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Testimonials Section
        if ($validated['include_testimonials_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'testimonials',
                'section_name' => 'Testimonials',
                'content_data' => [
                    'title' => 'What Our Customers Say',
                    'subtitle' => 'Real reviews from satisfied customers',
                    'layout' => 'carousel',
                    'show_ratings' => true,
                    'show_photos' => true
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Gallery Section
        if ($validated['include_gallery_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'gallery',
                'section_name' => 'Gallery',
                'content_data' => [
                    'title' => 'Our Work',
                    'subtitle' => 'See our latest projects and results',
                    'layout' => 'masonry',
                    'show_captions' => true,
                    'enable_lightbox' => true
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Contact Section
        if ($validated['include_contact_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'contact',
                'section_name' => 'Contact Us',
                'content_data' => [
                    'title' => 'Get In Touch',
                    'subtitle' => 'Contact us for more information',
                    'show_form' => true,
                    'show_map' => true,
                    'show_hours' => true,
                    'show_social_links' => true
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }

        // Booking Section
        if ($validated['include_booking_section'] ?? true) {
            $landingPage->sections()->create([
                'section_type' => 'booking',
                'section_name' => 'Book Appointment',
                'content_data' => [
                    'title' => 'Book Your Appointment',
                    'subtitle' => 'Schedule your visit with us',
                    'show_calendar' => $validated['show_booking_calendar'] ?? false,
                    'show_services' => true,
                    'show_staff_selection' => $validated['enable_staff_assignments'] ?? true,
                    'button_text' => $validated['booking_button_text'] ?? 'Book Now',
                    'button_style' => $validated['booking_button_style'] ?? 'primary'
                ],
                'sort_order' => $sortOrder++,
                'is_visible' => true
            ]);
        }
    }
}
