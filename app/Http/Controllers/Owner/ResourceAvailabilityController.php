<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Services\ResourceBookingService;
use App\Models\Service;
use App\Models\Resource;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ResourceAvailabilityController extends Controller
{
    protected $resourceBookingService;

    public function __construct(ResourceBookingService $resourceBookingService)
    {
        $this->resourceBookingService = $resourceBookingService;
    }

    /**
     * Check availability for specific services at a given time.
     */
    public function checkServiceAvailability(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'service_ids' => 'required|array|min:1',
            'service_ids.*' => 'exists:services,id',
            'start_datetime' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:1|max:1440',
            'exclude_booking_id' => 'nullable|integer|exists:bookings,id',
        ]);

        $serviceIds = $request->service_ids;
        $startDateTime = Carbon::parse($request->start_datetime);
        $endDateTime = $startDateTime->copy()->addMinutes($request->duration_minutes);
        $excludeBookingId = $request->exclude_booking_id;

        // Check resource conflicts
        $resourceCheck = $this->resourceBookingService->checkResourceConflicts(
            $serviceIds,
            $startDateTime,
            $endDateTime,
            $business,
            $excludeBookingId
        );

        // Get detailed resource information
        $services = $business->services()->whereIn('id', $serviceIds)->with('requiredResources')->get();
        $resourceDetails = [];

        foreach ($services as $service) {
            foreach ($service->requiredResources as $resource) {
                $availableQuantity = $this->resourceBookingService->getAvailableQuantity(
                    $resource,
                    $startDateTime,
                    $endDateTime
                );

                $resourceDetails[] = [
                    'service_id' => $service->id,
                    'service_name' => $service->name,
                    'resource_id' => $resource->id,
                    'resource_name' => $resource->name,
                    'resource_type' => $resource->resourceType->name,
                    'required_quantity' => $resource->pivot->quantity_required,
                    'available_quantity' => $availableQuantity,
                    'total_capacity' => $resource->capacity,
                    'is_available' => $availableQuantity >= $resource->pivot->quantity_required,
                ];
            }
        }

        return response()->json([
            'available' => !$resourceCheck['has_conflicts'],
            'conflicts' => $resourceCheck['conflicts'],
            'resource_details' => $resourceDetails,
            'message' => $resourceCheck['message'],
            'start_datetime' => $startDateTime->toISOString(),
            'end_datetime' => $endDateTime->toISOString(),
        ]);
    }

    /**
     * Get resource utilization for a specific period.
     */
    public function getResourceUtilization(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'resource_id' => 'required|exists:resources,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $resource = $business->resources()->findOrFail($request->resource_id);
        $startDate = Carbon::parse($request->start_date)->startOfDay();
        $endDate = Carbon::parse($request->end_date)->endOfDay();

        $utilization = $this->resourceBookingService->getResourceUtilization(
            $resource,
            $startDate,
            $endDate
        );

        return response()->json([
            'success' => true,
            'utilization' => $utilization,
        ]);
    }

    /**
     * Find alternative resources for a service.
     */
    public function findAlternatives(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'service_id' => 'required|exists:services,id',
            'start_datetime' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:1|max:1440',
        ]);

        $service = $business->services()->findOrFail($request->service_id);
        $startDateTime = Carbon::parse($request->start_datetime);
        $endDateTime = $startDateTime->copy()->addMinutes($request->duration_minutes);

        $alternatives = $this->resourceBookingService->findAlternativeResources(
            $service,
            $startDateTime,
            $endDateTime
        );

        $alternativeData = $alternatives->map(function ($resource) use ($startDateTime, $endDateTime) {
            return [
                'id' => $resource->id,
                'name' => $resource->name,
                'type' => $resource->resourceType->name,
                'capacity' => $resource->capacity,
                'available_quantity' => $this->resourceBookingService->getAvailableQuantity(
                    $resource,
                    $startDateTime,
                    $endDateTime
                ),
                'hourly_rate' => $resource->hourly_rate,
                'branch' => $resource->branch ? $resource->branch->name : 'All Branches',
            ];
        });

        return response()->json([
            'success' => true,
            'alternatives' => $alternativeData,
            'count' => $alternatives->count(),
        ]);
    }

    /**
     * Get next available time slot for a service.
     */
    public function getNextAvailableSlot(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'service_id' => 'required|exists:services,id',
            'from_datetime' => 'required|date|after:now',
            'duration_minutes' => 'required|integer|min:1|max:1440',
        ]);

        $service = $business->services()->findOrFail($request->service_id);
        $fromDateTime = Carbon::parse($request->from_datetime);
        $durationMinutes = $request->duration_minutes;

        $nextSlot = $this->resourceBookingService->getNextAvailableSlot(
            $service,
            $fromDateTime,
            $durationMinutes
        );

        if ($nextSlot) {
            return response()->json([
                'success' => true,
                'available' => true,
                'next_slot' => $nextSlot->toISOString(),
                'next_slot_formatted' => $nextSlot->format('Y-m-d H:i'),
                'end_time' => $nextSlot->copy()->addMinutes($durationMinutes)->toISOString(),
                'message' => 'Next available slot found',
            ]);
        } else {
            return response()->json([
                'success' => true,
                'available' => false,
                'next_slot' => null,
                'message' => 'No available slots found within the next 30 days',
            ]);
        }
    }

    /**
     * Get resource calendar data for a specific resource.
     */
    public function getResourceCalendar(Request $request)
    {
        $business = $this->getUserBusiness();

        $request->validate([
            'resource_id' => 'required|exists:resources,id',
            'start' => 'required|date',
            'end' => 'required|date|after:start',
        ]);

        $resource = $business->resources()->findOrFail($request->resource_id);
        $start = Carbon::parse($request->start);
        $end = Carbon::parse($request->end);

        // Get bookings for this resource
        $bookings = $resource->bookingServiceResources()
            ->with(['bookingService.booking', 'bookingService.service'])
            ->whereBetween('start_datetime', [$start, $end])
            ->get();

        $events = $bookings->map(function ($booking) {
            $bookingData = $booking->bookingService->booking;
            return [
                'id' => 'booking-' . $booking->id,
                'title' => $booking->bookingService->service->name . ' - ' . $bookingData->customer_name,
                'start' => $booking->start_datetime->toISOString(),
                'end' => $booking->end_datetime->toISOString(),
                'backgroundColor' => $this->getStatusColor($bookingData->status),
                'borderColor' => $this->getStatusColor($bookingData->status),
                'extendedProps' => [
                    'type' => 'booking',
                    'booking_id' => $bookingData->id,
                    'customer_name' => $bookingData->customer_name,
                    'service_name' => $booking->bookingService->service->name,
                    'status' => $bookingData->status,
                    'quantity' => $booking->quantity,
                ],
            ];
        });

        // Get availability blocks for this resource
        $blocks = $resource->availabilityBlocks()
            ->whereBetween('start_datetime', [$start, $end])
            ->get();

        $blockEvents = $blocks->map(function ($block) {
            return [
                'id' => 'block-' . $block->id,
                'title' => $block->reason ?: 'Blocked',
                'start' => $block->start_datetime->toISOString(),
                'end' => $block->end_datetime->toISOString(),
                'backgroundColor' => '#dc3545',
                'borderColor' => '#dc3545',
                'display' => 'background',
                'extendedProps' => [
                    'type' => 'block',
                    'block_id' => $block->id,
                    'reason' => $block->reason,
                ],
            ];
        });

        return response()->json([
            'success' => true,
            'events' => $events->merge($blockEvents),
            'resource' => [
                'id' => $resource->id,
                'name' => $resource->name,
                'capacity' => $resource->capacity,
                'type' => $resource->resourceType->name,
            ],
        ]);
    }

    /**
     * Get status color for calendar events.
     */
    protected function getStatusColor($status)
    {
        $colors = [
            'pending' => '#ffc107',
            'confirmed' => '#28a745',
            'in_progress' => '#17a2b8',
            'completed' => '#6c757d',
            'cancelled' => '#dc3545',
            'no_show' => '#fd7e14',
        ];

        return $colors[$status] ?? '#007bff';
    }
}
