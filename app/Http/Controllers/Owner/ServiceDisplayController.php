<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Controller;
use App\Models\LandingServiceSettings;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ServiceDisplayController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'business.isolation']);
    }

    /**
     * Show service display settings
     */
    public function index()
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $settings = $business->landingServiceSettings ?: new LandingServiceSettings(LandingServiceSettings::getDefaultSettings());
        $services = $business->services()->with('category')->get();
        $categories = $business->serviceCategories()->get();

        return view('owner.service-display.index', compact('business', 'settings', 'services', 'categories'));
    }

    /**
     * Update service display settings
     */
    public function update(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $validated = $request->validate([
            'homepage_display_count' => 'required|integer|min:1|max:20',
            'show_pricing' => 'boolean',
            'show_duration' => 'boolean',
            'show_description' => 'boolean',
            'show_images' => 'boolean',
            'show_categories' => 'boolean',
            'layout_type' => 'required|in:grid,list,carousel,masonry',
            'grid_columns' => 'required|integer|min:1|max:6',
            'enable_filtering' => 'boolean',
            'enable_sorting' => 'boolean',
            'enable_search' => 'boolean',
            'featured_services' => 'nullable|array',
            'featured_services.*' => 'exists:services,id',
            'hidden_services' => 'nullable|array',
            'hidden_services.*' => 'exists:services,id',
            'service_order' => 'required|in:manual,alphabetical,price_low_high,price_high_low,duration,popularity',
            'group_by_category' => 'boolean',
            'service_card_style' => 'required|in:modern,classic,minimal,creative',
            'show_service_icons' => 'boolean',
            'show_availability_status' => 'boolean',
            'show_special_offers' => 'boolean',
            'show_reviews_rating' => 'boolean',
            'enable_quick_booking' => 'boolean',
            'booking_button_style' => 'required|in:primary,secondary,outline',
            'booking_button_text' => 'required|string|max:50',
            'show_booking_calendar' => 'boolean',
            'enable_service_seo' => 'boolean',
            'generate_service_sitemap' => 'boolean',
            'track_service_analytics' => 'boolean',
            'mobile_optimized' => 'boolean',
        ]);

        DB::transaction(function () use ($validated, $business) {
            $settings = $business->landingServiceSettings;

            if ($settings) {
                $settings->update($validated);
            } else {
                $validated['business_id'] = $business->id;
                LandingServiceSettings::create($validated);
            }

            // Update business landing page timestamp
            $business->update([
                'landing_page_last_updated' => now(),
            ]);
        });

        return redirect()->back()->with('success', 'Service display settings updated successfully!');
    }

    /**
     * Update individual service display settings
     */
    public function updateService(Request $request, Service $service)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business || $service->business_id !== $business->id) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'is_public' => 'boolean',
            'featured_on_landing' => 'boolean',
            'landing_display_order' => 'nullable|integer|min:0',
            'show_price_on_landing' => 'boolean',
            'show_duration_on_landing' => 'boolean',
            'show_description_on_landing' => 'boolean',
            'show_image_on_landing' => 'boolean',
            'landing_page_title' => 'nullable|string|max:255',
            'landing_page_description' => 'nullable|string|max:500',
            'landing_page_keywords' => 'nullable|string|max:255',
            'quick_booking_enabled' => 'boolean',
            'booking_button_text' => 'nullable|string|max:50',
            'booking_button_color' => 'nullable|string|max:7',
        ]);

        $service->update($validated);

        return redirect()->back()->with('success', 'Service display settings updated successfully!');
    }

    /**
     * Bulk update service visibility
     */
    public function bulkUpdateVisibility(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $validated = $request->validate([
            'service_ids' => 'required|array',
            'service_ids.*' => 'exists:services,id',
            'action' => 'required|in:show,hide,feature,unfeature',
        ]);

        $services = $business->services()->whereIn('id', $validated['service_ids']);

        switch ($validated['action']) {
            case 'show':
                $services->update(['is_public' => true]);
                $message = 'Selected services are now visible on landing page.';
                break;
            case 'hide':
                $services->update(['is_public' => false]);
                $message = 'Selected services are now hidden from landing page.';
                break;
            case 'feature':
                $services->update(['featured_on_landing' => true]);
                $message = 'Selected services are now featured on landing page.';
                break;
            case 'unfeature':
                $services->update(['featured_on_landing' => false]);
                $message = 'Selected services are no longer featured on landing page.';
                break;
        }

        return redirect()->back()->with('success', $message);
    }

    /**
     * Reorder services for landing page display
     */
    public function reorderServices(Request $request)
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'service_orders' => 'required|array',
            'service_orders.*.id' => 'required|exists:services,id',
            'service_orders.*.order' => 'required|integer|min:0',
        ]);

        DB::transaction(function () use ($validated, $business) {
            foreach ($validated['service_orders'] as $serviceOrder) {
                $business->services()
                    ->where('id', $serviceOrder['id'])
                    ->update(['landing_display_order' => $serviceOrder['order']]);
            }
        });

        return response()->json(['success' => true, 'message' => 'Service order updated successfully!']);
    }

    /**
     * Preview service display
     */
    public function preview()
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $settings = $business->landingServiceSettings ?: new LandingServiceSettings(LandingServiceSettings::getDefaultSettings());
        $services = $settings->getHomepageServices();
        $servicesByCategory = $settings->getServicesByCategory();

        return view('owner.service-display.preview', compact('business', 'settings', 'services', 'servicesByCategory'));
    }

    /**
     * Get service analytics
     */
    public function analytics()
    {
        $user = Auth::user();
        $business = $user->business;

        if (!$business) {
            return redirect()->route('owner.dashboard')
                ->with('error', 'You need to create a business first.');
        }

        $services = $business->services()
            ->select('id', 'name', 'landing_page_views', 'landing_page_clicks', 'last_landing_view')
            ->where('is_public', true)
            ->orderByDesc('landing_page_views')
            ->get();

        $totalViews = $services->sum('landing_page_views');
        $totalClicks = $services->sum('landing_page_clicks');
        $conversionRate = $totalViews > 0 ? ($totalClicks / $totalViews) * 100 : 0;

        $analytics = [
            'total_views' => $totalViews,
            'total_clicks' => $totalClicks,
            'conversion_rate' => round($conversionRate, 2),
            'top_services' => $services->take(10),
        ];

        return view('owner.service-display.analytics', compact('business', 'analytics'));
    }
}
