<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessSeoSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'meta_title',
        'meta_description',
        'meta_keywords',
        'canonical_url',
        'og_title',
        'og_description',
        'og_image',
        'og_type',
        'og_locale',
        'twitter_card',
        'twitter_site',
        'twitter_creator',
        'twitter_title',
        'twitter_description',
        'twitter_image',
        'business_schema',
        'service_schema',
        'review_schema',
        'faq_schema',
        'custom_schema',
        'business_type',
        'opening_hours_schema',
        'price_range',
        'geo_coordinates',
        'google_my_business_id',
        'robots_meta',
        'sitemap_enabled',
        'sitemap_last_generated',
        'hreflang_tags',
        'google_analytics_id',
        'google_tag_manager_id',
        'facebook_pixel_id',
        'custom_tracking_codes',
        'amp_enabled',
        'critical_css',
        'lazy_loading_enabled',
    ];

    protected $casts = [
        'business_schema' => 'array',
        'service_schema' => 'array',
        'review_schema' => 'array',
        'faq_schema' => 'array',
        'custom_schema' => 'array',
        'opening_hours_schema' => 'array',
        'geo_coordinates' => 'array',
        'robots_meta' => 'array',
        'sitemap_enabled' => 'boolean',
        'sitemap_last_generated' => 'datetime',
        'hreflang_tags' => 'array',
        'custom_tracking_codes' => 'array',
        'amp_enabled' => 'boolean',
        'critical_css' => 'array',
        'lazy_loading_enabled' => 'boolean',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    // Helper methods
    public function generateBusinessSchema()
    {
        $business = $this->business;
        $mainBranch = $business->branches()->where('is_main_branch', true)->first();

        $schema = [
            '@context' => 'https://schema.org',
            '@type' => $this->business_type ?? 'LocalBusiness',
            'name' => $business->name,
            'description' => $business->description,
            'url' => $business->website ?? $business->landingPage?->full_url,
            'telephone' => $business->phone,
            'email' => $business->email,
        ];

        if ($mainBranch) {
            $schema['address'] = [
                '@type' => 'PostalAddress',
                'streetAddress' => $mainBranch->address,
                'addressLocality' => $mainBranch->city,
                'addressRegion' => $mainBranch->state,
                'postalCode' => $mainBranch->postal_code,
                'addressCountry' => $mainBranch->country,
            ];

            if ($mainBranch->latitude && $mainBranch->longitude) {
                $schema['geo'] = [
                    '@type' => 'GeoCoordinates',
                    'latitude' => $mainBranch->latitude,
                    'longitude' => $mainBranch->longitude,
                ];
            }
        }

        if ($this->opening_hours_schema) {
            $schema['openingHours'] = $this->opening_hours_schema;
        }

        if ($this->price_range) {
            $schema['priceRange'] = $this->price_range;
        }

        // Add services
        $services = $business->services()->where('is_active', true)->get();
        if ($services->isNotEmpty()) {
            $schema['hasOfferCatalog'] = [
                '@type' => 'OfferCatalog',
                'name' => 'Services',
                'itemListElement' => $services->map(function ($service) {
                    return [
                        '@type' => 'Offer',
                        'itemOffered' => [
                            '@type' => 'Service',
                            'name' => $service->name,
                            'description' => $service->description,
                        ],
                        'price' => $service->price,
                        'priceCurrency' => $this->business->currency,
                    ];
                })->toArray()
            ];
        }

        return $schema;
    }

    public function generateOpeningHoursSchema()
    {
        $operatingHours = $this->business->operatingHours;
        $openingHours = [];

        $dayMapping = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];

        foreach ($operatingHours as $hour) {
            if ($hour->is_open) {
                $dayName = $dayMapping[$hour->day_of_week];
                $openingHours[] = $dayName . ' ' . $hour->open_time . '-' . $hour->close_time;
            }
        }

        return $openingHours;
    }

    public function getMetaTags()
    {
        $business = $this->business;
        $tags = [];

        // Basic meta tags
        $tags['title'] = $this->meta_title ?? $business->name;
        $tags['description'] = $this->meta_description ?? $business->description;
        
        if ($this->meta_keywords) {
            $tags['keywords'] = $this->meta_keywords;
        }

        if ($this->canonical_url) {
            $tags['canonical'] = $this->canonical_url;
        }

        // Open Graph tags
        $tags['og:title'] = $this->og_title ?? $tags['title'];
        $tags['og:description'] = $this->og_description ?? $tags['description'];
        $tags['og:type'] = $this->og_type;
        $tags['og:locale'] = $this->og_locale;
        
        if ($this->og_image) {
            $tags['og:image'] = $this->og_image;
        }

        // Twitter Card tags
        $tags['twitter:card'] = $this->twitter_card;
        
        if ($this->twitter_site) {
            $tags['twitter:site'] = $this->twitter_site;
        }
        
        if ($this->twitter_creator) {
            $tags['twitter:creator'] = $this->twitter_creator;
        }

        $tags['twitter:title'] = $this->twitter_title ?? $tags['title'];
        $tags['twitter:description'] = $this->twitter_description ?? $tags['description'];
        
        if ($this->twitter_image) {
            $tags['twitter:image'] = $this->twitter_image;
        }

        // Robots meta
        if ($this->robots_meta) {
            $tags['robots'] = implode(', ', $this->robots_meta);
        }

        return $tags;
    }

    public function generateSitemap()
    {
        if (!$this->sitemap_enabled) {
            return null;
        }

        $business = $this->business;
        $landingPage = $business->landingPage;
        
        if (!$landingPage || !$landingPage->is_published) {
            return null;
        }

        $urls = $landingPage->generateSitemap();
        
        $this->update(['sitemap_last_generated' => now()]);
        
        return $urls;
    }
}
