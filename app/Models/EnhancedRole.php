<?php

namespace App\Models;

use Spatie\Permission\Models\Role as SpatieRole;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class EnhancedRole extends SpatieRole
{
    use HasFactory;

    protected $table = 'roles';

    protected $fillable = [
        'name',
        'guard_name',
        'hierarchy_level',
        'description',
        'is_system_role',
        'max_users',
        'security_level',
        'encrypted_permissions',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'is_system_role' => 'boolean',
        'hierarchy_level' => 'integer',
        'max_users' => 'integer',
        'security_level' => 'integer',
        'encrypted_permissions' => 'array',
    ];

    /**
     * Role hierarchy levels
     */
    const HIERARCHY_LEVELS = [
        0 => 'Super Admin',
        1 => 'Admin',
        2 => 'Business Owner',
        3 => 'Manager',
        4 => 'Staff',
        5 => 'Customer',
    ];

    /**
     * Security levels
     */
    const SECURITY_LEVELS = [
        1 => 'Low',
        2 => 'Medium',
        3 => 'High',
        4 => 'Critical',
        5 => 'Maximum',
    ];

    /**
     * System roles that cannot be deleted
     */
    const SYSTEM_ROLES = [
        'Super Admin',
        'Admin',
        'Business Owner',
        'Manager',
        'Staff',
        'Customer',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($role) {
            $role->created_by = auth()->id();
            $role->security_level = $role->security_level ?? 2;
            $role->hierarchy_level = $role->hierarchy_level ?? 5;
        });

        static::updating(function ($role) {
            $role->updated_by = auth()->id();
        });

        static::saved(function ($role) {
            // Clear role cache when role is saved
            Cache::forget("role_permissions_{$role->id}");
            Cache::forget("role_hierarchy_{$role->id}");
        });
    }

    /**
     * Get the hierarchy level name
     */
    public function getHierarchyLevelNameAttribute()
    {
        return self::HIERARCHY_LEVELS[$this->hierarchy_level] ?? 'Unknown';
    }

    /**
     * Get the security level name
     */
    public function getSecurityLevelNameAttribute()
    {
        return self::SECURITY_LEVELS[$this->security_level] ?? 'Unknown';
    }

    /**
     * Check if role is a system role
     */
    public function isSystemRole()
    {
        return in_array($this->name, self::SYSTEM_ROLES) || $this->is_system_role;
    }

    /**
     * Check if role can be deleted
     */
    public function canBeDeleted()
    {
        return !$this->isSystemRole() && $this->users()->count() === 0;
    }

    /**
     * Check if role can assign another role
     */
    public function canAssignRole($targetRole)
    {
        if (!$targetRole instanceof self) {
            $targetRole = self::findByName($targetRole);
        }

        return $this->hierarchy_level <= $targetRole->hierarchy_level;
    }

    /**
     * Get roles that this role can manage
     */
    public function getManageableRoles()
    {
        return self::where('hierarchy_level', '>=', $this->hierarchy_level)
                   ->where('id', '!=', $this->id)
                   ->get();
    }

    /**
     * Get encrypted sensitive permissions
     */
    public function getEncryptedPermissions()
    {
        if (!$this->encrypted_permissions) {
            return [];
        }

        try {
            return array_map(function ($permission) {
                return Crypt::decrypt($permission);
            }, $this->encrypted_permissions);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Set encrypted sensitive permissions
     */
    public function setEncryptedPermissions(array $permissions)
    {
        $this->encrypted_permissions = array_map(function ($permission) {
            return Crypt::encrypt($permission);
        }, $permissions);
        $this->save();
    }

    /**
     * Check if role has sensitive permissions
     */
    public function hasSensitivePermissions()
    {
        $sensitivePermissions = [
            'manage system settings',
            'manage server configuration',
            'manage backups',
            'manage audit logs',
            'manage security settings',
            'manage database',
            'manage file system',
        ];

        return $this->permissions()
                   ->whereIn('name', $sensitivePermissions)
                   ->exists();
    }

    /**
     * Get role statistics
     */
    public function getStatistics()
    {
        return [
            'users_count' => $this->users()->count(),
            'permissions_count' => $this->permissions()->count(),
            'sensitive_permissions_count' => $this->getSensitivePermissionsCount(),
            'hierarchy_level' => $this->hierarchy_level,
            'security_level' => $this->security_level,
            'is_system_role' => $this->isSystemRole(),
            'can_be_deleted' => $this->canBeDeleted(),
        ];
    }

    /**
     * Get sensitive permissions count
     */
    private function getSensitivePermissionsCount()
    {
        $sensitivePermissions = [
            'manage system settings',
            'manage server configuration',
            'manage backups',
            'manage audit logs',
            'manage security settings',
            'manage database',
            'manage file system',
        ];

        return $this->permissions()
                   ->whereIn('name', $sensitivePermissions)
                   ->count();
    }

    /**
     * Scope for filtering by hierarchy level
     */
    public function scopeByHierarchyLevel($query, $level)
    {
        return $query->where('hierarchy_level', $level);
    }

    /**
     * Scope for filtering by security level
     */
    public function scopeBySecurityLevel($query, $level)
    {
        return $query->where('security_level', $level);
    }

    /**
     * Scope for non-system roles
     */
    public function scopeNonSystem($query)
    {
        return $query->where('is_system_role', false)
                    ->whereNotIn('name', self::SYSTEM_ROLES);
    }

    /**
     * Get role template data
     */
    public function getTemplateData()
    {
        return [
            'name' => $this->name,
            'description' => $this->description,
            'hierarchy_level' => $this->hierarchy_level,
            'security_level' => $this->security_level,
            'permissions' => $this->permissions->pluck('name')->toArray(),
        ];
    }

    /**
     * Create role from template
     */
    public static function createFromTemplate(array $template, string $newName)
    {
        $role = self::create([
            'name' => $newName,
            'description' => $template['description'] ?? '',
            'hierarchy_level' => $template['hierarchy_level'] ?? 5,
            'security_level' => $template['security_level'] ?? 2,
        ]);

        if (isset($template['permissions'])) {
            $role->syncPermissions($template['permissions']);
        }

        return $role;
    }

    /**
     * Get user who created this role
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get user who last updated this role
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
