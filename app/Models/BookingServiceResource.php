<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BookingServiceResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_service_id',
        'resource_id',
        'quantity',
        'start_datetime',
        'end_datetime',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
    ];

    // Relationships
    public function bookingService(): BelongsTo
    {
        return $this->belongsTo(BookingService::class);
    }

    public function resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }
}
