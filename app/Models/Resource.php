<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Resource extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'business_branch_id',
        'resource_type_id',
        'name',
        'slug',
        'description',
        'capacity',
        'hourly_rate',
        'specifications',
        'availability_rules',
        'requires_approval',
        'is_active',
    ];

    protected $casts = [
        'capacity' => 'integer',
        'hourly_rate' => 'decimal:2',
        'specifications' => 'array',
        'availability_rules' => 'array',
        'requires_approval' => 'boolean',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($resource) {
            if (empty($resource->slug)) {
                $resource->slug = Str::slug($resource->name);
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    public function resourceType(): BelongsTo
    {
        return $this->belongsTo(ResourceType::class);
    }

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(Service::class, 'service_resources')
                    ->withPivot('quantity_required', 'is_required', 'setup_time_minutes', 'cleanup_time_minutes')
                    ->withTimestamps();
    }

    public function bookingServiceResources(): HasMany
    {
        return $this->hasMany(BookingServiceResource::class);
    }

    public function availabilityBlocks(): HasMany
    {
        return $this->hasMany(AvailabilityBlock::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $typeId)
    {
        return $query->where('resource_type_id', $typeId);
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('business_branch_id', $branchId);
    }

    public function scopeAvailable($query, $startDateTime, $endDateTime)
    {
        return $query->whereDoesntHave('bookingServiceResources', function ($q) use ($startDateTime, $endDateTime) {
            $q->where(function ($q2) use ($startDateTime, $endDateTime) {
                $q2->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                   ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                   ->orWhere(function ($q3) use ($startDateTime, $endDateTime) {
                       $q3->where('start_datetime', '<=', $startDateTime)
                          ->where('end_datetime', '>=', $endDateTime);
                   });
            });
        })->whereDoesntHave('availabilityBlocks', function ($q) use ($startDateTime, $endDateTime) {
            $q->where(function ($q2) use ($startDateTime, $endDateTime) {
                $q2->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                   ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                   ->orWhere(function ($q3) use ($startDateTime, $endDateTime) {
                       $q3->where('start_datetime', '<=', $startDateTime)
                          ->where('end_datetime', '>=', $endDateTime);
                   });
            });
        });
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function isAvailableAt($startDateTime, $endDateTime, $excludeBookingId = null)
    {
        // Check for conflicting bookings
        $conflictingBookings = $this->bookingServiceResources()
            ->where(function ($q) use ($startDateTime, $endDateTime) {
                $q->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                  ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                  ->orWhere(function ($q2) use ($startDateTime, $endDateTime) {
                      $q2->where('start_datetime', '<=', $startDateTime)
                         ->where('end_datetime', '>=', $endDateTime);
                  });
            });

        if ($excludeBookingId) {
            $conflictingBookings->whereHas('bookingService', function ($q) use ($excludeBookingId) {
                $q->where('booking_id', '!=', $excludeBookingId);
            });
        }

        if ($conflictingBookings->exists()) {
            return false;
        }

        // Check for availability blocks
        $blocks = $this->availabilityBlocks()
            ->where(function ($q) use ($startDateTime, $endDateTime) {
                $q->whereBetween('start_datetime', [$startDateTime, $endDateTime])
                  ->orWhereBetween('end_datetime', [$startDateTime, $endDateTime])
                  ->orWhere(function ($q2) use ($startDateTime, $endDateTime) {
                      $q2->where('start_datetime', '<=', $startDateTime)
                         ->where('end_datetime', '>=', $endDateTime);
                  });
            });

        return !$blocks->exists();
    }
}
