<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ServiceReview extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'service_reviews';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'service_id',
        'user_id',
        'booking_id',
        'rating',
        'title',
        'comment',
        'rating_breakdown',
        'is_verified',
        'is_approved',
        'is_featured',
        'reviewed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rating_breakdown' => 'array',
        'is_verified' => 'boolean',
        'is_approved' => 'boolean',
        'is_featured' => 'boolean',
        'reviewed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the service that owns the review.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the user that wrote the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the booking associated with the review.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Scope to get approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get featured reviews.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get verified reviews.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get reviews for a specific service.
     */
    public function scopeForService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }

    /**
     * Scope to get reviews by a specific user.
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to order by rating (highest first).
     */
    public function scopeOrderByRating($query, $direction = 'desc')
    {
        return $query->orderBy('rating', $direction);
    }

    /**
     * Scope to order by date (newest first).
     */
    public function scopeOrderByDate($query, $direction = 'desc')
    {
        return $query->orderBy('reviewed_at', $direction);
    }

    /**
     * Get the formatted rating as stars.
     */
    public function getStarsAttribute(): string
    {
        $fullStars = floor($this->rating);
        $halfStar = ($this->rating - $fullStars) >= 0.5 ? 1 : 0;
        $emptyStars = 5 - $fullStars - $halfStar;

        return str_repeat('★', $fullStars) .
               str_repeat('☆', $halfStar) .
               str_repeat('☆', $emptyStars);
    }

    /**
     * Get the review age in human readable format.
     */
    public function getAgeAttribute(): string
    {
        return $this->reviewed_at->diffForHumans();
    }

    /**
     * Get the customer's display name (anonymized if needed).
     */
    public function getCustomerNameAttribute(): string
    {
        if (!$this->user) {
            return 'Anonymous';
        }

        $name = $this->user->name;

        // Anonymize the name (show first name and last initial)
        $parts = explode(' ', $name);
        if (count($parts) > 1) {
            return $parts[0] . ' ' . substr($parts[1], 0, 1) . '.';
        }

        return $parts[0];
    }

    /**
     * Check if this review can be edited by the user.
     */
    public function canBeEditedBy($userId): bool
    {
        // Allow editing within 24 hours of creation
        $editWindow = 24; // hours
        $canEdit = $this->user_id === $userId &&
                   $this->created_at->diffInHours(now()) <= $editWindow;

        return $canEdit;
    }

    /**
     * Get average rating for a service.
     */
    public static function getAverageRating($serviceId): float
    {
        return static::where('service_id', $serviceId)
                    ->where('is_approved', true)
                    ->avg('rating') ?? 0;
    }

    /**
     * Get rating distribution for a service.
     */
    public static function getRatingDistribution($serviceId): array
    {
        $distribution = [];

        for ($i = 1; $i <= 5; $i++) {
            $count = static::where('service_id', $serviceId)
                          ->where('is_approved', true)
                          ->where('rating', $i)
                          ->count();
            $distribution[$i] = $count;
        }

        return $distribution;
    }

    /**
     * Get total reviews count for a service.
     */
    public static function getTotalReviews($serviceId): int
    {
        return static::where('service_id', $serviceId)
                    ->where('is_approved', true)
                    ->count();
    }
}
