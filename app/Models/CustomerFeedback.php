<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerFeedback extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_feedback';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'booking_id',
        'service_id',
        'type',
        'rating',
        'title',
        'comment',
        'survey_responses',
        'status',
        'internal_notes',
        'response',
        'responded_at',
        'responded_by',
        'is_public',
        'is_featured',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'survey_responses' => 'array',
        'responded_at' => 'datetime',
        'is_public' => 'boolean',
        'is_featured' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns the feedback.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer who provided the feedback.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the booking associated with the feedback.
     */
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    /**
     * Get the service associated with the feedback.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the user who responded to the feedback.
     */
    public function responder(): BelongsTo
    {
        return $this->belongsTo(User::class, 'responded_by');
    }

    /**
     * Scope to filter by business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get public feedback.
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope to get featured feedback.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to get reviews (with ratings).
     */
    public function scopeReviews($query)
    {
        return $query->where('type', 'review')->whereNotNull('rating');
    }

    /**
     * Scope to filter by rating.
     */
    public function scopeWithRating($query, $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to get high-rated feedback.
     */
    public function scopeHighRated($query, $minRating = 4)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Mark feedback as responded.
     */
    public function markAsResponded($response, $responderId = null)
    {
        $this->update([
            'status' => 'responded',
            'response' => $response,
            'responded_at' => now(),
            'responded_by' => $responderId ?? auth()->id(),
        ]);
    }

    /**
     * Mark feedback as resolved.
     */
    public function markAsResolved()
    {
        $this->update(['status' => 'resolved']);
    }

    /**
     * Toggle public visibility.
     */
    public function togglePublic()
    {
        $this->update(['is_public' => !$this->is_public]);
    }

    /**
     * Toggle featured status.
     */
    public function toggleFeatured()
    {
        $this->update(['is_featured' => !$this->is_featured]);
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge-warning',
            'reviewed' => 'badge-info',
            'responded' => 'badge-primary',
            'resolved' => 'badge-success',
            default => 'badge-secondary',
        };
    }

    /**
     * Get type badge class for UI.
     */
    public function getTypeBadgeClassAttribute()
    {
        return match($this->type) {
            'review' => 'badge-success',
            'complaint' => 'badge-danger',
            'suggestion' => 'badge-info',
            'compliment' => 'badge-primary',
            'survey_response' => 'badge-secondary',
            default => 'badge-secondary',
        };
    }

    /**
     * Get star rating display.
     */
    public function getStarRatingAttribute()
    {
        if (!$this->rating) return '';
        
        $stars = '';
        for ($i = 1; $i <= 5; $i++) {
            $stars .= $i <= $this->rating ? '★' : '☆';
        }
        return $stars;
    }

    /**
     * Get truncated comment for display.
     */
    public function getCommentPreviewAttribute()
    {
        return strlen($this->comment) > 100 ? 
               substr($this->comment, 0, 100) . '...' : 
               $this->comment;
    }

    /**
     * Check if feedback has been responded to.
     */
    public function hasResponse()
    {
        return !empty($this->response);
    }

    /**
     * Get days since feedback was submitted.
     */
    public function getDaysSinceSubmissionAttribute()
    {
        return $this->created_at->diffInDays(now());
    }
}
