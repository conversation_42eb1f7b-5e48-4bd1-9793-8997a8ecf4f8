<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class CustomerTag extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'name',
        'color',
        'description',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns the tag.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the tag assignments for this tag.
     */
    public function assignments(): HasMany
    {
        return $this->hasMany(CustomerTagAssignment::class);
    }

    /**
     * Get the customers that have this tag.
     */
    public function customers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'customer_tag_assignments', 'customer_tag_id', 'customer_id')
                    ->withPivot(['business_id', 'assigned_by', 'assigned_at'])
                    ->withTimestamps();
    }

    /**
     * Scope to get active tags only.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get tags for a specific business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Get the number of customers with this tag.
     */
    public function getCustomerCountAttribute(): int
    {
        return $this->assignments()->count();
    }

    /**
     * Get the tag's display name with customer count.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->customer_count . ')';
    }

    /**
     * Get predefined tag colors.
     */
    public static function getPredefinedColors(): array
    {
        return [
            '#007bff' => 'Blue',
            '#28a745' => 'Green',
            '#dc3545' => 'Red',
            '#ffc107' => 'Yellow',
            '#17a2b8' => 'Cyan',
            '#6f42c1' => 'Purple',
            '#e83e8c' => 'Pink',
            '#fd7e14' => 'Orange',
            '#20c997' => 'Teal',
            '#6c757d' => 'Gray',
        ];
    }

    /**
     * Create default tags for a business.
     */
    public static function createDefaultTags($businessId): void
    {
        $defaultTags = [
            ['name' => 'VIP Customer', 'color' => '#ffc107', 'description' => 'High-value customers'],
            ['name' => 'New Customer', 'color' => '#28a745', 'description' => 'Recently acquired customers'],
            ['name' => 'Regular Customer', 'color' => '#007bff', 'description' => 'Frequent visitors'],
            ['name' => 'At Risk', 'color' => '#dc3545', 'description' => 'Customers who haven\'t visited recently'],
            ['name' => 'Birthday This Month', 'color' => '#e83e8c', 'description' => 'Customers with birthdays this month'],
        ];

        foreach ($defaultTags as $tag) {
            static::create(array_merge($tag, ['business_id' => $businessId]));
        }
    }
}
