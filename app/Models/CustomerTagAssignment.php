<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerTagAssignment extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'customer_tag_id',
        'assigned_by',
        'assigned_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'assigned_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns this assignment.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer for this assignment.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the tag for this assignment.
     */
    public function tag(): BelongsTo
    {
        return $this->belongsTo(CustomerTag::class, 'customer_tag_id');
    }

    /**
     * Get the user who assigned this tag.
     */
    public function assignedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }

    /**
     * Scope to get assignments for a specific business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get assignments for a specific customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to get assignments for a specific tag.
     */
    public function scopeForTag($query, $tagId)
    {
        return $query->where('customer_tag_id', $tagId);
    }

    /**
     * Assign a tag to a customer.
     */
    public static function assignTag($businessId, $customerId, $tagId, $assignedBy = null): self
    {
        return static::firstOrCreate([
            'business_id' => $businessId,
            'customer_id' => $customerId,
            'customer_tag_id' => $tagId,
        ], [
            'assigned_by' => $assignedBy ?: auth()->id(),
            'assigned_at' => now(),
        ]);
    }

    /**
     * Remove a tag from a customer.
     */
    public static function removeTag($businessId, $customerId, $tagId): bool
    {
        return static::where([
            'business_id' => $businessId,
            'customer_id' => $customerId,
            'customer_tag_id' => $tagId,
        ])->delete();
    }

    /**
     * Get all tags for a customer in a business.
     */
    public static function getCustomerTags($businessId, $customerId)
    {
        return static::where([
            'business_id' => $businessId,
            'customer_id' => $customerId,
        ])->with('tag')->get();
    }
}
