<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class BusinessBranch extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'name',
        'slug',
        'description',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'latitude',
        'longitude',
        'google_maps_data',
        'manager_name',
        'manager_email',
        'manager_phone',
        'is_main_branch',
        'is_active',
    ];

    protected $casts = [
        'google_maps_data' => 'array',
        'is_main_branch' => 'boolean',
        'is_active' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($branch) {
            if (empty($branch->slug)) {
                $branch->slug = Str::slug($branch->name);
            }
        });
    }

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function operatingHours(): HasMany
    {
        return $this->hasMany(BusinessOperatingHour::class, 'business_branch_id');
    }

    public function holidays(): HasMany
    {
        return $this->hasMany(BusinessHoliday::class, 'business_branch_id');
    }

    public function resources(): HasMany
    {
        return $this->hasMany(Resource::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function availabilityBlocks(): HasMany
    {
        return $this->hasMany(AvailabilityBlock::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeMainBranch($query)
    {
        return $query->where('is_main_branch', true);
    }

    // Helper methods
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    public function getGoogleMapsUrlAttribute()
    {
        if ($this->latitude && $this->longitude) {
            return "https://www.google.com/maps?q={$this->latitude},{$this->longitude}";
        }

        return "https://www.google.com/maps/search/" . urlencode($this->full_address);
    }

    public function getOperatingHoursSummaryAttribute()
    {
        $hours = $this->operatingHours()->orderBy('day_of_week')->get();

        if ($hours->isEmpty()) {
            return 'No hours set';
        }

        $openDays = $hours->where('is_closed', false);

        if ($openDays->isEmpty()) {
            return 'Closed all days';
        }

        // Check if all open days have the same hours
        $firstOpenDay = $openDays->first();
        $sameHours = $openDays->every(function ($hour) use ($firstOpenDay) {
            return $hour->open_time == $firstOpenDay->open_time &&
                   $hour->close_time == $firstOpenDay->close_time;
        });

        if ($sameHours && $openDays->count() == 7) {
            return 'Daily: ' . $firstOpenDay->open_time->format('g:i A') . ' - ' . $firstOpenDay->close_time->format('g:i A');
        }

        if ($sameHours && $openDays->count() >= 5) {
            $dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
            $openDayNames = $openDays->map(function ($hour) use ($dayNames) {
                return $dayNames[$hour->day_of_week];
            })->implode(', ');

            return $openDayNames . ': ' . $firstOpenDay->open_time->format('g:i A') . ' - ' . $firstOpenDay->close_time->format('g:i A');
        }

        return $openDays->count() . ' days open';
    }

    public function getHolidaysSummaryAttribute()
    {
        $holidays = $this->holidays()->active()->get();

        if ($holidays->isEmpty()) {
            return 'No holidays set';
        }

        $upcomingHolidays = $holidays->where('start_date', '>=', now())->count();
        $recurringHolidays = $holidays->where('is_recurring', true)->count();

        if ($upcomingHolidays === 0) {
            return $recurringHolidays > 0 ? "{$recurringHolidays} recurring holidays" : 'No upcoming holidays';
        }

        $summary = "{$upcomingHolidays} upcoming";
        if ($recurringHolidays > 0) {
            $summary .= ", {$recurringHolidays} recurring";
        }

        return $summary;
    }
}
