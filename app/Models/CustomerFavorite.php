<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerFavorite extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_favorites';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'service_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that owns the favorite.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the service that is favorited.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Scope to get favorites for a specific user.
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get favorites for a specific service.
     */
    public function scopeForService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }

    /**
     * Check if a user has favorited a specific service.
     */
    public static function isFavorited($userId, $serviceId): bool
    {
        return static::where('user_id', $userId)
                    ->where('service_id', $serviceId)
                    ->exists();
    }

    /**
     * Toggle favorite status for a user and service.
     */
    public static function toggle($userId, $serviceId): array
    {
        $favorite = static::where('user_id', $userId)
                         ->where('service_id', $serviceId)
                         ->first();

        if ($favorite) {
            $favorite->delete();
            return ['favorited' => false, 'action' => 'removed'];
        } else {
            static::create([
                'user_id' => $userId,
                'service_id' => $serviceId,
            ]);
            return ['favorited' => true, 'action' => 'added'];
        }
    }
}
