<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class BusinessHoliday extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'business_branch_id',
        'name',
        'start_date',
        'end_date',
        'is_recurring',
        'recurrence_type',
        'recurrence_data',
        'description',
        'is_active',
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'is_recurring' => 'boolean',
        'recurrence_data' => 'array',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_date', [$startDate, $endDate])
              ->orWhereBetween('end_date', [$startDate, $endDate])
              ->orWhere(function ($q2) use ($startDate, $endDate) {
                  $q2->where('start_date', '<=', $startDate)
                     ->where('end_date', '>=', $endDate);
              });
        });
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    // Helper methods
    public function isActiveOnDate($date)
    {
        if (!$this->is_active) {
            return false;
        }

        $checkDate = Carbon::parse($date);

        if (!$this->is_recurring) {
            return $checkDate->between($this->start_date, $this->end_date);
        }

        return $this->checkRecurringDate($checkDate);
    }

    protected function checkRecurringDate($date)
    {
        if (!$this->is_recurring) {
            return false;
        }

        switch ($this->recurrence_type) {
            case 'yearly':
                return $this->checkYearlyRecurrence($date);
            case 'monthly':
                return $this->checkMonthlyRecurrence($date);
            case 'weekly':
                return $this->checkWeeklyRecurrence($date);
            default:
                return false;
        }
    }

    protected function checkYearlyRecurrence($date)
    {
        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);

        // Check if the date falls within the same month/day range
        return $date->format('m-d') >= $startDate->format('m-d') &&
               $date->format('m-d') <= $endDate->format('m-d');
    }

    protected function checkMonthlyRecurrence($date)
    {
        $startDate = Carbon::parse($this->start_date);
        $endDate = Carbon::parse($this->end_date);

        // Check if the date falls within the same day range of any month
        return $date->day >= $startDate->day && $date->day <= $endDate->day;
    }

    protected function checkWeeklyRecurrence($date)
    {
        $recurrenceData = $this->recurrence_data;

        if (isset($recurrenceData['days_of_week'])) {
            return in_array($date->dayOfWeek, $recurrenceData['days_of_week']);
        }

        return false;
    }
}
