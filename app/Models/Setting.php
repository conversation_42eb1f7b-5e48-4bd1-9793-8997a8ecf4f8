<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'group',
        'value',
        'display_name',
        'type',
        'options',
        'description',
        'is_public',
        'order',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function getValue(string $key, $default = null)
    {
        return Cache::rememberForever("setting_{$key}", function () use ($key, $default) {
            $setting = self::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    /**
     * Set a setting value by key
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function setValue(string $key, $value): bool
    {
        $setting = self::where('key', $key)->first();

        if ($setting) {
            // Update existing setting
            $updated = $setting->update(['value' => $value]);
        } else {
            // Create new setting with default values
            $setting = self::create([
                'key' => $key,
                'group' => 'general', // Default group
                'value' => $value,
                'display_name' => ucwords(str_replace('_', ' ', $key)),
                'type' => 'text',
                'description' => null,
                'is_public' => false,
                'order' => 0,
            ]);
            $updated = (bool) $setting;
        }

        if ($updated) {
            Cache::forget("setting_{$key}");
        }

        return (bool) $updated;
    }

    /**
     * Get all settings by group
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup(string $group)
    {
        return self::where('group', $group)
            ->orderBy('order')
            ->get();
    }

    /**
     * Get all settings groups
     *
     * @return \Illuminate\Support\Collection
     */
    public static function getGroups()
    {
        return self::select('group')
            ->distinct()
            ->orderBy('group')
            ->pluck('group');
    }

    /**
     * Get options as array
     *
     * @return array
     */
    public function getOptionsArrayAttribute()
    {
        return $this->options ? json_decode($this->options, true) : [];
    }

    /**
     * Clear all settings cache
     *
     * @return void
     */
    public static function clearCache()
    {
        $settings = self::all();

        foreach ($settings as $setting) {
            Cache::forget("setting_{$setting->key}");
        }
    }
}
