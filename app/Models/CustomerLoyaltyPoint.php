<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerLoyaltyPoint extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'type',
        'points',
        'points_value',
        'description',
        'reference_type',
        'reference_id',
        'processed_by',
        'expires_at',
        'is_expired',
        'running_balance',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'points_value' => 'decimal:2',
        'expires_at' => 'date',
        'is_expired' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns this points transaction.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer for this points transaction.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the user who processed this transaction.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Scope to get transactions for a specific business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get transactions for a specific customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to get earned points.
     */
    public function scopeEarned($query)
    {
        return $query->where('type', 'earned');
    }

    /**
     * Scope to get redeemed points.
     */
    public function scopeRedeemed($query)
    {
        return $query->where('type', 'redeemed');
    }

    /**
     * Scope to get non-expired points.
     */
    public function scopeActive($query)
    {
        return $query->where('is_expired', false)
                    ->where(function($q) {
                        $q->whereNull('expires_at')
                          ->orWhere('expires_at', '>', now());
                    });
    }

    /**
     * Scope to get expired points.
     */
    public function scopeExpired($query)
    {
        return $query->where('is_expired', true)
                    ->orWhere('expires_at', '<=', now());
    }

    /**
     * Award points to a customer.
     */
    public static function awardPoints($businessId, $customerId, $points, $description, $referenceType = null, $referenceId = null, $expiresAt = null): self
    {
        $currentBalance = static::getCustomerBalance($businessId, $customerId);
        
        return static::create([
            'business_id' => $businessId,
            'customer_id' => $customerId,
            'type' => 'earned',
            'points' => $points,
            'description' => $description,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'processed_by' => auth()->id(),
            'expires_at' => $expiresAt,
            'running_balance' => $currentBalance + $points,
        ]);
    }

    /**
     * Redeem points from a customer.
     */
    public static function redeemPoints($businessId, $customerId, $points, $description, $referenceType = null, $referenceId = null): self
    {
        $currentBalance = static::getCustomerBalance($businessId, $customerId);
        
        if ($currentBalance < $points) {
            throw new \Exception('Insufficient points balance');
        }
        
        return static::create([
            'business_id' => $businessId,
            'customer_id' => $customerId,
            'type' => 'redeemed',
            'points' => -$points,
            'description' => $description,
            'reference_type' => $referenceType,
            'reference_id' => $referenceId,
            'processed_by' => auth()->id(),
            'running_balance' => $currentBalance - $points,
        ]);
    }

    /**
     * Get customer's current points balance.
     */
    public static function getCustomerBalance($businessId, $customerId): int
    {
        return static::forBusiness($businessId)
                    ->forCustomer($customerId)
                    ->active()
                    ->sum('points');
    }

    /**
     * Get customer's points history.
     */
    public static function getCustomerHistory($businessId, $customerId, $limit = 50)
    {
        return static::forBusiness($businessId)
                    ->forCustomer($customerId)
                    ->with(['processedBy'])
                    ->orderBy('created_at', 'desc')
                    ->limit($limit)
                    ->get();
    }

    /**
     * Expire old points.
     */
    public static function expireOldPoints(): int
    {
        return static::where('expires_at', '<=', now())
                    ->where('is_expired', false)
                    ->update(['is_expired' => true]);
    }

    /**
     * Get the transaction type icon.
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            'earned' => 'fas fa-plus-circle text-success',
            'redeemed' => 'fas fa-minus-circle text-danger',
            'expired' => 'fas fa-clock text-warning',
            'adjusted' => 'fas fa-edit text-info',
            'bonus' => 'fas fa-gift text-primary',
            default => 'fas fa-circle',
        };
    }

    /**
     * Get formatted points display.
     */
    public function getFormattedPointsAttribute(): string
    {
        $prefix = $this->points > 0 ? '+' : '';
        return $prefix . number_format($this->points);
    }

    /**
     * Check if points are expired.
     */
    public function isExpired(): bool
    {
        return $this->is_expired || ($this->expires_at && $this->expires_at->isPast());
    }
}
