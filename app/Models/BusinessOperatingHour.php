<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BusinessOperatingHour extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'business_branch_id',
        'day_of_week',
        'open_time',
        'close_time',
        'is_closed',
        'break_times',
    ];

    protected $casts = [
        'break_times' => 'array',
        'is_closed' => 'boolean',
        'day_of_week' => 'integer',
        'open_time' => 'datetime:H:i',
        'close_time' => 'datetime:H:i',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    // Scopes
    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeOpen($query)
    {
        return $query->where('is_closed', false);
    }

    // Helper methods
    public function getDayNameAttribute()
    {
        $days = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];

        return $days[$this->day_of_week] ?? 'Unknown';
    }

    public function isOpenAt($time)
    {
        if ($this->is_closed) {
            return false;
        }

        $checkTime = is_string($time) ? strtotime($time) : $time;
        $openTime = strtotime($this->open_time);
        $closeTime = strtotime($this->close_time);

        // Handle overnight hours (e.g., 22:00 to 02:00)
        if ($closeTime < $openTime) {
            return $checkTime >= $openTime || $checkTime <= $closeTime;
        }

        return $checkTime >= $openTime && $checkTime <= $closeTime;
    }

    public function isOnBreakAt($time)
    {
        if (!$this->break_times || empty($this->break_times)) {
            return false;
        }

        $checkTime = is_string($time) ? strtotime($time) : $time;

        foreach ($this->break_times as $break) {
            $breakStart = strtotime($break['start']);
            $breakEnd = strtotime($break['end']);

            if ($checkTime >= $breakStart && $checkTime <= $breakEnd) {
                return true;
            }
        }

        return false;
    }
}
