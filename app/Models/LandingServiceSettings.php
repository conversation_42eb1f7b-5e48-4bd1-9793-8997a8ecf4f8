<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LandingServiceSettings extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'homepage_display_count',
        'show_pricing',
        'show_duration',
        'show_description',
        'show_images',
        'show_categories',
        'layout_type',
        'grid_columns',
        'enable_filtering',
        'enable_sorting',
        'enable_search',
        'featured_services',
        'hidden_services',
        'service_order',
        'group_by_category',
        'service_card_style',
        'show_service_icons',
        'show_availability_status',
        'show_special_offers',
        'show_reviews_rating',
        'enable_quick_booking',
        'booking_button_style',
        'booking_button_text',
        'show_booking_calendar',
        'enable_service_seo',
        'generate_service_sitemap',
        'track_service_analytics',
        'seo_config',
        'mobile_config',
        'tablet_config',
        'mobile_optimized',
    ];

    protected $casts = [
        'show_pricing' => 'boolean',
        'show_duration' => 'boolean',
        'show_description' => 'boolean',
        'show_images' => 'boolean',
        'show_categories' => 'boolean',
        'enable_filtering' => 'boolean',
        'enable_sorting' => 'boolean',
        'enable_search' => 'boolean',
        'featured_services' => 'array',
        'hidden_services' => 'array',
        'group_by_category' => 'boolean',
        'show_service_icons' => 'boolean',
        'show_availability_status' => 'boolean',
        'show_special_offers' => 'boolean',
        'show_reviews_rating' => 'boolean',
        'enable_quick_booking' => 'boolean',
        'show_booking_calendar' => 'boolean',
        'enable_service_seo' => 'boolean',
        'generate_service_sitemap' => 'boolean',
        'track_service_analytics' => 'boolean',
        'seo_config' => 'array',
        'mobile_config' => 'array',
        'tablet_config' => 'array',
        'mobile_optimized' => 'boolean',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    // Helper methods
    public function getFeaturedServicesQuery()
    {
        if (empty($this->featured_services)) {
            return $this->business->services()->where('featured_on_landing', true);
        }

        return $this->business->services()->whereIn('id', $this->featured_services);
    }

    public function getVisibleServicesQuery()
    {
        $query = $this->business->services()
            ->where('is_active', true)
            ->where('is_public', true);

        if (!empty($this->hidden_services)) {
            $query->whereNotIn('id', $this->hidden_services);
        }

        return $query;
    }

    public function getOrderedServicesQuery()
    {
        $query = $this->getVisibleServicesQuery();

        switch ($this->service_order) {
            case 'alphabetical':
                return $query->orderBy('name');
            case 'price_low_high':
                return $query->orderBy('base_price');
            case 'price_high_low':
                return $query->orderByDesc('base_price');
            case 'duration':
                return $query->orderBy('duration_minutes');
            case 'popularity':
                return $query->orderByDesc('landing_page_views');
            case 'manual':
            default:
                return $query->orderBy('landing_display_order')->orderBy('sort_order');
        }
    }

    public function getHomepageServices()
    {
        return $this->getOrderedServicesQuery()
            ->limit($this->homepage_display_count)
            ->get();
    }

    public function getServicesByCategory()
    {
        if (!$this->group_by_category) {
            return collect([
                'uncategorized' => $this->getOrderedServicesQuery()->get()
            ]);
        }

        $services = $this->getOrderedServicesQuery()->with('category')->get();
        return $services->groupBy(function ($service) {
            return $service->category ? $service->category->name : 'Uncategorized';
        });
    }

    public function getLayoutConfig()
    {
        $config = [
            'type' => $this->layout_type,
            'columns' => $this->grid_columns,
            'card_style' => $this->service_card_style,
        ];

        if ($this->mobile_config) {
            $config['mobile'] = $this->mobile_config;
        }

        if ($this->tablet_config) {
            $config['tablet'] = $this->tablet_config;
        }

        return $config;
    }

    public function getDisplayConfig()
    {
        return [
            'show_pricing' => $this->show_pricing,
            'show_duration' => $this->show_duration,
            'show_description' => $this->show_description,
            'show_images' => $this->show_images,
            'show_categories' => $this->show_categories,
            'show_service_icons' => $this->show_service_icons,
            'show_availability_status' => $this->show_availability_status,
            'show_special_offers' => $this->show_special_offers,
            'show_reviews_rating' => $this->show_reviews_rating,
        ];
    }

    public function getBookingConfig()
    {
        return [
            'enable_quick_booking' => $this->enable_quick_booking,
            'booking_button_style' => $this->booking_button_style,
            'booking_button_text' => $this->booking_button_text,
            'show_booking_calendar' => $this->show_booking_calendar,
        ];
    }

    public function getInteractionConfig()
    {
        return [
            'enable_filtering' => $this->enable_filtering,
            'enable_sorting' => $this->enable_sorting,
            'enable_search' => $this->enable_search,
        ];
    }

    public static function getDefaultSettings()
    {
        return [
            'homepage_display_count' => 6,
            'show_pricing' => true,
            'show_duration' => true,
            'show_description' => true,
            'show_images' => true,
            'show_categories' => true,
            'layout_type' => 'grid',
            'grid_columns' => 3,
            'enable_filtering' => true,
            'enable_sorting' => true,
            'enable_search' => true,
            'service_order' => 'manual',
            'group_by_category' => true,
            'service_card_style' => 'modern',
            'show_service_icons' => true,
            'show_availability_status' => true,
            'show_special_offers' => true,
            'show_reviews_rating' => true,
            'enable_quick_booking' => true,
            'booking_button_style' => 'primary',
            'booking_button_text' => 'Book Now',
            'show_booking_calendar' => false,
            'enable_service_seo' => true,
            'generate_service_sitemap' => true,
            'track_service_analytics' => true,
            'mobile_optimized' => true,
        ];
    }
}
