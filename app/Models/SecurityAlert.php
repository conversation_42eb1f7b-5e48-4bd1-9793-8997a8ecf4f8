<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class SecurityAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'severity',
        'user_id',
        'message',
        'details',
        'ip_address',
        'user_agent',
        'status',
        'resolved_by',
        'resolved_at',
        'resolution_notes',
    ];

    protected $casts = [
        'details' => 'array',
        'resolved_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Alert types
     */
    const TYPES = [
        'privilege_escalation' => 'Privilege Escalation',
        'unusual_access' => 'Unusual Access Pattern',
        'brute_force' => 'Brute Force Attack',
        'sensitive_access' => 'Sensitive Permission Access',
        'hierarchy_violation' => 'Role Hierarchy Violation',
        'off_hours_access' => 'Off-Hours Access',
        'multiple_ip_access' => 'Multiple IP Access',
        'failed_2fa' => 'Failed 2FA Attempts',
        'suspicious_activity' => 'Suspicious Activity',
        'data_breach_attempt' => 'Data Breach Attempt',
    ];

    /**
     * Severity levels
     */
    const SEVERITIES = [
        'low' => 'Low',
        'medium' => 'Medium',
        'high' => 'High',
        'critical' => 'Critical',
    ];

    /**
     * Alert statuses
     */
    const STATUSES = [
        'open' => 'Open',
        'investigating' => 'Investigating',
        'resolved' => 'Resolved',
        'false_positive' => 'False Positive',
        'ignored' => 'Ignored',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($alert) {
            $alert->status = $alert->status ?? 'open';
            
            if (!$alert->timestamp) {
                $alert->timestamp = now();
            }
        });
    }

    /**
     * Get the user associated with the alert
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who resolved the alert
     */
    public function resolver()
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }

    /**
     * Get type name
     */
    public function getTypeNameAttribute()
    {
        return self::TYPES[$this->type] ?? $this->type;
    }

    /**
     * Get severity name
     */
    public function getSeverityNameAttribute()
    {
        return self::SEVERITIES[$this->severity] ?? $this->severity;
    }

    /**
     * Get status name
     */
    public function getStatusNameAttribute()
    {
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Get severity color for UI
     */
    public function getSeverityColorAttribute()
    {
        $colors = [
            'low' => 'success',
            'medium' => 'warning',
            'high' => 'danger',
            'critical' => 'dark',
        ];

        return $colors[$this->severity] ?? 'secondary';
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            'open' => 'danger',
            'investigating' => 'warning',
            'resolved' => 'success',
            'false_positive' => 'info',
            'ignored' => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    /**
     * Scope for open alerts
     */
    public function scopeOpen($query)
    {
        return $query->where('status', 'open');
    }

    /**
     * Scope for critical alerts
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    /**
     * Scope for high severity alerts
     */
    public function scopeHigh($query)
    {
        return $query->where('severity', 'high');
    }

    /**
     * Scope for recent alerts
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for alerts by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for alerts by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Resolve the alert
     */
    public function resolve(string $notes = null): bool
    {
        $this->status = 'resolved';
        $this->resolved_by = Auth::id();
        $this->resolved_at = now();
        $this->resolution_notes = $notes;

        return $this->save();
    }

    /**
     * Mark as false positive
     */
    public function markAsFalsePositive(string $notes = null): bool
    {
        $this->status = 'false_positive';
        $this->resolved_by = Auth::id();
        $this->resolved_at = now();
        $this->resolution_notes = $notes;

        return $this->save();
    }

    /**
     * Start investigation
     */
    public function startInvestigation(): bool
    {
        $this->status = 'investigating';
        return $this->save();
    }

    /**
     * Ignore the alert
     */
    public function ignore(string $notes = null): bool
    {
        $this->status = 'ignored';
        $this->resolved_by = Auth::id();
        $this->resolved_at = now();
        $this->resolution_notes = $notes;

        return $this->save();
    }

    /**
     * Check if alert is resolved
     */
    public function isResolved(): bool
    {
        return in_array($this->status, ['resolved', 'false_positive', 'ignored']);
    }

    /**
     * Check if alert is critical
     */
    public function isCritical(): bool
    {
        return $this->severity === 'critical';
    }

    /**
     * Check if alert is high priority
     */
    public function isHighPriority(): bool
    {
        return in_array($this->severity, ['critical', 'high']);
    }

    /**
     * Get formatted details for display
     */
    public function getFormattedDetailsAttribute()
    {
        if (!$this->details) {
            return 'No additional details available.';
        }

        $formatted = [];
        foreach ($this->details as $key => $value) {
            $formatted[] = ucfirst(str_replace('_', ' ', $key)) . ': ' . (is_array($value) ? implode(', ', $value) : $value);
        }

        return implode('<br>', $formatted);
    }

    /**
     * Get time since alert was created
     */
    public function getTimeSinceAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get alert priority score for sorting
     */
    public function getPriorityScoreAttribute()
    {
        $severityScores = [
            'low' => 1,
            'medium' => 2,
            'high' => 3,
            'critical' => 4,
        ];

        $statusScores = [
            'open' => 4,
            'investigating' => 3,
            'resolved' => 1,
            'false_positive' => 1,
            'ignored' => 1,
        ];

        return ($severityScores[$this->severity] ?? 1) * ($statusScores[$this->status] ?? 1);
    }

    /**
     * Create security alert
     */
    public static function createAlert(array $data): self
    {
        return self::create([
            'type' => $data['type'],
            'severity' => $data['severity'],
            'user_id' => $data['user_id'] ?? null,
            'message' => $data['message'],
            'details' => $data['details'] ?? [],
            'ip_address' => $data['ip_address'] ?? request()->ip(),
            'user_agent' => $data['user_agent'] ?? request()->userAgent(),
        ]);
    }

    /**
     * Get statistics for dashboard
     */
    public static function getStatistics($hours = 24): array
    {
        $timeWindow = now()->subHours($hours);

        return [
            'total' => self::where('created_at', '>=', $timeWindow)->count(),
            'open' => self::where('created_at', '>=', $timeWindow)->where('status', 'open')->count(),
            'critical' => self::where('created_at', '>=', $timeWindow)->where('severity', 'critical')->count(),
            'high' => self::where('created_at', '>=', $timeWindow)->where('severity', 'high')->count(),
            'resolved' => self::where('created_at', '>=', $timeWindow)->where('status', 'resolved')->count(),
            'by_type' => self::where('created_at', '>=', $timeWindow)
                ->groupBy('type')
                ->selectRaw('type, count(*) as count')
                ->pluck('count', 'type')
                ->toArray(),
        ];
    }
}
