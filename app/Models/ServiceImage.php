<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class ServiceImage extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'service_images';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'service_id',
        'filename',
        'original_name',
        'path',
        'url',
        'mime_type',
        'file_size',
        'width',
        'height',
        'alt_text',
        'description',
        'is_primary',
        'is_featured',
        'is_active',
        'sort_order',
        'metadata',
        'uploaded_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
        'uploaded_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the service that owns the image.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Scope to get active images.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get primary images.
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to get featured images.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get the full URL for the image.
     */
    public function getFullUrlAttribute(): string
    {
        if ($this->url) {
            return $this->url;
        }

        return Storage::url($this->path);
    }

    /**
     * Get the thumbnail URL for the image.
     */
    public function getThumbnailUrlAttribute(): string
    {
        // Generate thumbnail path
        $pathInfo = pathinfo($this->path);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];

        if (Storage::exists($thumbnailPath)) {
            return Storage::url($thumbnailPath);
        }

        // Fallback to original image
        return $this->full_url;
    }

    /**
     * Get the medium-sized URL for the image.
     */
    public function getMediumUrlAttribute(): string
    {
        // Generate medium path
        $pathInfo = pathinfo($this->path);
        $mediumPath = $pathInfo['dirname'] . '/medium/' . $pathInfo['filename'] . '_medium.' . $pathInfo['extension'];

        if (Storage::exists($mediumPath)) {
            return Storage::url($mediumPath);
        }

        // Fallback to original image
        return $this->full_url;
    }

    /**
     * Get formatted file size.
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;

        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Get image dimensions as string.
     */
    public function getDimensionsAttribute(): string
    {
        if ($this->width && $this->height) {
            return $this->width . ' × ' . $this->height;
        }

        return 'Unknown';
    }

    /**
     * Check if image is landscape orientation.
     */
    public function isLandscape(): bool
    {
        return $this->width > $this->height;
    }

    /**
     * Check if image is portrait orientation.
     */
    public function isPortrait(): bool
    {
        return $this->height > $this->width;
    }

    /**
     * Check if image is square.
     */
    public function isSquare(): bool
    {
        return $this->width === $this->height;
    }

    /**
     * Set as primary image (and unset others).
     */
    public function setAsPrimary(): bool
    {
        // Unset other primary images for this service
        static::where('service_id', $this->service_id)
              ->where('id', '!=', $this->id)
              ->update(['is_primary' => false]);

        // Set this as primary
        return $this->update(['is_primary' => true]);
    }

    /**
     * Delete the image file from storage.
     */
    public function deleteFile(): bool
    {
        $deleted = true;

        // Delete original file
        if (Storage::exists($this->path)) {
            $deleted = Storage::delete($this->path) && $deleted;
        }

        // Delete thumbnail if exists
        $pathInfo = pathinfo($this->path);
        $thumbnailPath = $pathInfo['dirname'] . '/thumbnails/' . $pathInfo['filename'] . '_thumb.' . $pathInfo['extension'];
        if (Storage::exists($thumbnailPath)) {
            $deleted = Storage::delete($thumbnailPath) && $deleted;
        }

        // Delete medium size if exists
        $mediumPath = $pathInfo['dirname'] . '/medium/' . $pathInfo['filename'] . '_medium.' . $pathInfo['extension'];
        if (Storage::exists($mediumPath)) {
            $deleted = Storage::delete($mediumPath) && $deleted;
        }

        return $deleted;
    }

    /**
     * Boot method to handle model events.
     */
    protected static function boot()
    {
        parent::boot();

        // Delete file when model is deleted
        static::deleting(function ($image) {
            $image->deleteFile();
        });

        // Ensure only one primary image per service
        static::saving(function ($image) {
            if ($image->is_primary) {
                static::where('service_id', $image->service_id)
                      ->where('id', '!=', $image->id)
                      ->update(['is_primary' => false]);
            }
        });
    }
}
