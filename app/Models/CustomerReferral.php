<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class CustomerReferral extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'referrer_id',
        'referred_id',
        'referred_name',
        'referred_email',
        'referred_phone',
        'status',
        'referred_at',
        'contacted_at',
        'converted_at',
        'referral_reward',
        'reward_claimed',
        'reward_claimed_at',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'referred_at' => 'datetime',
        'contacted_at' => 'datetime',
        'converted_at' => 'datetime',
        'reward_claimed_at' => 'datetime',
        'referral_reward' => 'decimal:2',
        'reward_claimed' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns the referral.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer who made the referral.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the referred customer (if they signed up).
     */
    public function referred(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referred_id');
    }

    /**
     * Scope to filter by business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get converted referrals.
     */
    public function scopeConverted($query)
    {
        return $query->where('status', 'converted');
    }

    /**
     * Scope to get pending referrals.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Mark referral as contacted.
     */
    public function markAsContacted()
    {
        $this->update([
            'status' => 'contacted',
            'contacted_at' => now(),
        ]);
    }

    /**
     * Mark referral as converted.
     */
    public function markAsConverted($referredUserId = null)
    {
        $this->update([
            'status' => 'converted',
            'converted_at' => now(),
            'referred_id' => $referredUserId,
        ]);
    }

    /**
     * Claim referral reward.
     */
    public function claimReward()
    {
        $this->update([
            'reward_claimed' => true,
            'reward_claimed_at' => now(),
        ]);
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute()
    {
        return match($this->status) {
            'pending' => 'badge-warning',
            'contacted' => 'badge-info',
            'converted' => 'badge-success',
            'declined' => 'badge-danger',
            default => 'badge-secondary',
        };
    }

    /**
     * Get days since referral.
     */
    public function getDaysSinceReferralAttribute()
    {
        return $this->referred_at->diffInDays(now());
    }

    /**
     * Check if referral is eligible for reward.
     */
    public function isEligibleForReward()
    {
        return $this->status === 'converted' && 
               $this->referral_reward > 0 && 
               !$this->reward_claimed;
    }
}
