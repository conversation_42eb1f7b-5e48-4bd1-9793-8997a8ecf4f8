<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerAddress extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'type',
        'label',
        'address_line_1',
        'address_line_2',
        'city',
        'state_province',
        'postal_code',
        'country',
        'is_primary',
        'latitude',
        'longitude',
        'delivery_instructions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns the address.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer that owns the address.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Scope to filter by business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get primary addresses.
     */
    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    /**
     * Scope to filter by address type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Set this address as primary and unset others of the same type.
     */
    public function setPrimary()
    {
        // Unset other primary addresses of the same type for this customer
        static::where('business_id', $this->business_id)
              ->where('customer_id', $this->customer_id)
              ->where('type', $this->type)
              ->where('id', '!=', $this->id)
              ->update(['is_primary' => false]);

        // Set this as primary
        $this->update(['is_primary' => true]);
    }

    /**
     * Get full formatted address.
     */
    public function getFullAddressAttribute()
    {
        $parts = array_filter([
            $this->address_line_1,
            $this->address_line_2,
            $this->city,
            $this->state_province,
            $this->postal_code,
            $this->country,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get display label for the address.
     */
    public function getDisplayLabelAttribute()
    {
        return $this->label ?: ucfirst($this->type);
    }

    /**
     * Get type badge class for UI.
     */
    public function getTypeBadgeClassAttribute()
    {
        return match($this->type) {
            'home' => 'badge-primary',
            'work' => 'badge-info',
            'billing' => 'badge-warning',
            'shipping' => 'badge-success',
            default => 'badge-secondary',
        };
    }
}
