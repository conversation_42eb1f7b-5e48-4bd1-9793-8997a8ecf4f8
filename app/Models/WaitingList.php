<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WaitingList extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'service_id',
        'customer_id',
        'customer_name',
        'customer_email',
        'customer_phone',
        'preferred_date',
        'preferred_time_start',
        'preferred_time_end',
        'preferred_days_of_week',
        'participant_count',
        'notes',
        'status',
        'notified_at',
        'expires_at',
        'priority',
    ];

    protected $casts = [
        'preferred_date' => 'date',
        'preferred_time_start' => 'datetime:H:i',
        'preferred_time_end' => 'datetime:H:i',
        'preferred_days_of_week' => 'array',
        'participant_count' => 'integer',
        'notified_at' => 'datetime',
        'expires_at' => 'datetime',
        'priority' => 'integer',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc')->orderBy('created_at');
    }

    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    // Helper methods
    public function getStatusColorAttribute()
    {
        $colors = [
            'active' => 'info',
            'notified' => 'warning',
            'booked' => 'success',
            'expired' => 'secondary',
            'cancelled' => 'danger',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getPreferredDaysNamesAttribute()
    {
        if (!$this->preferred_days_of_week) {
            return [];
        }

        $days = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];

        return array_map(function ($day) use ($days) {
            return $days[$day] ?? 'Unknown';
        }, $this->preferred_days_of_week);
    }

    public function markAsNotified()
    {
        $this->update([
            'status' => 'notified',
            'notified_at' => now(),
            'expires_at' => now()->addHours(24), // Give 24 hours to respond
        ]);
    }

    public function markAsBooked()
    {
        $this->update([
            'status' => 'booked',
        ]);
    }

    public function markAsExpired()
    {
        $this->update([
            'status' => 'expired',
        ]);
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }

    public function isExpired()
    {
        return $this->expires_at && $this->expires_at < now();
    }

    public function matchesTimeSlot($date, $startTime, $endTime)
    {
        // Check if the date matches preferences
        if ($this->preferred_date && $this->preferred_date != $date) {
            return false;
        }

        // Check if day of week matches
        if ($this->preferred_days_of_week) {
            $dayOfWeek = date('w', strtotime($date));
            if (!in_array($dayOfWeek, $this->preferred_days_of_week)) {
                return false;
            }
        }

        // Check if time range matches
        if ($this->preferred_time_start && $this->preferred_time_end) {
            $preferredStart = strtotime($this->preferred_time_start);
            $preferredEnd = strtotime($this->preferred_time_end);
            $slotStart = strtotime($startTime);
            $slotEnd = strtotime($endTime);

            return $slotStart >= $preferredStart && $slotEnd <= $preferredEnd;
        }

        return true;
    }
}
