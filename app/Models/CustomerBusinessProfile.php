<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class CustomerBusinessProfile extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'status',
        'customer_since',
        'last_visit_date',
        'total_visits',
        'total_spent',
        'average_order_value',
        'preferences',
        'communication_preferences',
        'notes',
        'special_requirements',
        'loyalty_points_balance',
        'loyalty_tier',
        'lifetime_value',
        'referrals_made',
        'no_show_count',
        'cancellation_count',
        'marketing_consent',
        'marketing_consent_date',
        'custom_fields',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'customer_since' => 'date',
        'last_visit_date' => 'date',
        'total_spent' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'lifetime_value' => 'decimal:2',
        'preferences' => 'array',
        'communication_preferences' => 'array',
        'custom_fields' => 'array',
        'marketing_consent' => 'boolean',
        'marketing_consent_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns this profile.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer for this profile.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Scope to get profiles for a specific business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get active customers.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get VIP customers.
     */
    public function scopeVip($query)
    {
        return $query->where('status', 'vip');
    }

    /**
     * Scope to get customers by loyalty tier.
     */
    public function scopeByLoyaltyTier($query, $tier)
    {
        return $query->where('loyalty_tier', $tier);
    }

    /**
     * Scope to get customers with marketing consent.
     */
    public function scopeMarketingConsent($query)
    {
        return $query->where('marketing_consent', true);
    }

    /**
     * Scope to get high-value customers.
     */
    public function scopeHighValue($query, $threshold = 1000)
    {
        return $query->where('lifetime_value', '>=', $threshold);
    }

    /**
     * Scope to get recent customers.
     */
    public function scopeRecentCustomers($query, $days = 30)
    {
        return $query->where('customer_since', '>=', now()->subDays($days));
    }

    /**
     * Scope to get at-risk customers (haven't visited recently).
     */
    public function scopeAtRisk($query, $days = 90)
    {
        return $query->where('last_visit_date', '<=', now()->subDays($days))
                    ->where('status', 'active');
    }

    /**
     * Update customer statistics after a booking.
     */
    public function updateAfterBooking($bookingAmount = 0): void
    {
        $this->increment('total_visits');
        $this->increment('total_spent', $bookingAmount);

        // Update average order value
        $this->average_order_value = $this->total_spent / $this->total_visits;

        // Update last visit date
        $this->last_visit_date = now()->toDateString();

        // Update lifetime value (could include additional calculations)
        $this->lifetime_value = $this->total_spent;

        // Update loyalty tier based on spending
        $this->updateLoyaltyTier();

        $this->save();
    }

    /**
     * Update loyalty tier based on spending or visits.
     */
    public function updateLoyaltyTier(): void
    {
        $tier = match(true) {
            $this->total_spent >= 5000 => 'diamond',
            $this->total_spent >= 2500 => 'platinum',
            $this->total_spent >= 1000 => 'gold',
            $this->total_spent >= 500 => 'silver',
            default => 'bronze',
        };

        $this->loyalty_tier = $tier;
    }

    /**
     * Record a no-show.
     */
    public function recordNoShow(): void
    {
        $this->increment('no_show_count');

        // Optionally downgrade status if too many no-shows
        if ($this->no_show_count >= 3 && $this->status === 'vip') {
            $this->status = 'active';
            $this->save();
        }
    }

    /**
     * Record a cancellation.
     */
    public function recordCancellation(): void
    {
        $this->increment('cancellation_count');
    }

    /**
     * Record a referral.
     */
    public function recordReferral(): void
    {
        $this->increment('referrals_made');

        // Award loyalty points for referral
        CustomerLoyaltyPoint::awardPoints(
            $this->business_id,
            $this->customer_id,
            100, // 100 points for referral
            'Referral bonus',
            'referral'
        );
    }

    /**
     * Get the status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'active' => 'badge-success',
            'inactive' => 'badge-secondary',
            'vip' => 'badge-warning',
            'blocked' => 'badge-danger',
            'prospect' => 'badge-info',
            default => 'badge-secondary',
        };
    }

    /**
     * Get the loyalty tier badge class.
     */
    public function getLoyaltyTierBadgeClassAttribute(): string
    {
        return match($this->loyalty_tier) {
            'bronze' => 'badge-secondary',
            'silver' => 'badge-light',
            'gold' => 'badge-warning',
            'platinum' => 'badge-info',
            'diamond' => 'badge-primary',
            default => 'badge-secondary',
        };
    }

    /**
     * Get customer tenure in days.
     */
    public function getTenureInDaysAttribute(): int
    {
        return $this->customer_since->diffInDays(now());
    }

    /**
     * Get days since last visit.
     */
    public function getDaysSinceLastVisitAttribute(): ?int
    {
        return $this->last_visit_date ? $this->last_visit_date->diffInDays(now()) : null;
    }

    /**
     * Check if customer is at risk.
     */
    public function isAtRisk($days = 90): bool
    {
        return $this->last_visit_date && $this->last_visit_date->diffInDays(now()) > $days;
    }

    /**
     * Get customer value segment.
     */
    public function getValueSegmentAttribute(): string
    {
        return match(true) {
            $this->lifetime_value >= 2500 => 'High Value',
            $this->lifetime_value >= 1000 => 'Medium Value',
            $this->lifetime_value >= 250 => 'Low Value',
            default => 'New Customer',
        };
    }

    /**
     * Get the customer's emergency contacts.
     */
    public function emergencyContacts(): HasMany
    {
        return $this->hasMany(CustomerEmergencyContact::class, 'customer_id', 'customer_id')
                    ->where('business_id', $this->business_id);
    }

    /**
     * Get the customer's addresses.
     */
    public function addresses(): HasMany
    {
        return $this->hasMany(CustomerAddress::class, 'customer_id', 'customer_id')
                    ->where('business_id', $this->business_id);
    }

    /**
     * Get the customer's referrals made.
     */
    public function referralsMade(): HasMany
    {
        return $this->hasMany(CustomerReferral::class, 'referrer_id', 'customer_id')
                    ->where('business_id', $this->business_id);
    }

    /**
     * Get the customer's activity timeline.
     */
    public function activityTimeline(): HasMany
    {
        return $this->hasMany(CustomerActivityTimeline::class, 'customer_id', 'customer_id')
                    ->where('business_id', $this->business_id)
                    ->orderBy('created_at', 'desc');
    }

    /**
     * Get the customer's feedback.
     */
    public function feedback(): HasMany
    {
        return $this->hasMany(CustomerFeedback::class, 'customer_id', 'customer_id')
                    ->where('business_id', $this->business_id);
    }
}
