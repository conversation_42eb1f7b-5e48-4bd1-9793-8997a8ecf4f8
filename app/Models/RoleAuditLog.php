<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class RoleAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'action',
        'target_type',
        'target_id',
        'target_name',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'session_id',
        'risk_level',
        'additional_data',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'additional_data' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Action types
     */
    const ACTIONS = [
        'role_created' => 'Role Created',
        'role_updated' => 'Role Updated',
        'role_deleted' => 'Role Deleted',
        'permission_assigned' => 'Permission Assigned',
        'permission_revoked' => 'Permission Revoked',
        'user_role_assigned' => 'User Role Assigned',
        'user_role_revoked' => 'User Role Revoked',
        'privilege_escalation_attempt' => 'Privilege Escalation Attempt',
        'unauthorized_access_attempt' => 'Unauthorized Access Attempt',
        'sensitive_permission_access' => 'Sensitive Permission Access',
        'role_hierarchy_violation' => 'Role Hierarchy Violation',
    ];

    /**
     * Risk levels
     */
    const RISK_LEVELS = [
        'low' => 1,
        'medium' => 2,
        'high' => 3,
        'critical' => 4,
        'maximum' => 5,
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            if (!$log->user_id && Auth::check()) {
                $log->user_id = Auth::id();
            }
            
            if (!$log->ip_address) {
                $log->ip_address = request()->ip();
            }
            
            if (!$log->user_agent) {
                $log->user_agent = request()->userAgent();
            }
            
            if (!$log->session_id) {
                $log->session_id = session()->getId();
            }
        });
    }

    /**
     * Get the user who performed the action
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the target model (polymorphic)
     */
    public function target()
    {
        return $this->morphTo();
    }

    /**
     * Get action name
     */
    public function getActionNameAttribute()
    {
        return self::ACTIONS[$this->action] ?? $this->action;
    }

    /**
     * Get risk level name
     */
    public function getRiskLevelNameAttribute()
    {
        $levels = array_flip(self::RISK_LEVELS);
        return $levels[$this->risk_level] ?? 'unknown';
    }

    /**
     * Log role creation
     */
    public static function logRoleCreated($role, array $additionalData = [])
    {
        return self::create([
            'action' => 'role_created',
            'target_type' => get_class($role),
            'target_id' => $role->id,
            'target_name' => $role->name,
            'new_values' => $role->toArray(),
            'risk_level' => self::calculateRiskLevel('role_created', $role),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log role update
     */
    public static function logRoleUpdated($role, array $oldValues, array $additionalData = [])
    {
        return self::create([
            'action' => 'role_updated',
            'target_type' => get_class($role),
            'target_id' => $role->id,
            'target_name' => $role->name,
            'old_values' => $oldValues,
            'new_values' => $role->toArray(),
            'risk_level' => self::calculateRiskLevel('role_updated', $role, $oldValues),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log role deletion
     */
    public static function logRoleDeleted($role, array $additionalData = [])
    {
        return self::create([
            'action' => 'role_deleted',
            'target_type' => get_class($role),
            'target_id' => $role->id,
            'target_name' => $role->name,
            'old_values' => $role->toArray(),
            'risk_level' => self::calculateRiskLevel('role_deleted', $role),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log permission assignment
     */
    public static function logPermissionAssigned($role, $permissions, array $additionalData = [])
    {
        return self::create([
            'action' => 'permission_assigned',
            'target_type' => get_class($role),
            'target_id' => $role->id,
            'target_name' => $role->name,
            'new_values' => ['permissions' => $permissions],
            'risk_level' => self::calculatePermissionRiskLevel($permissions),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log permission revocation
     */
    public static function logPermissionRevoked($role, $permissions, array $additionalData = [])
    {
        return self::create([
            'action' => 'permission_revoked',
            'target_type' => get_class($role),
            'target_id' => $role->id,
            'target_name' => $role->name,
            'old_values' => ['permissions' => $permissions],
            'risk_level' => self::calculatePermissionRiskLevel($permissions),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log user role assignment
     */
    public static function logUserRoleAssigned($user, $role, array $additionalData = [])
    {
        return self::create([
            'action' => 'user_role_assigned',
            'target_type' => get_class($user),
            'target_id' => $user->id,
            'target_name' => $user->name,
            'new_values' => ['role' => $role->name, 'role_id' => $role->id],
            'risk_level' => self::calculateRiskLevel('user_role_assigned', $role),
            'additional_data' => $additionalData,
        ]);
    }

    /**
     * Log security violation
     */
    public static function logSecurityViolation($action, array $data = [])
    {
        return self::create([
            'action' => $action,
            'target_type' => 'security_violation',
            'risk_level' => self::RISK_LEVELS['critical'],
            'additional_data' => $data,
        ]);
    }

    /**
     * Calculate risk level for actions
     */
    private static function calculateRiskLevel($action, $role = null, $oldValues = null)
    {
        // High risk actions
        if (in_array($action, ['role_deleted', 'privilege_escalation_attempt'])) {
            return self::RISK_LEVELS['high'];
        }

        // Critical risk for Super Admin role modifications
        if ($role && $role->name === 'Super Admin') {
            return self::RISK_LEVELS['critical'];
        }

        // Medium risk for system roles
        if ($role && method_exists($role, 'isSystemRole') && $role->isSystemRole()) {
            return self::RISK_LEVELS['medium'];
        }

        return self::RISK_LEVELS['low'];
    }

    /**
     * Calculate risk level for permission changes
     */
    private static function calculatePermissionRiskLevel($permissions)
    {
        $sensitivePermissions = [
            'manage system settings',
            'manage server configuration',
            'manage backups',
            'manage audit logs',
            'manage security settings',
            'manage database',
            'manage file system',
        ];

        $permissionNames = is_array($permissions) ? $permissions : [$permissions];
        
        foreach ($permissionNames as $permission) {
            if (in_array($permission, $sensitivePermissions)) {
                return self::RISK_LEVELS['critical'];
            }
        }

        return self::RISK_LEVELS['medium'];
    }

    /**
     * Scope for high risk activities
     */
    public function scopeHighRisk($query)
    {
        return $query->where('risk_level', '>=', self::RISK_LEVELS['high']);
    }

    /**
     * Scope for recent activities
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for specific action
     */
    public function scopeForAction($query, $action)
    {
        return $query->where('action', $action);
    }
}
