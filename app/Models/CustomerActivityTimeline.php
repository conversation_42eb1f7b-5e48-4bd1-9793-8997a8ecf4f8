<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CustomerActivityTimeline extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer_activity_timeline';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'business_id',
        'customer_id',
        'created_by',
        'activity_type',
        'title',
        'description',
        'metadata',
        'icon',
        'color',
        'is_important',
        'is_system_generated',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'is_important' => 'boolean',
        'is_system_generated' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the business that owns the activity.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer that owns the activity.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the user who created the activity.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to filter by business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter by customer.
     */
    public function scopeForCustomer($query, $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to get important activities.
     */
    public function scopeImportant($query)
    {
        return $query->where('is_important', true);
    }

    /**
     * Scope to get system-generated activities.
     */
    public function scopeSystemGenerated($query)
    {
        return $query->where('is_system_generated', true);
    }

    /**
     * Scope to get manual activities.
     */
    public function scopeManual($query)
    {
        return $query->where('is_system_generated', false);
    }

    /**
     * Create a new activity entry.
     */
    public static function createActivity($businessId, $customerId, $activityType, $title, $options = [])
    {
        $defaultIcons = [
            'booking_created' => 'fas fa-calendar-plus',
            'booking_modified' => 'fas fa-calendar-edit',
            'booking_cancelled' => 'fas fa-calendar-times',
            'booking_completed' => 'fas fa-calendar-check',
            'communication_sent' => 'fas fa-paper-plane',
            'communication_received' => 'fas fa-envelope',
            'points_awarded' => 'fas fa-gift',
            'points_redeemed' => 'fas fa-star',
            'tag_assigned' => 'fas fa-tag',
            'tag_removed' => 'fas fa-tag',
            'profile_updated' => 'fas fa-user-edit',
            'note_added' => 'fas fa-sticky-note',
            'status_changed' => 'fas fa-exchange-alt',
            'referral_made' => 'fas fa-user-friends',
            'feedback_received' => 'fas fa-comment',
            'campaign_sent' => 'fas fa-bullhorn',
            'login' => 'fas fa-sign-in-alt',
            'registration' => 'fas fa-user-plus',
        ];

        $defaultColors = [
            'booking_created' => '#28a745',
            'booking_modified' => '#ffc107',
            'booking_cancelled' => '#dc3545',
            'booking_completed' => '#28a745',
            'communication_sent' => '#007bff',
            'communication_received' => '#17a2b8',
            'points_awarded' => '#28a745',
            'points_redeemed' => '#ffc107',
            'tag_assigned' => '#6f42c1',
            'tag_removed' => '#6c757d',
            'profile_updated' => '#17a2b8',
            'note_added' => '#ffc107',
            'status_changed' => '#fd7e14',
            'referral_made' => '#20c997',
            'feedback_received' => '#e83e8c',
            'campaign_sent' => '#6610f2',
            'login' => '#6c757d',
            'registration' => '#28a745',
        ];

        return static::create([
            'business_id' => $businessId,
            'customer_id' => $customerId,
            'created_by' => $options['created_by'] ?? auth()->id(),
            'activity_type' => $activityType,
            'title' => $title,
            'description' => $options['description'] ?? null,
            'metadata' => $options['metadata'] ?? null,
            'icon' => $options['icon'] ?? $defaultIcons[$activityType] ?? 'fas fa-circle',
            'color' => $options['color'] ?? $defaultColors[$activityType] ?? '#6c757d',
            'is_important' => $options['is_important'] ?? false,
            'is_system_generated' => $options['is_system_generated'] ?? true,
        ]);
    }

    /**
     * Get formatted time for display.
     */
    public function getFormattedTimeAttribute()
    {
        return $this->created_at->format('M j, Y g:i A');
    }

    /**
     * Get relative time for display.
     */
    public function getRelativeTimeAttribute()
    {
        return $this->created_at->diffForHumans();
    }
}
