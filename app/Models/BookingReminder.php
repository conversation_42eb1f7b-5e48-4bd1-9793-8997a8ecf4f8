<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BookingReminder extends Model
{
    use HasFactory;

    protected $fillable = [
        'booking_id',
        'reminder_type',
        'hours_before',
        'scheduled_at',
        'sent_at',
        'status',
        'message',
        'delivery_data',
    ];

    protected $casts = [
        'hours_before' => 'integer',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
        'delivery_data' => 'array',
    ];

    // Relationships
    public function booking(): BelongsTo
    {
        return $this->belongsTo(Booking::class);
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('reminder_type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeDue($query)
    {
        return $query->where('scheduled_at', '<=', now())
                    ->where('status', 'pending');
    }

    // Helper methods
    public function getStatusColorAttribute()
    {
        $colors = [
            'pending' => 'warning',
            'sent' => 'success',
            'failed' => 'danger',
            'cancelled' => 'secondary',
        ];

        return $colors[$this->status] ?? 'secondary';
    }

    public function getTypeIconAttribute()
    {
        $icons = [
            'email' => 'fas fa-envelope',
            'sms' => 'fas fa-sms',
            'push' => 'fas fa-bell',
            'call' => 'fas fa-phone',
        ];

        return $icons[$this->reminder_type] ?? 'fas fa-bell';
    }

    public function markAsSent($deliveryData = null)
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'delivery_data' => $deliveryData,
        ]);
    }

    public function markAsFailed($deliveryData = null)
    {
        $this->update([
            'status' => 'failed',
            'delivery_data' => $deliveryData,
        ]);
    }

    public function cancel()
    {
        $this->update([
            'status' => 'cancelled',
        ]);
    }
}
