<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class TwoFactorSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'setting_key',
        'setting_value',
        'description',
        'category',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get a setting value by key
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("2fa_setting_{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('setting_key', $key)
                ->where('is_active', true)
                ->first();

            if (!$setting) {
                return $default;
            }

            // Convert string values to appropriate types
            $value = $setting->setting_value;
            
            if ($value === 'true') return true;
            if ($value === 'false') return false;
            if (is_numeric($value)) return (int) $value;
            
            return $value;
        });
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, string $description = null): bool
    {
        $stringValue = is_bool($value) ? ($value ? 'true' : 'false') : (string) $value;

        $setting = static::updateOrCreate(
            ['setting_key' => $key],
            [
                'setting_value' => $stringValue,
                'description' => $description,
                'category' => 'security',
                'is_active' => true,
            ]
        );

        // Clear cache
        Cache::forget("2fa_setting_{$key}");

        return $setting->wasRecentlyCreated || $setting->wasChanged();
    }

    /**
     * Check if 2FA is globally enabled
     */
    public static function isEnabled(): bool
    {
        return static::get('2fa_enabled', false);
    }

    /**
     * Check if 2FA is required for role operations
     */
    public static function isRequiredForRoles(): bool
    {
        return static::get('2fa_required_for_roles', false);
    }

    /**
     * Check if 2FA is always required for Super Admin
     */
    public static function isRequiredForSuperAdmin(): bool
    {
        return static::get('2fa_required_for_super_admin', true);
    }

    /**
     * Get 2FA session duration in minutes
     */
    public static function getSessionDuration(): int
    {
        return static::get('2fa_session_duration', 30);
    }

    /**
     * Get 2FA code expiry time in minutes
     */
    public static function getCodeExpiry(): int
    {
        return static::get('2fa_code_expiry', 10);
    }

    /**
     * Enable 2FA globally
     */
    public static function enable(): bool
    {
        return static::set('2fa_enabled', true, 'Two-factor authentication enabled globally');
    }

    /**
     * Disable 2FA globally
     */
    public static function disable(): bool
    {
        return static::set('2fa_enabled', false, 'Two-factor authentication disabled globally');
    }

    /**
     * Enable 2FA for role operations
     */
    public static function enableForRoles(): bool
    {
        return static::set('2fa_required_for_roles', true, 'Two-factor authentication required for role operations');
    }

    /**
     * Disable 2FA for role operations
     */
    public static function disableForRoles(): bool
    {
        return static::set('2fa_required_for_roles', false, 'Two-factor authentication not required for role operations');
    }

    /**
     * Get all 2FA settings
     */
    public static function getAllSettings(): array
    {
        return [
            'enabled' => static::isEnabled(),
            'required_for_roles' => static::isRequiredForRoles(),
            'required_for_super_admin' => static::isRequiredForSuperAdmin(),
            'session_duration' => static::getSessionDuration(),
            'code_expiry' => static::getCodeExpiry(),
        ];
    }

    /**
     * Clear all 2FA setting caches
     */
    public static function clearCache(): void
    {
        $keys = [
            '2fa_enabled',
            '2fa_required_for_roles',
            '2fa_required_for_super_admin',
            '2fa_session_duration',
            '2fa_code_expiry',
        ];

        foreach ($keys as $key) {
            Cache::forget("2fa_setting_{$key}");
        }
    }
}
