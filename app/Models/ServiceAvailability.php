<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class ServiceAvailability extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'service_availability';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'service_id',
        'business_id',
        'date',
        'start_time',
        'end_time',
        'max_bookings',
        'current_bookings',
        'is_available',
        'unavailable_reason',
        'staff_ids',
        'resource_ids',
        'price_override',
        'notes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'staff_ids' => 'array',
        'resource_ids' => 'array',
        'is_available' => 'boolean',
        'price_override' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the service that owns the availability slot.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the business that owns the availability slot.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Scope to get available slots.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true)
                    ->where('current_bookings', '<', 'max_bookings');
    }

    /**
     * Scope to get slots for a specific date.
     */
    public function scopeForDate($query, $date)
    {
        return $query->where('date', $date);
    }

    /**
     * Scope to get slots for a specific service.
     */
    public function scopeForService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }

    /**
     * Scope to get slots for a specific business.
     */
    public function scopeForBusiness($query, $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get slots within a time range.
     */
    public function scopeWithinTimeRange($query, $startTime, $endTime)
    {
        return $query->where('start_time', '>=', $startTime)
                    ->where('end_time', '<=', $endTime);
    }

    /**
     * Scope to get future slots only.
     */
    public function scopeFuture($query)
    {
        $now = now();
        return $query->where(function($q) use ($now) {
            $q->where('date', '>', $now->toDateString())
              ->orWhere(function($q2) use ($now) {
                  $q2->where('date', '=', $now->toDateString())
                     ->where('start_time', '>', $now->toTimeString());
              });
        });
    }

    /**
     * Check if this slot is bookable.
     */
    public function isBookable(): bool
    {
        return $this->is_available &&
               $this->current_bookings < $this->max_bookings &&
               $this->isFuture();
    }

    /**
     * Check if this slot is in the future.
     */
    public function isFuture(): bool
    {
        $slotDateTime = Carbon::parse($this->date->toDateString() . ' ' . $this->start_time);
        return $slotDateTime->isFuture();
    }

    /**
     * Get the remaining capacity for this slot.
     */
    public function getRemainingCapacityAttribute(): int
    {
        return max(0, $this->max_bookings - $this->current_bookings);
    }

    /**
     * Get the formatted time range.
     */
    public function getTimeRangeAttribute(): string
    {
        return Carbon::parse($this->start_time)->format('H:i') . ' - ' .
               Carbon::parse($this->end_time)->format('H:i');
    }

    /**
     * Get the formatted date and time.
     */
    public function getFormattedDateTimeAttribute(): string
    {
        return $this->date->format('M j, Y') . ' at ' . $this->time_range;
    }

    /**
     * Get the effective price for this slot.
     */
    public function getEffectivePriceAttribute(): float
    {
        return $this->price_override ?? $this->service->base_price;
    }

    /**
     * Book this slot (increment current bookings).
     */
    public function book(): bool
    {
        if (!$this->isBookable()) {
            return false;
        }

        $this->increment('current_bookings');

        // Mark as unavailable if fully booked
        if ($this->current_bookings >= $this->max_bookings) {
            $this->update(['is_available' => false, 'unavailable_reason' => 'Fully booked']);
        }

        return true;
    }

    /**
     * Cancel a booking for this slot (decrement current bookings).
     */
    public function cancelBooking(): bool
    {
        if ($this->current_bookings <= 0) {
            return false;
        }

        $this->decrement('current_bookings');

        // Mark as available if there's capacity and no other reason for unavailability
        if ($this->current_bookings < $this->max_bookings && $this->unavailable_reason === 'Fully booked') {
            $this->update(['is_available' => true, 'unavailable_reason' => null]);
        }

        return true;
    }

    /**
     * Generate availability slots for a service.
     */
    public static function generateSlotsForService($serviceId, $businessId, $date, $startTime = '09:00', $endTime = '17:00', $slotDuration = 60)
    {
        $slots = [];
        $current = Carbon::parse($date . ' ' . $startTime);
        $end = Carbon::parse($date . ' ' . $endTime);

        while ($current->addMinutes($slotDuration)->lte($end)) {
            $slotStart = $current->copy()->subMinutes($slotDuration);
            $slotEnd = $current->copy();

            $slots[] = [
                'service_id' => $serviceId,
                'business_id' => $businessId,
                'date' => $date,
                'start_time' => $slotStart->format('H:i:s'),
                'end_time' => $slotEnd->format('H:i:s'),
                'max_bookings' => 1,
                'current_bookings' => 0,
                'is_available' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        return static::insert($slots);
    }
}
