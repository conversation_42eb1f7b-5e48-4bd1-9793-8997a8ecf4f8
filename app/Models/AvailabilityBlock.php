<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AvailabilityBlock extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_id',
        'business_branch_id',
        'resource_id',
        'title',
        'description',
        'start_datetime',
        'end_datetime',
        'block_type',
        'affects_all_resources',
        'affected_services',
        'is_recurring',
        'recurrence_data',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'affects_all_resources' => 'boolean',
        'affected_services' => 'array',
        'is_recurring' => 'boolean',
        'recurrence_data' => 'array',
    ];

    // Relationships
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    public function branch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    public function resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    // Scopes
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('start_datetime', [$startDate, $endDate])
              ->orWhereBetween('end_datetime', [$startDate, $endDate])
              ->orWhere(function ($q2) use ($startDate, $endDate) {
                  $q2->where('start_datetime', '<=', $startDate)
                     ->where('end_datetime', '>=', $endDate);
              });
        });
    }

    public function scopeByType($query, $type)
    {
        return $query->where('block_type', $type);
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    // Helper methods
    public function getTypeColorAttribute()
    {
        $colors = [
            'maintenance' => 'warning',
            'holiday' => 'info',
            'private_event' => 'primary',
            'staff_break' => 'secondary',
            'other' => 'dark',
        ];

        return $colors[$this->block_type] ?? 'secondary';
    }

    public function getDurationInHoursAttribute()
    {
        return $this->start_datetime->diffInHours($this->end_datetime);
    }

    public function getFormattedDurationAttribute()
    {
        $totalMinutes = $this->start_datetime->diffInMinutes($this->end_datetime);
        $hours = floor($totalMinutes / 60);
        $minutes = $totalMinutes % 60;
        
        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function affectsService($serviceId)
    {
        if ($this->affects_all_resources) {
            return true;
        }

        return in_array($serviceId, $this->affected_services ?? []);
    }
}
