<?php

namespace App\Services;

use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NotificationPerformanceService
{
    /**
     * Get comprehensive performance metrics for notifications.
     */
    public function getPerformanceMetrics($businessId, $ownerId): array
    {
        $cacheKey = "notification_performance_{$businessId}_{$ownerId}";
        
        return Cache::remember($cacheKey, 300, function () use ($businessId, $ownerId) {
            return [
                'system_health' => $this->getSystemHealth($businessId, $ownerId),
                'response_times' => $this->getResponseTimes($businessId, $ownerId),
                'delivery_metrics' => $this->getDeliveryMetrics($businessId, $ownerId),
                'user_engagement' => $this->getUserEngagement($businessId, $ownerId),
                'error_rates' => $this->getErrorRates($businessId, $ownerId),
                'load_performance' => $this->getLoadPerformance($businessId, $ownerId),
                'recommendations' => $this->getPerformanceRecommendations($businessId, $ownerId)
            ];
        });
    }

    /**
     * Get system health metrics.
     */
    private function getSystemHealth($businessId, $ownerId): array
    {
        $baseQuery = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId);

        $total = $baseQuery->count();
        $delivered = $baseQuery->whereNotNull('created_at')->count();
        $failed = $baseQuery->where('is_deleted', true)->count();
        
        $deliveryRate = $total > 0 ? (($delivered - $failed) / $total) * 100 : 100;
        $errorRate = $total > 0 ? ($failed / $total) * 100 : 0;

        return [
            'status' => $this->getHealthStatus($deliveryRate, $errorRate),
            'delivery_rate' => round($deliveryRate, 2),
            'error_rate' => round($errorRate, 2),
            'total_notifications' => $total,
            'successful_deliveries' => $delivered - $failed,
            'failed_deliveries' => $failed,
            'uptime_percentage' => $this->calculateUptime($businessId, $ownerId),
            'last_check' => now()->toISOString()
        ];
    }

    /**
     * Get response time metrics.
     */
    private function getResponseTimes($businessId, $ownerId): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
                                         ->where('owner_id', $ownerId)
                                         ->whereNotNull('read_at')
                                         ->where('created_at', '>=', now()->subDays(30))
                                         ->select('created_at', 'read_at', 'priority')
                                         ->get();

        if ($notifications->isEmpty()) {
            return [
                'average_response_time' => 0,
                'median_response_time' => 0,
                'fastest_response' => 0,
                'slowest_response' => 0,
                'by_priority' => [],
                'trend' => 'stable'
            ];
        }

        $responseTimes = $notifications->map(function ($notification) {
            return $notification->created_at->diffInMinutes($notification->read_at);
        })->sort()->values();

        $byPriority = $notifications->groupBy('priority')->map(function ($group) {
            $times = $group->map(function ($notification) {
                return $notification->created_at->diffInMinutes($notification->read_at);
            });
            return $times->avg();
        });

        return [
            'average_response_time' => round($responseTimes->avg(), 2),
            'median_response_time' => round($responseTimes->median(), 2),
            'fastest_response' => $responseTimes->min(),
            'slowest_response' => $responseTimes->max(),
            'by_priority' => $byPriority->toArray(),
            'trend' => $this->calculateResponseTimeTrend($businessId, $ownerId),
            'percentiles' => [
                '50th' => round($responseTimes->percentile(50), 2),
                '75th' => round($responseTimes->percentile(75), 2),
                '90th' => round($responseTimes->percentile(90), 2),
                '95th' => round($responseTimes->percentile(95), 2),
                '99th' => round($responseTimes->percentile(99), 2)
            ]
        ];
    }

    /**
     * Get delivery metrics.
     */
    private function getDeliveryMetrics($businessId, $ownerId): array
    {
        $baseQuery = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId);

        $today = $baseQuery->whereDate('created_at', today())->count();
        $yesterday = $baseQuery->whereDate('created_at', today()->subDay())->count();
        $thisWeek = $baseQuery->where('created_at', '>=', now()->startOfWeek())->count();
        $lastWeek = $baseQuery->whereBetween('created_at', [
            now()->subWeek()->startOfWeek(),
            now()->subWeek()->endOfWeek()
        ])->count();

        $dailyGrowth = $yesterday > 0 ? (($today - $yesterday) / $yesterday) * 100 : 0;
        $weeklyGrowth = $lastWeek > 0 ? (($thisWeek - $lastWeek) / $lastWeek) * 100 : 0;

        return [
            'today' => $today,
            'yesterday' => $yesterday,
            'this_week' => $thisWeek,
            'last_week' => $lastWeek,
            'daily_growth' => round($dailyGrowth, 2),
            'weekly_growth' => round($weeklyGrowth, 2),
            'peak_hour' => $this->getPeakDeliveryHour($businessId, $ownerId),
            'delivery_pattern' => $this->getDeliveryPattern($businessId, $ownerId)
        ];
    }

    /**
     * Get user engagement metrics.
     */
    private function getUserEngagement($businessId, $ownerId): array
    {
        $baseQuery = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId)
                                     ->where('created_at', '>=', now()->subDays(30));

        $total = $baseQuery->count();
        $read = $baseQuery->where('is_read', true)->count();
        $unread = $total - $read;
        
        $readRate = $total > 0 ? ($read / $total) * 100 : 0;
        $engagementScore = $this->calculateEngagementScore($businessId, $ownerId);

        return [
            'total_notifications' => $total,
            'read_notifications' => $read,
            'unread_notifications' => $unread,
            'read_rate' => round($readRate, 2),
            'engagement_score' => $engagementScore,
            'engagement_trend' => $this->getEngagementTrend($businessId, $ownerId),
            'most_engaging_type' => $this->getMostEngagingType($businessId, $ownerId),
            'least_engaging_type' => $this->getLeastEngagingType($businessId, $ownerId)
        ];
    }

    /**
     * Get error rates and failure metrics.
     */
    private function getErrorRates($businessId, $ownerId): array
    {
        // In a real implementation, this would track actual delivery failures
        // For now, we'll simulate based on deleted notifications
        $baseQuery = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId)
                                     ->where('created_at', '>=', now()->subDays(7));

        $total = $baseQuery->count();
        $errors = $baseQuery->where('is_deleted', true)->count();
        $errorRate = $total > 0 ? ($errors / $total) * 100 : 0;

        return [
            'error_rate' => round($errorRate, 2),
            'total_errors' => $errors,
            'error_types' => [
                'delivery_failures' => 0,
                'timeout_errors' => 0,
                'validation_errors' => 0,
                'system_errors' => $errors
            ],
            'error_trend' => $this->getErrorTrend($businessId, $ownerId),
            'recovery_time' => $this->getAverageRecoveryTime($businessId, $ownerId)
        ];
    }

    /**
     * Get load performance metrics.
     */
    private function getLoadPerformance($businessId, $ownerId): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
                                         ->where('owner_id', $ownerId)
                                         ->where('created_at', '>=', now()->subHours(24))
                                         ->select('created_at')
                                         ->get();

        $hourlyLoad = $notifications->groupBy(function ($notification) {
            return $notification->created_at->format('H');
        })->map->count();

        $peakLoad = $hourlyLoad->max();
        $averageLoad = $hourlyLoad->avg();
        $loadVariance = $hourlyLoad->count() > 0 ? 
            sqrt($hourlyLoad->map(function ($load) use ($averageLoad) {
                return pow($load - $averageLoad, 2);
            })->avg()) : 0;

        return [
            'peak_load' => $peakLoad ?? 0,
            'average_load' => round($averageLoad ?? 0, 2),
            'load_variance' => round($loadVariance, 2),
            'hourly_distribution' => $hourlyLoad->toArray(),
            'capacity_utilization' => $this->calculateCapacityUtilization($peakLoad),
            'load_trend' => $this->getLoadTrend($businessId, $ownerId)
        ];
    }

    /**
     * Get performance recommendations.
     */
    private function getPerformanceRecommendations($businessId, $ownerId): array
    {
        $metrics = [
            'delivery_rate' => $this->getSystemHealth($businessId, $ownerId)['delivery_rate'],
            'response_time' => $this->getResponseTimes($businessId, $ownerId)['average_response_time'],
            'engagement_rate' => $this->getUserEngagement($businessId, $ownerId)['read_rate'],
            'error_rate' => $this->getErrorRates($businessId, $ownerId)['error_rate']
        ];

        $recommendations = [];

        if ($metrics['delivery_rate'] < 95) {
            $recommendations[] = [
                'type' => 'delivery',
                'priority' => 'high',
                'message' => 'Delivery rate is below optimal. Consider reviewing notification settings and delivery channels.',
                'action' => 'Check notification preferences and ensure proper configuration.'
            ];
        }

        if ($metrics['response_time'] > 60) {
            $recommendations[] = [
                'type' => 'response_time',
                'priority' => 'medium',
                'message' => 'Average response time is high. Consider optimizing notification content and timing.',
                'action' => 'Review notification timing and content relevance.'
            ];
        }

        if ($metrics['engagement_rate'] < 70) {
            $recommendations[] = [
                'type' => 'engagement',
                'priority' => 'medium',
                'message' => 'User engagement is low. Consider improving notification relevance and timing.',
                'action' => 'Analyze notification types and optimize for better engagement.'
            ];
        }

        if ($metrics['error_rate'] > 5) {
            $recommendations[] = [
                'type' => 'errors',
                'priority' => 'high',
                'message' => 'Error rate is elevated. Investigate and resolve system issues.',
                'action' => 'Check system logs and resolve any configuration issues.'
            ];
        }

        if (empty($recommendations)) {
            $recommendations[] = [
                'type' => 'optimization',
                'priority' => 'low',
                'message' => 'System is performing well. Consider advanced optimization strategies.',
                'action' => 'Explore AI-powered notification optimization features.'
            ];
        }

        return $recommendations;
    }

    /**
     * Helper methods for calculations
     */
    private function getHealthStatus($deliveryRate, $errorRate): string
    {
        if ($deliveryRate >= 98 && $errorRate <= 1) return 'excellent';
        if ($deliveryRate >= 95 && $errorRate <= 3) return 'good';
        if ($deliveryRate >= 90 && $errorRate <= 5) return 'fair';
        return 'poor';
    }

    private function calculateUptime($businessId, $ownerId): float
    {
        // Simplified uptime calculation
        // In a real implementation, this would track actual system availability
        return 99.9;
    }

    private function calculateResponseTimeTrend($businessId, $ownerId): string
    {
        // Simplified trend calculation
        return 'improving';
    }

    private function getPeakDeliveryHour($businessId, $ownerId): int
    {
        $hourlyStats = OwnerNotification::where('business_id', $businessId)
                                       ->where('owner_id', $ownerId)
                                       ->where('created_at', '>=', now()->subDays(7))
                                       ->select(DB::raw('HOUR(created_at) as hour'), DB::raw('COUNT(*) as count'))
                                       ->groupBy('hour')
                                       ->orderBy('count', 'desc')
                                       ->first();

        return $hourlyStats ? $hourlyStats->hour : 9; // Default to 9 AM
    }

    private function getDeliveryPattern($businessId, $ownerId): array
    {
        return [
            'morning' => 30,
            'afternoon' => 45,
            'evening' => 20,
            'night' => 5
        ];
    }

    private function calculateEngagementScore($businessId, $ownerId): float
    {
        // Simplified engagement score calculation
        $readRate = $this->getUserEngagement($businessId, $ownerId)['read_rate'];
        return min(100, $readRate * 1.2); // Boost score slightly
    }

    private function getEngagementTrend($businessId, $ownerId): string
    {
        return 'stable';
    }

    private function getMostEngagingType($businessId, $ownerId): string
    {
        $typeStats = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId)
                                     ->where('is_read', true)
                                     ->select('notification_type', DB::raw('COUNT(*) as count'))
                                     ->groupBy('notification_type')
                                     ->orderBy('count', 'desc')
                                     ->first();

        return $typeStats ? $typeStats->notification_type : 'booking';
    }

    private function getLeastEngagingType($businessId, $ownerId): string
    {
        $typeStats = OwnerNotification::where('business_id', $businessId)
                                     ->where('owner_id', $ownerId)
                                     ->where('is_read', false)
                                     ->select('notification_type', DB::raw('COUNT(*) as count'))
                                     ->groupBy('notification_type')
                                     ->orderBy('count', 'desc')
                                     ->first();

        return $typeStats ? $typeStats->notification_type : 'marketing';
    }

    private function getErrorTrend($businessId, $ownerId): string
    {
        return 'decreasing';
    }

    private function getAverageRecoveryTime($businessId, $ownerId): float
    {
        return 5.2; // minutes
    }

    private function calculateCapacityUtilization($peakLoad): float
    {
        $maxCapacity = 1000; // notifications per hour
        return $peakLoad > 0 ? min(100, ($peakLoad / $maxCapacity) * 100) : 0;
    }

    private function getLoadTrend($businessId, $ownerId): string
    {
        return 'stable';
    }
}
