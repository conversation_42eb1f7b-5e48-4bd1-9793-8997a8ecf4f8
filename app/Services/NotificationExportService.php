<?php

namespace App\Services;

use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class NotificationExportService
{
    /**
     * Export notifications to various formats.
     */
    public function export($businessId, $ownerId, $format = 'csv', $filters = []): array
    {
        try {
            $notifications = $this->getFilteredNotifications($businessId, $ownerId, $filters);

            switch (strtolower($format)) {
                case 'csv':
                    return $this->exportToCsv($notifications, $businessId, $ownerId);
                case 'excel':
                    return $this->exportToExcel($notifications, $businessId, $ownerId);
                case 'json':
                    return $this->exportToJson($notifications, $businessId, $ownerId);
                case 'pdf':
                    return $this->exportToPdf($notifications, $businessId, $ownerId);
                default:
                    throw new \InvalidArgumentException('Unsupported export format');
            }
        } catch (\Exception $e) {
            \Log::error('Notification export failed', [
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'format' => $format,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get filtered notifications for export.
     */
    private function getFilteredNotifications($businessId, $ownerId, $filters): \Illuminate\Database\Eloquent\Collection
    {
        $query = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId);

        // Apply filters
        if (!empty($filters['type'])) {
            $query->where('notification_type', $filters['type']);
        }

        if (!empty($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        if (!empty($filters['status'])) {
            if ($filters['status'] === 'read') {
                $query->where('is_read', true);
            } elseif ($filters['status'] === 'unread') {
                $query->where('is_read', false);
            } elseif ($filters['status'] === 'deleted') {
                $query->where('is_deleted', true);
            }
        }

        if (!empty($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('title', 'like', "%{$filters['search']}%")
                  ->orWhere('message', 'like', "%{$filters['search']}%");
            });
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Export to CSV format.
     */
    private function exportToCsv($notifications, $businessId, $ownerId): array
    {
        $filename = "notifications_export_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.csv';
        $filepath = "exports/notifications/{$filename}";

        $csvData = [];

        // Add header row
        $csvData[] = [
            'ID',
            'Type',
            'Priority',
            'Title',
            'Message',
            'Status',
            'Source Type',
            'Source ID',
            'Created At',
            'Read At',
            'Data'
        ];

        // Add notification rows
        foreach ($notifications as $notification) {
            $csvData[] = [
                $notification->id,
                $notification->notification_type,
                $notification->priority,
                $notification->title,
                $notification->message,
                $notification->is_read ? 'Read' : 'Unread',
                $notification->source_type ?? '',
                $notification->source_id ?? '',
                $notification->created_at->format('Y-m-d H:i:s'),
                $notification->read_at ? $notification->read_at->format('Y-m-d H:i:s') : '',
                $notification->data ? json_encode($notification->data) : ''
            ];
        }

        // Write CSV file
        $handle = fopen('php://temp', 'r+');
        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }
        rewind($handle);
        $csvContent = stream_get_contents($handle);
        fclose($handle);

        Storage::put($filepath, $csvContent);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath),
            'record_count' => count($notifications)
        ];
    }

    /**
     * Export to JSON format.
     */
    private function exportToJson($notifications, $businessId, $ownerId): array
    {
        $filename = "notifications_export_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.json';
        $filepath = "exports/notifications/{$filename}";

        $exportData = [
            'export_info' => [
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'exported_at' => now()->toISOString(),
                'total_records' => count($notifications),
                'format' => 'json'
            ],
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->notification_type,
                    'priority' => $notification->priority,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'is_read' => $notification->is_read,
                    'is_deleted' => $notification->is_deleted,
                    'source_type' => $notification->source_type,
                    'source_id' => $notification->source_id,
                    'data' => $notification->data,
                    'created_at' => $notification->created_at->toISOString(),
                    'read_at' => $notification->read_at?->toISOString(),
                    'expires_at' => $notification->expires_at?->toISOString()
                ];
            })
        ];

        $jsonContent = json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        Storage::put($filepath, $jsonContent);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath),
            'record_count' => count($notifications)
        ];
    }

    /**
     * Export to Excel format (simplified CSV with .xlsx extension).
     */
    private function exportToExcel($notifications, $businessId, $ownerId): array
    {
        // For now, we'll create a CSV file with .xlsx extension
        // In a real implementation, you'd use a library like PhpSpreadsheet
        $result = $this->exportToCsv($notifications, $businessId, $ownerId);

        if ($result['success']) {
            // Rename file to .xlsx
            $oldPath = $result['filepath'];
            $newFilename = str_replace('.csv', '.xlsx', $result['filename']);
            $newPath = str_replace('.csv', '.xlsx', $oldPath);

            Storage::move($oldPath, $newPath);

            $result['filename'] = $newFilename;
            $result['filepath'] = $newPath;
            $result['download_url'] = Storage::url($newPath);
        }

        return $result;
    }

    /**
     * Export to PDF format.
     */
    private function exportToPdf($notifications, $businessId, $ownerId): array
    {
        $filename = "notifications_export_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.pdf';
        $filepath = "exports/notifications/{$filename}";

        // Create HTML content for PDF
        $html = $this->generatePdfHtml($notifications, $businessId, $ownerId);

        // For now, we'll save as HTML file
        // In a real implementation, you'd use a library like DomPDF or wkhtmltopdf
        Storage::put($filepath, $html);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath),
            'record_count' => count($notifications)
        ];
    }

    /**
     * Generate HTML content for PDF export.
     */
    private function generatePdfHtml($notifications, $businessId, $ownerId): string
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Notifications Export</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .export-info { background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 5px; }
        .notification { border: 1px solid #ddd; margin-bottom: 15px; padding: 15px; border-radius: 5px; }
        .notification.unread { background: #fff3cd; }
        .notification.urgent { border-left: 4px solid #dc3545; }
        .notification.high { border-left: 4px solid #ffc107; }
        .notification-header { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .notification-title { font-weight: bold; font-size: 16px; }
        .notification-meta { font-size: 12px; color: #666; }
        .notification-message { margin: 10px 0; }
        .notification-data { background: #f8f9fa; padding: 10px; margin-top: 10px; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Notifications Export Report</h1>
        <p>Business ID: ' . $businessId . ' | Owner ID: ' . $ownerId . '</p>
    </div>

    <div class="export-info">
        <strong>Export Information:</strong><br>
        Generated: ' . now()->format('Y-m-d H:i:s') . '<br>
        Total Notifications: ' . count($notifications) . '<br>
        Format: PDF Report
    </div>';

        foreach ($notifications as $notification) {
            $classes = ['notification'];
            if (!$notification->is_read) $classes[] = 'unread';
            if ($notification->priority === 'urgent') $classes[] = 'urgent';
            if ($notification->priority === 'high') $classes[] = 'high';

            $html .= '<div class="' . implode(' ', $classes) . '">
                <div class="notification-header">
                    <div class="notification-title">' . htmlspecialchars($notification->title) . '</div>
                    <div class="notification-meta">
                        ' . ucfirst($notification->notification_type) . ' | ' . ucfirst($notification->priority) . ' |
                        ' . ($notification->is_read ? 'Read' : 'Unread') . '
                    </div>
                </div>
                <div class="notification-message">' . htmlspecialchars($notification->message) . '</div>
                <div class="notification-meta">
                    Created: ' . $notification->created_at->format('Y-m-d H:i:s') .
                    ($notification->read_at ? ' | Read: ' . $notification->read_at->format('Y-m-d H:i:s') : '') . '
                </div>';

            if ($notification->data) {
                $html .= '<div class="notification-data">
                    <strong>Additional Data:</strong><br>
                    <pre>' . htmlspecialchars(json_encode($notification->data, JSON_PRETTY_PRINT)) . '</pre>
                </div>';
            }

            $html .= '</div>';
        }

        $html .= '</body></html>';

        return $html;
    }

    /**
     * Get export statistics.
     */
    public function getExportStats($businessId, $ownerId): array
    {
        $exportPath = "exports/notifications/";
        $files = Storage::files($exportPath);

        // Filter files for this business/owner
        $userFiles = array_filter($files, function ($file) use ($businessId, $ownerId) {
            return strpos($file, "notifications_export_{$businessId}_{$ownerId}_") !== false;
        });

        $totalSize = 0;
        $filesByFormat = [];

        foreach ($userFiles as $file) {
            $size = Storage::size($file);
            $totalSize += $size;

            $extension = pathinfo($file, PATHINFO_EXTENSION);
            $filesByFormat[$extension] = ($filesByFormat[$extension] ?? 0) + 1;
        }

        return [
            'total_exports' => count($userFiles),
            'total_size_bytes' => $totalSize,
            'total_size_mb' => round($totalSize / 1024 / 1024, 2),
            'files_by_format' => $filesByFormat,
            'recent_exports' => array_slice($userFiles, -5) // Last 5 exports
        ];
    }

    /**
     * Clean up old export files.
     */
    public function cleanupOldExports($businessId, $ownerId, $daysOld = 30): int
    {
        $exportPath = "exports/notifications/";
        $files = Storage::files($exportPath);

        $cutoffDate = Carbon::now()->subDays($daysOld);
        $deletedCount = 0;

        foreach ($files as $file) {
            // Check if file belongs to this business/owner
            if (strpos($file, "notifications_export_{$businessId}_{$ownerId}_") !== false) {
                $lastModified = Carbon::createFromTimestamp(Storage::lastModified($file));

                if ($lastModified->lt($cutoffDate)) {
                    Storage::delete($file);
                    $deletedCount++;
                }
            }
        }

        return $deletedCount;
    }

    /**
     * Export analytics report.
     */
    public function exportAnalytics($businessId, $ownerId, $analytics, $format = 'pdf'): array
    {
        try {
            switch (strtolower($format)) {
                case 'pdf':
                    return $this->exportAnalyticsToPdf($analytics, $businessId, $ownerId);
                case 'excel':
                    return $this->exportAnalyticsToExcel($analytics, $businessId, $ownerId);
                case 'csv':
                    return $this->exportAnalyticsToCsv($analytics, $businessId, $ownerId);
                default:
                    throw new \InvalidArgumentException('Unsupported export format for analytics');
            }
        } catch (\Exception $e) {
            Log::error('Analytics export failed', [
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'format' => $format,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Export failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Export analytics to PDF format.
     */
    private function exportAnalyticsToPdf($analytics, $businessId, $ownerId): array
    {
        $filename = "notification_analytics_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.pdf';
        $filepath = "exports/analytics/{$filename}";

        // Create HTML content for PDF
        $html = $this->generateAnalyticsPdfHtml($analytics, $businessId, $ownerId);

        // For now, we'll save as HTML file
        // In a real implementation, you'd use a library like DomPDF or wkhtmltopdf
        Storage::put($filepath, $html);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath)
        ];
    }

    /**
     * Export analytics to Excel format.
     */
    private function exportAnalyticsToExcel($analytics, $businessId, $ownerId): array
    {
        $filename = "notification_analytics_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.xlsx';
        $filepath = "exports/analytics/{$filename}";

        // Create CSV content and save as Excel
        $csvContent = $this->generateAnalyticsCsvContent($analytics);
        Storage::put($filepath, $csvContent);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath)
        ];
    }

    /**
     * Export analytics to CSV format.
     */
    private function exportAnalyticsToCsv($analytics, $businessId, $ownerId): array
    {
        $filename = "notification_analytics_{$businessId}_{$ownerId}_" . date('Y-m-d_H-i-s') . '.csv';
        $filepath = "exports/analytics/{$filename}";

        $csvContent = $this->generateAnalyticsCsvContent($analytics);
        Storage::put($filepath, $csvContent);

        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'download_url' => Storage::url($filepath),
            'file_size' => Storage::size($filepath)
        ];
    }

    /**
     * Generate analytics PDF HTML content.
     */
    private function generateAnalyticsPdfHtml($analytics, $businessId, $ownerId): string
    {
        $overview = $analytics['overview'] ?? [];
        $trends = $analytics['trends'] ?? [];
        $distribution = $analytics['distribution'] ?? [];
        $performance = $analytics['performance'] ?? [];

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <title>Notification Analytics Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .section { margin-bottom: 25px; }
                .metric { display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ddd; }
                table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
            </style>
        </head>
        <body>
            <div class='header'>
                <h1>Notification Analytics Report</h1>
                <p>Generated on " . date('Y-m-d H:i:s') . "</p>
                <p>Business ID: {$businessId} | Owner ID: {$ownerId}</p>
            </div>

            <div class='section'>
                <h2>Overview Statistics</h2>
                <div class='metric'>
                    <strong>Total Notifications:</strong> " . ($overview['total_notifications'] ?? 0) . "
                </div>
                <div class='metric'>
                    <strong>Read Rate:</strong> " . ($overview['read_rate'] ?? 0) . "%
                </div>
                <div class='metric'>
                    <strong>Avg Response Time:</strong> " . ($overview['average_response_time_hours'] ?? 0) . " hours
                </div>
                <div class='metric'>
                    <strong>Urgent Notifications:</strong> " . ($overview['urgent_notifications'] ?? 0) . "
                </div>
            </div>

            <div class='section'>
                <h2>Performance Metrics</h2>
                <table>
                    <tr>
                        <th>Metric</th>
                        <th>Value</th>
                    </tr>
                    <tr>
                        <td>Engagement Rate</td>
                        <td>" . ($performance['engagement_rate'] ?? 0) . "%</td>
                    </tr>
                    <tr>
                        <td>Fastest Response Time</td>
                        <td>" . ($performance['fastest_response_time'] ?? 0) . " hours</td>
                    </tr>
                    <tr>
                        <td>Notification Frequency</td>
                        <td>" . ($performance['notification_frequency'] ?? 0) . " per day</td>
                    </tr>
                </table>
            </div>

            <div class='section'>
                <h2>Type Distribution</h2>
                <table>
                    <tr>
                        <th>Type</th>
                        <th>Count</th>
                    </tr>";

        foreach (($distribution['type_distribution'] ?? []) as $type => $count) {
            $html .= "<tr><td>{$type}</td><td>{$count}</td></tr>";
        }

        $html .= "
                </table>
            </div>
        </body>
        </html>";

        return $html;
    }

    /**
     * Generate analytics CSV content.
     */
    private function generateAnalyticsCsvContent($analytics): string
    {
        $overview = $analytics['overview'] ?? [];
        $performance = $analytics['performance'] ?? [];
        $distribution = $analytics['distribution'] ?? [];

        $csv = "Notification Analytics Report\n";
        $csv .= "Generated on," . date('Y-m-d H:i:s') . "\n\n";

        $csv .= "Overview Statistics\n";
        $csv .= "Metric,Value\n";
        $csv .= "Total Notifications," . ($overview['total_notifications'] ?? 0) . "\n";
        $csv .= "Read Rate," . ($overview['read_rate'] ?? 0) . "%\n";
        $csv .= "Average Response Time," . ($overview['average_response_time_hours'] ?? 0) . " hours\n";
        $csv .= "Urgent Notifications," . ($overview['urgent_notifications'] ?? 0) . "\n\n";

        $csv .= "Performance Metrics\n";
        $csv .= "Metric,Value\n";
        $csv .= "Engagement Rate," . ($performance['engagement_rate'] ?? 0) . "%\n";
        $csv .= "Fastest Response Time," . ($performance['fastest_response_time'] ?? 0) . " hours\n";
        $csv .= "Notification Frequency," . ($performance['notification_frequency'] ?? 0) . " per day\n\n";

        $csv .= "Type Distribution\n";
        $csv .= "Type,Count\n";
        foreach (($distribution['type_distribution'] ?? []) as $type => $count) {
            $csv .= "{$type},{$count}\n";
        }

        return $csv;
    }
}
