<?php

namespace App\Services;

use App\Models\Setting;
use T<PERSON>lio\Rest\Client as TwilioClient;
use Vonage\Client as VonageClient;
use Vonage\Client\Credentials\Basic as VonageBasic;
use Vonage\SMS\Message\SMS;
use Aws\Sns\SnsClient;
use Illuminate\Support\Facades\Log;
use Exception;

class SmsService
{
    /**
     * Send SMS using the configured provider.
     */
    public function sendSms(string $to, string $message): array
    {
        try {
            // Check if SMS is enabled
            if (!Setting::getValue('sms_enabled', false)) {
                return [
                    'success' => false,
                    'message' => 'SMS notifications are disabled'
                ];
            }

            $provider = Setting::getValue('sms_provider', 'twilio');
            
            switch ($provider) {
                case 'twilio':
                    return $this->sendViaTwilio($to, $message);
                case 'nexmo':
                    return $this->sendViaNexmo($to, $message);
                case 'aws_sns':
                    return $this->sendViaAwsSns($to, $message);
                default:
                    return [
                        'success' => false,
                        'message' => 'Invalid SMS provider configured'
                    ];
            }
        } catch (Exception $e) {
            Log::error('SMS sending failed', [
                'to' => $to,
                'message' => $message,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Failed to send SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send SMS via Twilio.
     */
    protected function sendViaTwilio(string $to, string $message): array
    {
        $accountSid = Setting::getValue('sms_api_key');
        $authToken = Setting::getValue('sms_api_secret');
        $fromNumber = Setting::getValue('sms_from_number');

        if (!$accountSid || !$authToken || !$fromNumber) {
            return [
                'success' => false,
                'message' => 'Twilio credentials not configured properly'
            ];
        }

        try {
            $client = new TwilioClient($accountSid, $authToken);
            
            $twilioMessage = $client->messages->create($to, [
                'from' => $fromNumber,
                'body' => $message
            ]);

            Log::info('Twilio SMS sent successfully', [
                'to' => $to,
                'message_sid' => $twilioMessage->sid,
                'status' => $twilioMessage->status
            ]);

            return [
                'success' => true,
                'message' => 'SMS sent successfully via Twilio',
                'data' => [
                    'provider' => 'twilio',
                    'message_id' => $twilioMessage->sid,
                    'status' => $twilioMessage->status
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Twilio error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send SMS via Nexmo (Vonage).
     */
    protected function sendViaNexmo(string $to, string $message): array
    {
        $apiKey = Setting::getValue('sms_api_key');
        $apiSecret = Setting::getValue('sms_api_secret');
        $fromNumber = Setting::getValue('sms_from_number');

        if (!$apiKey || !$apiSecret || !$fromNumber) {
            return [
                'success' => false,
                'message' => 'Nexmo credentials not configured properly'
            ];
        }

        try {
            $basic = new VonageBasic($apiKey, $apiSecret);
            $client = new VonageClient($basic);

            $smsMessage = new SMS($to, $fromNumber, $message);
            $response = $client->sms()->send($smsMessage);

            $current = $response->current();
            
            if ($current->getStatus() == 0) {
                Log::info('Nexmo SMS sent successfully', [
                    'to' => $to,
                    'message_id' => $current->getMessageId(),
                    'status' => $current->getStatus()
                ]);

                return [
                    'success' => true,
                    'message' => 'SMS sent successfully via Nexmo',
                    'data' => [
                        'provider' => 'nexmo',
                        'message_id' => $current->getMessageId(),
                        'status' => $current->getStatus()
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Nexmo error: ' . $current->getStatusText()
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Nexmo error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send SMS via AWS SNS.
     */
    protected function sendViaAwsSns(string $to, string $message): array
    {
        $accessKey = Setting::getValue('sms_api_key');
        $secretKey = Setting::getValue('sms_api_secret');
        $region = Setting::getValue('aws_region', 'us-east-1');

        if (!$accessKey || !$secretKey) {
            return [
                'success' => false,
                'message' => 'AWS SNS credentials not configured properly'
            ];
        }

        try {
            $client = new SnsClient([
                'version' => 'latest',
                'region' => $region,
                'credentials' => [
                    'key' => $accessKey,
                    'secret' => $secretKey,
                ],
            ]);

            $result = $client->publish([
                'PhoneNumber' => $to,
                'Message' => $message,
            ]);

            Log::info('AWS SNS SMS sent successfully', [
                'to' => $to,
                'message_id' => $result['MessageId']
            ]);

            return [
                'success' => true,
                'message' => 'SMS sent successfully via AWS SNS',
                'data' => [
                    'provider' => 'aws_sns',
                    'message_id' => $result['MessageId']
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'AWS SNS error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test SMS configuration.
     */
    public function testSms(string $to, string $message): array
    {
        return $this->sendSms($to, $message);
    }

    /**
     * Validate provider credentials.
     */
    public function validateCredentials(string $provider, array $credentials): array
    {
        switch ($provider) {
            case 'twilio':
                return $this->validateTwilioCredentials($credentials);
            case 'nexmo':
                return $this->validateNexmoCredentials($credentials);
            case 'aws_sns':
                return $this->validateAwsCredentials($credentials);
            default:
                return [
                    'valid' => false,
                    'message' => 'Invalid provider'
                ];
        }
    }

    /**
     * Validate Twilio credentials.
     */
    protected function validateTwilioCredentials(array $credentials): array
    {
        if (empty($credentials['api_key']) || empty($credentials['api_secret'])) {
            return [
                'valid' => false,
                'message' => 'Account SID and Auth Token are required'
            ];
        }

        try {
            $client = new TwilioClient($credentials['api_key'], $credentials['api_secret']);
            $account = $client->api->accounts($credentials['api_key'])->fetch();
            
            return [
                'valid' => true,
                'message' => 'Twilio credentials are valid',
                'account_name' => $account->friendlyName
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => 'Invalid Twilio credentials: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate Nexmo credentials.
     */
    protected function validateNexmoCredentials(array $credentials): array
    {
        if (empty($credentials['api_key']) || empty($credentials['api_secret'])) {
            return [
                'valid' => false,
                'message' => 'API Key and API Secret are required'
            ];
        }

        try {
            $basic = new VonageBasic($credentials['api_key'], $credentials['api_secret']);
            $client = new VonageClient($basic);
            $balance = $client->account()->getBalance();
            
            return [
                'valid' => true,
                'message' => 'Nexmo credentials are valid',
                'balance' => $balance->getBalance()
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => 'Invalid Nexmo credentials: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate AWS credentials.
     */
    protected function validateAwsCredentials(array $credentials): array
    {
        if (empty($credentials['api_key']) || empty($credentials['api_secret'])) {
            return [
                'valid' => false,
                'message' => 'Access Key ID and Secret Access Key are required'
            ];
        }

        try {
            $client = new SnsClient([
                'version' => 'latest',
                'region' => $credentials['region'] ?? 'us-east-1',
                'credentials' => [
                    'key' => $credentials['api_key'],
                    'secret' => $credentials['api_secret'],
                ],
            ]);

            // Test by listing topics (this will fail if credentials are invalid)
            $client->listTopics(['MaxItems' => 1]);
            
            return [
                'valid' => true,
                'message' => 'AWS SNS credentials are valid'
            ];
        } catch (Exception $e) {
            return [
                'valid' => false,
                'message' => 'Invalid AWS credentials: ' . $e->getMessage()
            ];
        }
    }
}
