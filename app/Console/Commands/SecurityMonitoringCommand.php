<?php

namespace App\Console\Commands;

use App\Services\SecurityMonitoringService;
use App\Services\DataRetentionService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SecurityMonitoringCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'security:monitor 
                            {--scan : Run security scan}
                            {--retention : Execute data retention policies}
                            {--all : Run all security tasks}';

    /**
     * The console command description.
     */
    protected $description = 'Run security monitoring tasks including scans and data retention';

    protected $securityMonitoring;
    protected $dataRetention;

    /**
     * Create a new command instance.
     */
    public function __construct(
        SecurityMonitoringService $securityMonitoring,
        DataRetentionService $dataRetention
    ) {
        parent::__construct();
        $this->securityMonitoring = $securityMonitoring;
        $this->dataRetention = $dataRetention;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting security monitoring tasks...');

        $runScan = $this->option('scan') || $this->option('all');
        $runRetention = $this->option('retention') || $this->option('all');

        if (!$runScan && !$runRetention) {
            // Default behavior - run security scan
            $runScan = true;
        }

        $results = [];

        if ($runScan) {
            $results['security_scan'] = $this->runSecurityScan();
        }

        if ($runRetention) {
            $results['data_retention'] = $this->runDataRetention();
        }

        $this->displayResults($results);
        $this->info('Security monitoring tasks completed.');

        return 0;
    }

    /**
     * Run security monitoring scan
     */
    private function runSecurityScan(): array
    {
        $this->info('Running security scan...');

        try {
            $alerts = $this->securityMonitoring->monitorSecurityEvents();

            $result = [
                'success' => true,
                'alerts_generated' => count($alerts),
                'critical_alerts' => count(array_filter($alerts, fn($alert) => $alert['severity'] === 'critical')),
                'high_alerts' => count(array_filter($alerts, fn($alert) => $alert['severity'] === 'high')),
            ];

            if ($result['critical_alerts'] > 0) {
                $this->error("⚠️  {$result['critical_alerts']} critical security alerts generated!");
            } elseif ($result['high_alerts'] > 0) {
                $this->warn("⚠️  {$result['high_alerts']} high priority security alerts generated.");
            } else {
                $this->info("✅ Security scan completed. {$result['alerts_generated']} alerts generated.");
            }

            Log::info('Security monitoring scan completed', $result);

            return $result;
        } catch (\Exception $e) {
            $this->error("❌ Security scan failed: {$e->getMessage()}");
            
            Log::error('Security monitoring scan failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Run data retention policies
     */
    private function runDataRetention(): array
    {
        $this->info('Executing data retention policies...');

        try {
            $results = $this->dataRetention->executeRetentionPolicies();

            $totalArchived = array_sum(array_column($results, 'archived_count'));
            $totalDeleted = array_sum(array_column($results, 'deleted_count'));

            $this->info("✅ Data retention completed. Archived: {$totalArchived}, Deleted: {$totalDeleted}");

            // Display detailed results
            $this->table(
                ['Data Type', 'Archived', 'Deleted', 'Status'],
                array_map(function ($dataType, $result) {
                    return [
                        $dataType,
                        $result['archived_count'] ?? 0,
                        $result['deleted_count'] ?? 0,
                        $result['success'] ? '✅ Success' : '❌ Failed',
                    ];
                }, array_keys($results), $results)
            );

            Log::info('Data retention policies executed', [
                'total_archived' => $totalArchived,
                'total_deleted' => $totalDeleted,
                'results' => $results,
            ]);

            return [
                'success' => true,
                'total_archived' => $totalArchived,
                'total_deleted' => $totalDeleted,
                'details' => $results,
            ];
        } catch (\Exception $e) {
            $this->error("❌ Data retention failed: {$e->getMessage()}");
            
            Log::error('Data retention execution failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Display command results
     */
    private function displayResults(array $results): void
    {
        $this->newLine();
        $this->info('=== Security Monitoring Results ===');

        foreach ($results as $task => $result) {
            $this->newLine();
            $this->info(strtoupper(str_replace('_', ' ', $task)) . ':');

            if ($result['success']) {
                $this->info('  Status: ✅ Success');
                
                if ($task === 'security_scan') {
                    $this->info("  Alerts Generated: {$result['alerts_generated']}");
                    if ($result['critical_alerts'] > 0) {
                        $this->error("  Critical Alerts: {$result['critical_alerts']}");
                    }
                    if ($result['high_alerts'] > 0) {
                        $this->warn("  High Priority Alerts: {$result['high_alerts']}");
                    }
                } elseif ($task === 'data_retention') {
                    $this->info("  Records Archived: {$result['total_archived']}");
                    $this->info("  Records Deleted: {$result['total_deleted']}");
                }
            } else {
                $this->error('  Status: ❌ Failed');
                $this->error("  Error: {$result['error']}");
            }
        }

        $this->newLine();
    }
}
