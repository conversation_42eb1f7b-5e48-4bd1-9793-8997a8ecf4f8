<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class ProcessDueReminders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reminders:process {--dry-run : Show what would be processed without actually sending}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process due booking reminders and send notifications';

    /**
     * The notification service instance.
     */
    protected $notificationService;

    /**
     * Create a new command instance.
     */
    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Processing due reminders...');
        
        $dryRun = $this->option('dry-run');
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No reminders will actually be sent');
        }

        try {
            if ($dryRun) {
                $processed = $this->showDueReminders();
            } else {
                $processed = $this->notificationService->processDueReminders();
            }

            if ($processed > 0) {
                $this->info("Successfully processed {$processed} due reminders.");
                Log::info("Processed {$processed} due reminders via command");
            } else {
                $this->info('No due reminders to process.');
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error('Error processing reminders: ' . $e->getMessage());
            Log::error('Error processing reminders via command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Show due reminders without processing them.
     */
    protected function showDueReminders(): int
    {
        $dueReminders = \App\Models\BookingReminder::due()->with(['booking'])->get();

        if ($dueReminders->isEmpty()) {
            $this->info('No due reminders found.');
            return 0;
        }

        $this->table(
            ['ID', 'Booking', 'Customer', 'Type', 'Scheduled At', 'Hours Before'],
            $dueReminders->map(function ($reminder) {
                return [
                    $reminder->id,
                    $reminder->booking->booking_number,
                    $reminder->booking->customer_name,
                    ucfirst($reminder->reminder_type),
                    $reminder->scheduled_at->format('Y-m-d H:i:s'),
                    $reminder->hours_before,
                ];
            })->toArray()
        );

        return $dueReminders->count();
    }
}
