@extends('adminlte::page')

@section('title', config('ownerlte.title', 'BookKei Owner'))

@push('meta')
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="user-id" content="{{ auth()->id() }}">
@endpush

@section('content')
    <!-- Integration Notifications Area -->
    <div id="integration-notifications" class="mb-3" style="display: none;"></div>

    <!-- Main Content -->
    @parent
@stop

@section('adminlte_css_pre')
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
@stop

@section('adminlte_css')
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .content-wrapper {
            background-color: #f8f9fa;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .card-header {
            background-color: transparent;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        }

        .btn {
            border-radius: 0.375rem;
        }

        .form-control {
            border-radius: 0.375rem;
        }

        .nav-sidebar .nav-link {
            border-radius: 0.375rem;
            margin: 0.125rem 0;
        }

        .nav-sidebar .nav-link.active {
            color: #fff;
            background-color: #28a745;
        }

        .brand-text {
            font-weight: 600;
        }

        .info-box {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .small-box {
            border-radius: 0.5rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        .navbar-success {
            background-color: #28a745 !important;
        }

        .sidebar-dark-success .nav-sidebar > .nav-item > .nav-link.active {
            background-color: #20c997;
            color: #fff;
        }

        .sidebar-dark-success .nav-sidebar > .nav-item > .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: #fff;
        }

        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
        }

        .metric-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .metric-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }

        .dashboard-widget {
            border-radius: 0.75rem;
            border: none;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
        }

        .widget-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.75rem 0.75rem 0 0;
            padding: 1rem 1.25rem;
        }

        .widget-title {
            font-weight: 600;
            margin: 0;
            color: #495057;
        }

        .widget-body {
            padding: 1.25rem;
        }
    </style>
@stop

@section('adminlte_js')
    <!-- Ensure jQuery is loaded -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- Toastr JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Integration JavaScript -->
    <script src="{{ asset('js/owner-integration.js') }}"></script>

    <script>
        // Initialize tooltips
        $(function () {
            $('[data-toggle="tooltip"]').tooltip();
        });

        // Global CSRF token setup for AJAX
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // Auto-refresh functionality for dashboard
        if (window.location.pathname.includes('/owner/dashboard')) {
            setInterval(function() {
                // Auto-refresh dashboard stats every 5 minutes
                if (typeof refreshDashboardStats === 'function') {
                    refreshDashboardStats();
                }
            }, 300000);
        }
    </script>
@stop
