@extends('owner.layouts.app')

@section('title', 'Notification Analytics')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 id="main-heading" tabindex="-1">Notification Analytics</h1>
            <p class="text-muted" id="page-description">Comprehensive insights into your notification performance</p>
        </div>
        <div class="d-flex" style="gap: 10px;" role="toolbar" aria-label="Analytics actions">
            <button type="button" class="btn btn-outline-primary" id="refreshAnalyticsBtn"
                    aria-describedby="refresh-help" title="Refresh analytics data">
                <i class="fas fa-sync-alt" aria-hidden="true"></i>
                <span>Refresh</span>
            </button>
            <div id="refresh-help" class="sr-only">Refreshes analytics data to show latest metrics</div>

            <button type="button" class="btn btn-outline-success" id="exportAnalyticsBtn"
                    aria-describedby="export-help" title="Export analytics report">
                <i class="fas fa-download" aria-hidden="true"></i>
                <span>Export Report</span>
            </button>
            <div id="export-help" class="sr-only">Exports analytics data to PDF or Excel format</div>
        </div>
    </div>
@stop

@section('content')
    {{-- Warning Message --}}
    @if(session('warning'))
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2" aria-hidden="true"></i>
            {{ session('warning') }}
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    @endif

    {{-- Overview Statistics --}}
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="total-notifications" aria-live="polite">{{ $analytics['overview']['total_notifications'] ?? 0 }}</h3>
                    <p>Total Notifications</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bell" aria-hidden="true"></i>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="read-rate" aria-live="polite">{{ $analytics['overview']['read_rate'] ?? 0 }}%</h3>
                    <p>Read Rate</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle" aria-hidden="true"></i>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="avg-response-time" aria-live="polite">{{ $analytics['overview']['average_response_time_hours'] ?? 0 }}h</h3>
                    <p>Avg Response Time</p>
                </div>
                <div class="icon">
                    <i class="fas fa-clock" aria-hidden="true"></i>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="urgent-notifications" aria-live="polite">{{ $analytics['overview']['urgent_notifications'] ?? 0 }}</h3>
                    <p>Urgent Notifications</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle" aria-hidden="true"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Charts Row --}}
    <div class="row mb-4">
        {{-- Notification Trends Chart --}}
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-line mr-2" aria-hidden="true"></i>
                        Notification Trends
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="trendsChart" role="img" aria-label="Notification trends over time chart"
                            style="height: 300px;"></canvas>
                </div>
            </div>
        </div>

        {{-- Type Distribution Chart --}}
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-pie mr-2" aria-hidden="true"></i>
                        Type Distribution
                    </h3>
                </div>
                <div class="card-body">
                    <canvas id="typeChart" role="img" aria-label="Notification type distribution chart"
                            style="height: 300px;"></canvas>
                </div>
            </div>
        </div>
    </div>

    {{-- Performance Metrics --}}
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tachometer-alt mr-2" aria-hidden="true"></i>
                        Performance Metrics
                    </h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" role="table" aria-label="Performance metrics">
                            <thead>
                                <tr>
                                    <th scope="col">Metric</th>
                                    <th scope="col">Value</th>
                                    <th scope="col">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Engagement Rate</td>
                                    <td>{{ $analytics['performance']['engagement_rate'] ?? 0 }}%</td>
                                    <td>
                                        <span class="badge badge-success" aria-label="Good performance">
                                            <i class="fas fa-check" aria-hidden="true"></i> Good
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Fastest Response</td>
                                    <td>{{ $analytics['performance']['fastest_response_time'] ?? 0 }}h</td>
                                    <td>
                                        <span class="badge badge-info" aria-label="Excellent performance">
                                            <i class="fas fa-star" aria-hidden="true"></i> Excellent
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Notification Frequency</td>
                                    <td>{{ $analytics['performance']['notification_frequency'] ?? 0 }}/day</td>
                                    <td>
                                        <span class="badge badge-warning" aria-label="Moderate frequency">
                                            <i class="fas fa-info-circle" aria-hidden="true"></i> Moderate
                                        </span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        {{-- AI Insights --}}
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-brain mr-2" aria-hidden="true"></i>
                        AI Insights
                    </h3>
                </div>
                <div class="card-body">
                    <div id="ai-insights" role="region" aria-label="AI-generated insights">
                        @if(isset($analytics['insights']) && is_array($analytics['insights']) && count($analytics['insights']) > 0)
                            @foreach($analytics['insights'] as $insight)
                                <div class="alert alert-info" role="alert">
                                    <i class="fas fa-lightbulb mr-2" aria-hidden="true"></i>
                                    {{ is_string($insight) ? $insight : (is_array($insight) ? implode(', ', $insight) : 'Insight data available') }}
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-secondary" role="alert">
                                <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
                                No insights available yet. More data needed for AI analysis.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Recommendations --}}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-recommendations mr-2" aria-hidden="true"></i>
                        Recommendations
                    </h3>
                </div>
                <div class="card-body">
                    <div id="recommendations" role="region" aria-label="System recommendations">
                        @if(isset($analytics['recommendations']) && is_array($analytics['recommendations']) && count($analytics['recommendations']) > 0)
                            @foreach($analytics['recommendations'] as $recommendation)
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle mr-2" aria-hidden="true"></i>
                                    @if(is_string($recommendation))
                                        {{ $recommendation }}
                                    @elseif(is_array($recommendation) && isset($recommendation['message']))
                                        {{ $recommendation['message'] }}
                                        @if(isset($recommendation['action']))
                                            <br><small class="text-muted">{{ $recommendation['action'] }}</small>
                                        @endif
                                    @else
                                        Recommendation available
                                    @endif
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle mr-2" aria-hidden="true"></i>
                                Your notification system is performing well. Keep up the good work!
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@stop

@section('css')
<style>
/* WCAG 2.1 AA Compliance Styles */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }

    .badge {
        border: 1px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus indicators */
button:focus,
a:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 3px solid #007bff;
    outline-offset: 2px;
}

/* Color contrast improvements */
.text-muted {
    color: #6c757d !important;
}

.small-box .inner h3 {
    font-weight: bold;
    color: #fff;
}
</style>
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Initialize charts with accessibility features
    initializeTrendsChart();
    initializeTypeChart();

    // Refresh functionality
    $('#refreshAnalyticsBtn').click(function() {
        refreshAnalytics();
    });

    // Export functionality
    $('#exportAnalyticsBtn').click(function() {
        exportAnalytics();
    });
});

function initializeTrendsChart() {
    const ctx = document.getElementById('trendsChart').getContext('2d');
    const trendsData = @json($analytics['trends']['daily_stats'] ?? []);

    // Ensure trendsData is an object
    const chartData = typeof trendsData === 'object' && trendsData !== null ? trendsData : {};

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: Object.keys(chartData),
            datasets: [{
                label: 'Daily Notifications',
                data: Object.values(chartData).map(item => typeof item === 'object' && item !== null ? (item.total || 0) : 0),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    labels: {
                        usePointStyle: true
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Notifications'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Date'
                    }
                }
            },
            accessibility: {
                announceNewData: {
                    enabled: true
                }
            }
        }
    });
}

function initializeTypeChart() {
    const ctx = document.getElementById('typeChart').getContext('2d');
    const typeData = @json($analytics['distribution']['type_distribution'] ?? []);

    // Ensure typeData is an object
    const chartData = typeof typeData === 'object' && typeData !== null ? typeData : {};

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(chartData),
            datasets: [{
                data: Object.values(chartData),
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0',
                    '#9966FF',
                    '#FF9F40',
                    '#FF6384',
                    '#C9CBCF',
                    '#4BC0C0',
                    '#FF6384'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            },
            accessibility: {
                announceNewData: {
                    enabled: true
                }
            }
        }
    });
}

function refreshAnalytics() {
    // Show loading state
    $('#refreshAnalyticsBtn').prop('disabled', true);
    $('#refreshAnalyticsBtn i').addClass('fa-spin');

    // Simulate refresh (in real implementation, make AJAX call)
    setTimeout(function() {
        location.reload();
    }, 1000);
}

function exportAnalytics() {
    // Show loading state
    $('#exportAnalyticsBtn').prop('disabled', true);

    // Make AJAX call to export endpoint
    $.ajax({
        url: '{{ route("owner.notifications.export-analytics") }}',
        method: 'POST',
        data: {
            _token: $('meta[name="csrf-token"]').attr('content'),
            format: 'pdf'
        },
        success: function(response) {
            if (response.success) {
                // Create download link
                const link = document.createElement('a');
                link.href = response.download_url;
                link.download = response.filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                toastr.success('Analytics report exported successfully');
            } else {
                toastr.error('Failed to export analytics report');
            }
        },
        error: function() {
            toastr.error('Failed to export analytics report');
        },
        complete: function() {
            $('#exportAnalyticsBtn').prop('disabled', false);
        }
    });
}

// Keyboard navigation support
$(document).keydown(function(e) {
    // Alt + R for refresh
    if (e.altKey && e.keyCode === 82) {
        e.preventDefault();
        $('#refreshAnalyticsBtn').click();
    }

    // Alt + E for export
    if (e.altKey && e.keyCode === 69) {
        e.preventDefault();
        $('#exportAnalyticsBtn').click();
    }
});

// Announce page load for screen readers
setTimeout(function() {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = 'Notification analytics page loaded successfully';
    document.body.appendChild(announcement);

    setTimeout(function() {
        document.body.removeChild(announcement);
    }, 1000);
}, 500);
</script>
@stop
