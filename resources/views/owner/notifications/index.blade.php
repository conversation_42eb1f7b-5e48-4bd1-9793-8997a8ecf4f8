@extends('owner.layouts.app')

@section('title', 'Notifications')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 id="main-heading" tabindex="-1">Notifications</h1>
            <p class="text-muted" id="page-description">Manage your business notifications and alerts</p>
        </div>
        <div class="d-flex" style="gap: 10px;" role="toolbar" aria-label="Notification actions">
            <button type="button" class="btn btn-outline-primary" id="markAllReadBtn"
                    aria-describedby="mark-all-help" title="Mark all notifications as read">
                <i class="fas fa-check-double" aria-hidden="true"></i>
                <span>Mark All Read</span>
            </button>
            <div id="mark-all-help" class="sr-only">Marks all unread notifications as read</div>

            <button type="button" class="btn btn-outline-secondary" id="refreshBtn"
                    aria-describedby="refresh-help" title="Refresh notification list">
                <i class="fas fa-sync-alt" aria-hidden="true"></i>
                <span>Refresh</span>
            </button>
            <div id="refresh-help" class="sr-only">Refreshes the notification list to show latest updates</div>

            <button type="button" class="btn btn-outline-info" id="soundToggleBtn"
                    aria-describedby="sound-help" title="Toggle notification sounds">
                <i class="fas fa-volume-up" aria-hidden="true"></i>
                <span id="soundStatus">Sound On</span>
            </button>
            <div id="sound-help" class="sr-only">Toggles audio notifications on or off</div>

            <button type="button" class="btn btn-outline-success" id="exportBtn"
                    aria-describedby="export-help" title="Export notifications to file">
                <i class="fas fa-download" aria-hidden="true"></i>
                <span>Export</span>
            </button>
            <div id="export-help" class="sr-only">Exports notification data to CSV, Excel, or PDF format</div>

            <a href="{{ route('owner.notifications.analytics-dashboard') }}" class="btn btn-outline-info"
               aria-describedby="analytics-help" title="View notification analytics">
                <i class="fas fa-chart-bar" aria-hidden="true"></i>
                <span>Analytics</span>
            </a>
            <div id="analytics-help" class="sr-only">View detailed analytics and insights about your notifications</div>

            <a href="{{ route('owner.notification-preferences.index') }}" class="btn btn-outline-warning"
               aria-describedby="settings-help" title="Notification preferences">
                <i class="fas fa-cog" aria-hidden="true"></i>
                <span>Settings</span>
            </a>
            <div id="settings-help" class="sr-only">Configure notification preferences and delivery settings</div>
        </div>
    </div>
@stop

@push('css')
<style>
.notification-row.unread {
    background-color: #fff3cd !important;
}
.notification-row.read {
    background-color: #ffffff;
}
.notification-actions .btn {
    margin-right: 5px;
}
.notification-priority-urgent {
    border-left: 4px solid #dc3545;
}
.notification-priority-high {
    border-left: 4px solid #ffc107;
}
</style>
@endpush

@section('content')
    {{-- Statistics Cards --}}
    <div class="row mb-4">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3 id="totalCount">{{ $stats['total'] ?? 0 }}</h3>
                    <p>Total Notifications</p>
                </div>
                <div class="icon">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3 id="unreadCount">{{ $stats['unread'] ?? 0 }}</h3>
                    <p>Unread</p>
                </div>
                <div class="icon">
                    <i class="fas fa-envelope"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-danger">
                <div class="inner">
                    <h3 id="urgentCount">{{ $stats['urgent'] ?? 0 }}</h3>
                    <p>Urgent</p>
                </div>
                <div class="icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3 id="todayCount">{{ $stats['today'] ?? 0 }}</h3>
                    <p>Today</p>
                </div>
                <div class="icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
            </div>
        </div>
    </div>

    {{-- Filters and Search --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters & Search
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('owner.notifications.index') }}" id="filterForm">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="type">Type</label>
                            <select name="type" id="type" class="form-control">
                                <option value="">All Types</option>
                                <option value="booking" {{ request('type') === 'booking' ? 'selected' : '' }}>Bookings</option>
                                <option value="cancellation" {{ request('type') === 'cancellation' ? 'selected' : '' }}>Cancellations</option>
                                <option value="payment" {{ request('type') === 'payment' ? 'selected' : '' }}>Payments</option>
                                <option value="review" {{ request('type') === 'review' ? 'selected' : '' }}>Reviews</option>
                                <option value="system" {{ request('type') === 'system' ? 'selected' : '' }}>System</option>
                                <option value="marketing" {{ request('type') === 'marketing' ? 'selected' : '' }}>Marketing</option>
                                <option value="alert" {{ request('type') === 'alert' ? 'selected' : '' }}>Alerts</option>
                                <option value="reminder" {{ request('type') === 'reminder' ? 'selected' : '' }}>Reminders</option>
                                <option value="customer_message" {{ request('type') === 'customer_message' ? 'selected' : '' }}>Customer Messages</option>
                                <option value="waiting_list" {{ request('type') === 'waiting_list' ? 'selected' : '' }}>Waiting Lists</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="priority">Priority</label>
                            <select name="priority" id="priority" class="form-control">
                                <option value="">All Priorities</option>
                                <option value="urgent" {{ request('priority') === 'urgent' ? 'selected' : '' }}>Urgent</option>
                                <option value="high" {{ request('priority') === 'high' ? 'selected' : '' }}>High</option>
                                <option value="normal" {{ request('priority') === 'normal' ? 'selected' : '' }}>Normal</option>
                                <option value="low" {{ request('priority') === 'low' ? 'selected' : '' }}>Low</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">All</option>
                                <option value="unread" {{ request('status') === 'unread' ? 'selected' : '' }}>Unread</option>
                                <option value="read" {{ request('status') === 'read' ? 'selected' : '' }}>Read</option>
                                <option value="deleted" {{ request('status') === 'deleted' ? 'selected' : '' }}>Deleted</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_from">From Date</label>
                            <input type="date" name="date_from" id="date_from" class="form-control" value="{{ request('date_from') }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="date_to">To Date</label>
                            <input type="date" name="date_to" id="date_to" class="form-control" value="{{ request('date_to') }}">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" name="search" id="search" class="form-control" placeholder="Search notifications..." value="{{ request('search') }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-secondary" id="clearFiltersBtn">
                                    <i class="fas fa-times"></i> Clear Filters
                                </button>
                                <button type="button" class="btn btn-info" id="bulkActionsBtn" disabled>
                                    <i class="fas fa-tasks"></i> Bulk Actions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {{-- Notifications List --}}
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-list mr-2"></i>
                Notifications
                @if($notifications->total() > 0)
                    <span class="badge badge-info ml-2">{{ $notifications->total() }}</span>
                @endif
            </h3>
            <div class="card-tools">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="selectAll">
                    <label class="form-check-label" for="selectAll">
                        Select All
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if($notifications->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <tbody>
                            @foreach($notifications as $notification)
                                <tr class="notification-row {{ !$notification->is_read ? 'unread table-warning' : 'read' }} {{ $notification->priority === 'urgent' ? 'notification-priority-urgent' : '' }} {{ $notification->priority === 'high' ? 'notification-priority-high' : '' }}"
                                    data-id="{{ $notification->id }}"
                                    data-read="{{ $notification->is_read ? 'true' : 'false' }}">
                                    <td width="30">
                                        <div class="form-check">
                                            <input class="form-check-input notification-checkbox"
                                                   type="checkbox"
                                                   value="{{ $notification->id }}"
                                                   id="notification_{{ $notification->id }}">
                                        </div>
                                    </td>
                                    <td width="50">
                                        <div class="d-flex align-items-center">
                                            <i class="{{ $notification->type_icon }} text-{{ $notification->type_color }} mr-2"></i>
                                            @if($notification->priority === 'urgent' || $notification->priority === 'high')
                                                <i class="{{ $notification->priority_icon }} text-{{ $notification->priority_color }}"></i>
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 notification-title {{ !$notification->is_read ? 'font-weight-bold' : '' }}">
                                                    {{ $notification->title }}
                                                    @if(!$notification->is_read)
                                                        <span class="badge badge-primary badge-sm ml-1 new-badge">New</span>
                                                    @endif
                                                    @if($notification->priority === 'urgent')
                                                        <span class="badge badge-danger badge-sm ml-1">Urgent</span>
                                                    @elseif($notification->priority === 'high')
                                                        <span class="badge badge-warning badge-sm ml-1">High</span>
                                                    @endif
                                                </h6>
                                                <p class="mb-1 text-muted">{{ $notification->short_message }}</p>
                                                <small class="text-muted">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    {{ $notification->formatted_created_at }}
                                                    @if($notification->source_type)
                                                        • <span class="text-capitalize">{{ str_replace('_', ' ', $notification->source_type) }}</span>
                                                    @endif
                                                </small>
                                            </div>
                                            <div class="ml-3 notification-actions">
                                                <div class="btn-group btn-group-sm">
                                                    <button type="button"
                                                            class="btn btn-outline-primary btn-sm view-notification"
                                                            data-id="{{ $notification->id }}"
                                                            title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    @if(!$notification->is_read)
                                                        <button type="button"
                                                                class="btn btn-outline-success btn-sm mark-read"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Read"
                                                                style="display: inline-block;">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button"
                                                                class="btn btn-outline-warning btn-sm mark-unread"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Unread"
                                                                style="display: none;">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    @else
                                                        <button type="button"
                                                                class="btn btn-outline-success btn-sm mark-read"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Read"
                                                                style="display: none;">
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button type="button"
                                                                class="btn btn-outline-warning btn-sm mark-unread"
                                                                data-id="{{ $notification->id }}"
                                                                title="Mark as Unread"
                                                                style="display: inline-block;">
                                                            <i class="fas fa-undo"></i>
                                                        </button>
                                                    @endif
                                                    <button type="button"
                                                            class="btn btn-outline-danger btn-sm delete-notification"
                                                            data-id="{{ $notification->id }}"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                    <h4>No Notifications Found</h4>
                    <p class="text-muted">
                        @if(request()->hasAny(['type', 'priority', 'status', 'search', 'date_from', 'date_to']))
                            No notifications match your current filters. Try adjusting your search criteria.
                        @else
                            You don't have any notifications yet. They will appear here when you receive them.
                        @endif
                    </p>
                    @if(request()->hasAny(['type', 'priority', 'status', 'search', 'date_from', 'date_to']))
                        <a href="{{ route('owner.notifications.index') }}" class="btn btn-primary">
                            <i class="fas fa-times mr-1"></i> Clear Filters
                        </a>
                    @endif
                </div>
            @endif
        </div>
        @if($notifications->hasPages())
            <div class="card-footer">
                {{ $notifications->links() }}
            </div>
        @endif
    </div>

    {{-- INLINE JAVASCRIPT TEST --}}
    <script>
        console.log('=== INLINE SCRIPT LOADING ===');

        // Ensure jQuery is loaded before proceeding
        function waitForJQuery(callback) {
            if (typeof $ !== 'undefined') {
                callback();
            } else {
                console.log('jQuery not loaded yet, waiting...');
                setTimeout(function() {
                    waitForJQuery(callback);
                }, 100);
            }
        }

        // Test basic JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== DOM CONTENT LOADED ===');

            waitForJQuery(function() {
                console.log('jQuery is available:', $.fn.jquery);

                // Add simple click handlers
                $('#testBtn').on('click', function() {
                    alert('INLINE: Test button clicked!');
                    console.log('Test button clicked via inline script');
                });

                $('#markAllReadBtn').on('click', function(e) {
                    e.preventDefault();
                    console.log('Mark All Read clicked via inline script');

                    if (confirm('Mark all notifications as read?')) {
                        console.log('User confirmed, sending mark all read request...');

                        $.ajax({
                            url: '{{ route("owner.notifications.mark-all-read") }}',
                            type: 'POST',
                            data: {
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log('Mark all read response:', response);
                                if (response.success) {
                                    // Show success message
                                    if (typeof toastr !== 'undefined') {
                                        toastr.success(response.message);
                                    } else {
                                        alert('Success: ' + response.message);
                                    }

                                    // Reload the page to reflect changes
                                    location.reload();
                                } else {
                                    alert('Failed to mark all notifications as read');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Mark all read failed:', xhr, status, error);
                                alert('Error: Failed to mark all notifications as read');
                            }
                        });
                    }
                });

                // Add handlers for notification buttons with REAL functionality
                $(document).on('click', '.view-notification', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    console.log('View notification clicked via inline script:', id);

                    // Show notification details in modal
                    $('#notificationModal').modal('show');
                    $('#notificationModalBody').html('<div class="text-center py-3"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading...</p></div>');

                    $.get('{{ route("owner.notifications.show", ":id") }}'.replace(':id', id))
                        .done(function(response) {
                            if (response.success) {
                                const notification = response.notification;
                                $('#notificationModalBody').html(`
                                    <div class="notification-detail">
                                        <div class="d-flex align-items-center mb-3">
                                            <i class="fas fa-bell fa-2x mr-3 text-info"></i>
                                            <div>
                                                <h5 class="mb-1">${notification.title}</h5>
                                                <small class="text-muted">${new Date(notification.created_at).toLocaleString()}</small>
                                            </div>
                                        </div>
                                        <div class="notification-message">
                                            <p>${notification.message}</p>
                                        </div>
                                    </div>
                                `);

                                // Mark as read if not already read
                                if (!notification.is_read) {
                                    markNotificationAsRead(id);
                                }
                            }
                        })
                        .fail(function() {
                            $('#notificationModalBody').html('<div class="alert alert-danger">Failed to load notification details.</div>');
                        });
                });

                $(document).on('click', '.mark-read', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    console.log('Mark as read clicked via inline script:', id);
                    markNotificationAsRead(id);
                });

                $(document).on('click', '.mark-unread', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    console.log('Mark as unread clicked via inline script:', id);
                    markNotificationAsUnread(id);
                });

                $(document).on('click', '.delete-notification', function(e) {
                    e.preventDefault();
                    const id = $(this).data('id');
                    console.log('Delete notification clicked via inline script:', id);

                    if (confirm('Are you sure you want to delete this notification?')) {
                        deleteNotification(id);
                    }
                });

                // Helper functions for AJAX operations
                window.markNotificationAsRead = function(notificationId) {
                    console.log('Marking notification as read:', notificationId);
                    $.ajax({
                        url: '{{ route("owner.notifications.mark-read", ":id") }}'.replace(':id', notificationId),
                        type: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            console.log('Mark read response:', response);
                            if (response.success) {
                                // Show success message
                                if (typeof toastr !== 'undefined') {
                                    toastr.success(response.message);
                                } else {
                                    alert('Success: ' + response.message);
                                }

                                // Update the UI
                                updateNotificationRowStatus(notificationId, true);
                                refreshStats();
                            } else {
                                alert('Failed to mark notification as read');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Mark read failed:', xhr, status, error);
                            alert('Error: Failed to mark notification as read');
                        }
                    });
                };

                window.markNotificationAsUnread = function(notificationId) {
                    console.log('Marking notification as unread:', notificationId);
                    $.ajax({
                        url: '{{ route("owner.notifications.mark-unread", ":id") }}'.replace(':id', notificationId),
                        type: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            console.log('Mark unread response:', response);
                            if (response.success) {
                                // Show success message
                                if (typeof toastr !== 'undefined') {
                                    toastr.success(response.message);
                                } else {
                                    alert('Success: ' + response.message);
                                }

                                // Update the UI
                                updateNotificationRowStatus(notificationId, false);
                                refreshStats();
                            } else {
                                alert('Failed to mark notification as unread');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Mark unread failed:', xhr, status, error);
                            alert('Error: Failed to mark notification as unread');
                        }
                    });
                };

                window.deleteNotification = function(notificationId) {
                    console.log('Deleting notification:', notificationId);
                    $.ajax({
                        url: '{{ route("owner.notifications.delete", ":id") }}'.replace(':id', notificationId),
                        type: 'POST',
                        data: {
                            _token: $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            console.log('Delete response:', response);
                            if (response.success) {
                                // Show success message
                                if (typeof toastr !== 'undefined') {
                                    toastr.success(response.message);
                                } else {
                                    alert('Success: ' + response.message);
                                }

                                // Remove the notification row
                                $('tr[data-id="' + notificationId + '"]').fadeOut(300, function() {
                                    $(this).remove();
                                });
                                refreshStats();
                            } else {
                                alert('Failed to delete notification');
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Delete failed:', xhr, status, error);
                            alert('Error: Failed to delete notification');
                        }
                    });
                };

                // Helper function to update notification row status
                window.updateNotificationRowStatus = function(notificationId, isRead) {
                    const row = $('tr[data-id="' + notificationId + '"]');
                    const readButton = row.find('.mark-read');
                    const unreadButton = row.find('.mark-unread');
                    const title = row.find('.notification-title');
                    const newBadge = row.find('.new-badge');

                    if (isRead) {
                        row.removeClass('unread table-warning').addClass('read');
                        row.data('read', 'true');
                        title.removeClass('font-weight-bold');
                        newBadge.remove();
                        readButton.hide();
                        unreadButton.show();
                    } else {
                        row.removeClass('read').addClass('unread table-warning');
                        row.data('read', 'false');
                        title.addClass('font-weight-bold');
                        if (newBadge.length === 0) {
                            title.append('<span class="badge badge-primary badge-sm ml-1 new-badge">New</span>');
                        }
                        unreadButton.hide();
                        readButton.show();
                    }
                };

                // Helper function to refresh statistics
                window.refreshStats = function() {
                    $.get('{{ route("owner.notifications.stats") }}')
                        .done(function(response) {
                            if (response.success) {
                                $('#totalCount').text(response.stats.total);
                                $('#unreadCount').text(response.stats.unread);
                                $('#urgentCount').text(response.stats.urgent);
                                $('#todayCount').text(response.stats.today);
                            }
                        })
                        .fail(function() {
                            console.log('Failed to refresh stats');
                        });
                };

                console.log('=== INLINE HANDLERS ATTACHED ===');
            });
        });
    </script>

    {{-- Notification Detail Modal --}}
    <div class="modal fade" id="notificationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-bell mr-2"></i>
                        Notification Details
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="notificationModalBody">
                    <div class="text-center py-3">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <p class="mt-2">Loading notification details...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="markReadFromModal" style="display: none;">
                        <i class="fas fa-check"></i> Mark as Read
                    </button>
                    <button type="button" class="btn btn-warning" id="markUnreadFromModal" style="display: none;">
                        <i class="fas fa-undo"></i> Mark as Unread
                    </button>
                    <button type="button" class="btn btn-danger" id="deleteFromModal" style="display: none;">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Bulk Actions Modal --}}
    <div class="modal fade" id="bulkActionsModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-tasks mr-2"></i>
                        Bulk Actions
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Select an action to perform on <span id="selectedCount">0</span> selected notifications:</p>
                    <div class="list-group">
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="mark_read">
                            <i class="fas fa-check text-success mr-2"></i>
                            Mark as Read
                        </button>
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="mark_unread">
                            <i class="fas fa-undo text-warning mr-2"></i>
                            Mark as Unread
                        </button>
                        <button type="button" class="list-group-item list-group-item-action bulk-action" data-action="delete">
                            <i class="fas fa-trash text-danger mr-2"></i>
                            Delete Selected
                        </button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
alert('JavaScript section is loading!');
console.log('Notification JavaScript loading...');

$(document).ready(function() {
    alert('jQuery document ready fired!');
    console.log('Document ready - jQuery loaded:', typeof $ !== 'undefined');
    console.log('Toastr loaded:', typeof toastr !== 'undefined');

    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));

    // Test if buttons exist
    console.log('Mark all read button exists:', $('#markAllReadBtn').length > 0);
    console.log('View buttons exist:', $('.view-notification').length);
    console.log('Mark read buttons exist:', $('.mark-read').length);
    console.log('Delete buttons exist:', $('.delete-notification').length);

    // Auto-refresh notifications every 30 seconds
    let autoRefreshInterval = setInterval(function() {
        refreshStats();
    }, 30000);

    // Refresh button
    $('#refreshBtn').click(function() {
        console.log('Refresh button clicked');
        location.reload();
    });

    // Clear filters button
    $('#clearFiltersBtn').click(function() {
        console.log('Clear filters button clicked');
        window.location.href = '{{ route("owner.notifications.index") }}';
    });

    // Test button - SIMPLIFIED
    $('#testBtn').on('click', function(e) {
        e.preventDefault();
        console.log('=== TEST BUTTON CLICKED ===');
        alert('Test button works! Check console for details.');
        console.log('jQuery version:', $.fn.jquery);
        console.log('Toastr available:', typeof toastr !== 'undefined');
        console.log('CSRF token:', $('meta[name="csrf-token"]').attr('content'));
        if (typeof toastr !== 'undefined') {
            toastr.success('JavaScript is working correctly!');
        }
    });

    // Mark All Read - SIMPLIFIED
    $('#markAllReadBtn').on('click', function(e) {
        e.preventDefault();
        console.log('=== MARK ALL READ CLICKED ===');
        alert('Mark All Read clicked! Check console.');

        if (confirm('Mark all notifications as read?')) {
            console.log('User confirmed, sending request...');

            $.ajax({
                url: '{{ route("owner.notifications.mark-all-read") }}',
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Success response:', response);
                    alert('Success: ' + response.message);
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Error:', xhr, status, error);
                    alert('Error: ' + error);
                }
            });
        }
    });

    // Select all checkbox
    $('#selectAll').on('change', function() {
        console.log('Select all checkbox changed');
        $('.notification-checkbox').prop('checked', this.checked);
        updateBulkActionsButton();
    });

    // Individual notification checkboxes
    $(document).on('change', '.notification-checkbox', function() {
        updateBulkActionsButton();

        // Update select all checkbox
        const totalCheckboxes = $('.notification-checkbox').length;
        const checkedCheckboxes = $('.notification-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
    });

    // Update bulk actions button state
    function updateBulkActionsButton() {
        const checkedCount = $('.notification-checkbox:checked').length;
        $('#bulkActionsBtn').prop('disabled', checkedCount === 0);
        $('#selectedCount').text(checkedCount);
    }

    // Bulk actions button
    $('#bulkActionsBtn').click(function() {
        const checkedCount = $('.notification-checkbox:checked').length;
        if (checkedCount > 0) {
            $('#selectedCount').text(checkedCount);
            $('#bulkActionsModal').modal('show');
        }
    });

    // Bulk action execution
    $('.bulk-action').click(function() {
        const action = $(this).data('action');
        const selectedIds = $('.notification-checkbox:checked').map(function() {
            return $(this).val();
        }).get();

        if (selectedIds.length === 0) {
            toastr.warning('No notifications selected');
            return;
        }

        // Confirm destructive actions
        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete the selected notifications?')) {
                return;
            }
        }

        performBulkAction(action, selectedIds);
        $('#bulkActionsModal').modal('hide');
    });

    // Mark all as read
    $('#markAllReadBtn').click(function() {
        console.log('Mark all read button clicked');
        if (confirm('Mark all notifications as read?')) {
            console.log('Sending mark all read request');
            $.post('{{ route("owner.notifications.mark-all-read") }}')
                .done(function(response) {
                    console.log('Mark all read response:', response);
                    if (response.success) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error('Failed to mark notifications as read');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('Mark all read failed:', xhr, status, error);
                    toastr.error('Failed to mark notifications as read');
                });
        }
    });

    // View notification details - COMPLETE MODAL IMPLEMENTATION
    $(document).on('click', '.view-notification', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('=== VIEW NOTIFICATION CLICKED ===', notificationId);

        // Show modal and load notification details
        loadNotificationDetails(notificationId);
    });

    // Mark as read - SIMPLIFIED
    $(document).on('click', '.mark-read', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('=== MARK AS READ CLICKED ===', notificationId);
        alert('Mark as read ' + notificationId + ' clicked!');

        $.ajax({
            url: '{{ route("owner.notifications.mark-read", ":id") }}'.replace(':id', notificationId),
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Mark read response:', response);
                alert('Success: ' + response.message);
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('Mark read failed:', xhr, status, error);
                alert('Error: ' + error);
            }
        });
    });

    // Mark as unread - SIMPLIFIED
    $(document).on('click', '.mark-unread', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('=== MARK AS UNREAD CLICKED ===', notificationId);
        alert('Mark as unread ' + notificationId + ' clicked!');

        $.ajax({
            url: '{{ route("owner.notifications.mark-unread", ":id") }}'.replace(':id', notificationId),
            type: 'POST',
            data: {
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                console.log('Mark unread response:', response);
                alert('Success: ' + response.message);
                location.reload();
            },
            error: function(xhr, status, error) {
                console.error('Mark unread failed:', xhr, status, error);
                alert('Error: ' + error);
            }
        });
    });

    // Delete notification - SIMPLIFIED
    $(document).on('click', '.delete-notification', function(e) {
        e.preventDefault();
        const notificationId = $(this).data('id');
        console.log('=== DELETE NOTIFICATION CLICKED ===', notificationId);
        alert('Delete notification ' + notificationId + ' clicked!');

        if (confirm('Are you sure you want to delete this notification?')) {
            $.ajax({
                url: '{{ route("owner.notifications.delete", ":id") }}'.replace(':id', notificationId),
                type: 'POST',
                data: {
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    console.log('Delete response:', response);
                    alert('Success: ' + response.message);
                    location.reload();
                },
                error: function(xhr, status, error) {
                    console.error('Delete failed:', xhr, status, error);
                    alert('Error: ' + error);
                }
            });
        }
    });

    // Mark as read from modal
    $('#markReadFromModal').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsRead(notificationId);
        $('#notificationModal').modal('hide');
    });

    // Functions
    function refreshStats() {
        $.get('{{ route("owner.notifications.stats") }}')
            .done(function(response) {
                if (response.success) {
                    $('#totalCount').text(response.stats.total);
                    $('#unreadCount').text(response.stats.unread);
                    $('#urgentCount').text(response.stats.urgent);
                    $('#todayCount').text(response.stats.today);
                }
            });
    }

    function loadNotificationDetails(notificationId) {
        $('#notificationModal').modal('show');
        $('#notificationModalBody').html(`
            <div class="text-center py-3">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">Loading notification details...</p>
            </div>
        `);

        $.get(`{{ route("owner.notifications.show", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                if (response.success) {
                    const notification = response.notification;
                    const isRead = notification.is_read;

                    $('#notificationModalBody').html(`
                        <div class="notification-detail">
                            <div class="d-flex align-items-center mb-3">
                                <i class="${getTypeIcon(notification.notification_type)} text-${getTypeColor(notification.notification_type)} fa-2x mr-3"></i>
                                <div>
                                    <h5 class="mb-1">${notification.title}</h5>
                                    <small class="text-muted">
                                        <i class="fas fa-clock mr-1"></i>
                                        ${new Date(notification.created_at).toLocaleString()}
                                        ${notification.priority !== 'normal' ? `• <span class="badge badge-${getPriorityColor(notification.priority)}">${notification.priority.toUpperCase()}</span>` : ''}
                                    </small>
                                </div>
                            </div>
                            <div class="notification-message">
                                <p>${notification.message}</p>
                            </div>
                            ${notification.data && Object.keys(notification.data).length > 0 ? `
                                <div class="notification-data mt-3">
                                    <h6>Additional Details:</h6>
                                    <div class="bg-light p-3 rounded">
                                        ${formatNotificationData(notification.data)}
                                    </div>
                                </div>
                            ` : ''}
                            ${notification.source_type && notification.source_id ? `
                                <div class="notification-source mt-3">
                                    <h6>Source Information:</h6>
                                    <div class="bg-info p-3 rounded text-white">
                                        <i class="fas fa-info-circle mr-2"></i>
                                        Generated from <strong>${notification.source_type.replace('_', ' ')}</strong>
                                        (ID: ${notification.source_id})
                                    </div>
                                </div>
                            ` : ''}
                        </div>
                    `);

                    // Show/hide action buttons based on notification status
                    if (!isRead) {
                        $('#markReadFromModal').show().data('id', notificationId);
                        $('#markUnreadFromModal').hide();
                    } else {
                        $('#markReadFromModal').hide();
                        $('#markUnreadFromModal').show().data('id', notificationId);
                    }
                    $('#deleteFromModal').show().data('id', notificationId);

                    // Auto-mark as read when viewed
                    if (!isRead) {
                        markNotificationAsRead(notificationId, false);
                    }
                } else {
                    $('#notificationModalBody').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            Failed to load notification details.
                        </div>
                    `);
                }
            })
            .fail(function() {
                $('#notificationModalBody').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        Failed to load notification details.
                    </div>
                `);
            });
    }

    function markNotificationAsRead(notificationId) {
        console.log('Marking notification as read:', notificationId);
        $.post(`{{ route("owner.notifications.mark-read", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Mark as read response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    updateNotificationRow(notificationId, true);
                    refreshStats();
                } else {
                    toastr.error('Failed to mark notification as read');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Mark as read failed:', xhr, status, error);
                toastr.error('Failed to mark notification as read: ' + (xhr.responseJSON?.message || error));
            });
    }

    function markNotificationAsUnread(notificationId) {
        console.log('Marking notification as unread:', notificationId);
        $.post(`{{ route("owner.notifications.mark-unread", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Mark as unread response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    updateNotificationRow(notificationId, false);
                    refreshStats();
                } else {
                    toastr.error('Failed to mark notification as unread');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Mark as unread failed:', xhr, status, error);
                toastr.error('Failed to mark notification as unread: ' + (xhr.responseJSON?.message || error));
            });
    }

    function deleteNotification(notificationId) {
        console.log('Deleting notification:', notificationId);
        $.post(`{{ route("owner.notifications.delete", ":id") }}`.replace(':id', notificationId))
            .done(function(response) {
                console.log('Delete response:', response);
                if (response.success) {
                    toastr.success(response.message);
                    $(`tr[data-id="${notificationId}"]`).fadeOut(300, function() {
                        $(this).remove();
                    });
                    refreshStats();
                } else {
                    toastr.error('Failed to delete notification');
                }
            })
            .fail(function(xhr, status, error) {
                console.error('Delete failed:', xhr, status, error);
                toastr.error('Failed to delete notification: ' + (xhr.responseJSON?.message || error));
            });
    }

    function performBulkAction(action, notificationIds) {
        $.post('{{ route("owner.notifications.bulk-action") }}', {
            action: action,
            notification_ids: notificationIds
        })
        .done(function(response) {
            if (response.success) {
                toastr.success(response.message);

                // Update UI based on action
                if (action === 'delete') {
                    notificationIds.forEach(function(id) {
                        $(`tr[data-id="${id}"]`).fadeOut(300, function() {
                            $(this).remove();
                        });
                    });
                } else if (action === 'mark_read') {
                    notificationIds.forEach(function(id) {
                        updateNotificationRow(id, true);
                    });
                } else if (action === 'mark_unread') {
                    notificationIds.forEach(function(id) {
                        updateNotificationRow(id, false);
                    });
                }

                // Clear selections
                $('.notification-checkbox').prop('checked', false);
                $('#selectAll').prop('checked', false);
                updateBulkActionsButton();
                refreshStats();
            } else {
                toastr.error('Failed to perform bulk action');
            }
        })
        .fail(function() {
            toastr.error('Failed to perform bulk action');
        });
    }

    function updateNotificationRow(notificationId, isRead) {
        const row = $(`tr[data-id="${notificationId}"]`);
        const readButton = row.find('.mark-read');
        const unreadButton = row.find('.mark-unread');
        const title = row.find('h6');
        const newBadge = row.find('.badge-primary');

        if (isRead) {
            row.removeClass('table-warning');
            row.data('read', 'true');
            title.removeClass('font-weight-bold');
            newBadge.remove();
            readButton.hide();
            unreadButton.show().removeClass('btn-outline-warning').addClass('btn-outline-warning');
        } else {
            row.addClass('table-warning');
            row.data('read', 'false');
            title.addClass('font-weight-bold');
            if (newBadge.length === 0) {
                title.append('<span class="badge badge-primary badge-sm ml-1">New</span>');
            }
            unreadButton.hide();
            readButton.show().removeClass('btn-outline-success').addClass('btn-outline-success');
        }
    }

    // Helper functions for notification display
    function getTypeIcon(type) {
        const icons = {
            'booking': 'fas fa-calendar-check',
            'cancellation': 'fas fa-calendar-times',
            'payment': 'fas fa-credit-card',
            'review': 'fas fa-star',
            'system': 'fas fa-cog',
            'marketing': 'fas fa-bullhorn',
            'alert': 'fas fa-exclamation-triangle',
            'reminder': 'fas fa-clock',
            'customer_message': 'fas fa-comment',
            'waiting_list': 'fas fa-list'
        };
        return icons[type] || 'fas fa-bell';
    }

    function getTypeColor(type) {
        const colors = {
            'booking': 'success',
            'cancellation': 'danger',
            'payment': 'info',
            'review': 'warning',
            'system': 'secondary',
            'marketing': 'primary',
            'alert': 'danger',
            'reminder': 'warning',
            'customer_message': 'info',
            'waiting_list': 'primary'
        };
        return colors[type] || 'info';
    }

    function getPriorityColor(priority) {
        const colors = {
            'urgent': 'danger',
            'high': 'warning',
            'normal': 'info',
            'low': 'secondary'
        };
        return colors[priority] || 'info';
    }

    function formatNotificationData(data) {
        let html = '';
        for (const [key, value] of Object.entries(data)) {
            if (value !== null && value !== undefined && value !== '') {
                const formattedKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                let formattedValue = value;

                // Format specific data types
                if (key.includes('date') || key.includes('time')) {
                    formattedValue = new Date(value).toLocaleString();
                } else if (key.includes('amount') || key.includes('value')) {
                    formattedValue = `$${parseFloat(value).toFixed(2)}`;
                } else if (typeof value === 'object') {
                    formattedValue = JSON.stringify(value, null, 2);
                }

                html += `<div class="row mb-1">
                    <div class="col-4"><strong>${formattedKey}:</strong></div>
                    <div class="col-8">${formattedValue}</div>
                </div>`;
            }
        }
        return html || '<p class="text-muted">No additional details available.</p>';
    }

    // Real-time search
    let searchTimeout;
    $('#search').on('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            $('#filterForm').submit();
        }, 500);
    });

    // Auto-submit filters on change
    $('#type, #priority, #status, #date_from, #date_to').change(function() {
        $('#filterForm').submit();
    });

    // Modal action button handlers
    $('#markReadFromModal').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsRead(notificationId);
        $('#notificationModal').modal('hide');
    });

    $('#markUnreadFromModal').click(function() {
        const notificationId = $(this).data('id');
        markNotificationAsUnread(notificationId);
        $('#notificationModal').modal('hide');
    });

    $('#deleteFromModal').click(function() {
        const notificationId = $(this).data('id');
        if (confirm('Are you sure you want to delete this notification?')) {
            deleteNotification(notificationId);
            $('#notificationModal').modal('hide');
        }
    });

    // Sound toggle functionality
    $('#soundToggleBtn').click(function() {
        if (window.notificationSounds) {
            if (window.notificationSounds.isEnabled()) {
                window.notificationSounds.disable();
                $(this).find('i').removeClass('fa-volume-up').addClass('fa-volume-mute');
                $('#soundStatus').text('Sound Off');
                $(this).removeClass('btn-outline-info').addClass('btn-outline-secondary');
            } else {
                window.notificationSounds.enable();
                $(this).find('i').removeClass('fa-volume-mute').addClass('fa-volume-up');
                $('#soundStatus').text('Sound On');
                $(this).removeClass('btn-outline-secondary').addClass('btn-outline-info');
                // Test sound
                window.notificationSounds.testSound();
            }
        }
    });

    // Export functionality
    $('#exportBtn').click(function() {
        // Show export modal or trigger export
        const format = prompt('Export format (csv, excel, json, pdf):', 'csv');
        if (format && ['csv', 'excel', 'json', 'pdf'].includes(format.toLowerCase())) {
            exportNotifications(format.toLowerCase());
        }
    });

    // Initialize sound system status
    if (window.notificationSounds) {
        const isEnabled = window.notificationSounds.isEnabled();
        if (!isEnabled) {
            $('#soundToggleBtn').find('i').removeClass('fa-volume-up').addClass('fa-volume-mute');
            $('#soundStatus').text('Sound Off');
            $('#soundToggleBtn').removeClass('btn-outline-info').addClass('btn-outline-secondary');
        }
    }

    // Export notifications function
    function exportNotifications(format) {
        const filters = {
            type: $('#type').val(),
            priority: $('#priority').val(),
            status: $('#status').val(),
            date_from: $('#date_from').val(),
            date_to: $('#date_to').val(),
            search: $('#search').val()
        };

        $.ajax({
            url: '{{ route("owner.notifications.export") }}',
            type: 'POST',
            data: {
                format: format,
                ...filters
            },
            success: function(response) {
                if (response.success && response.download_url) {
                    // Create temporary download link
                    const link = document.createElement('a');
                    link.href = response.download_url;
                    link.download = response.filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    toastr.success(`Export completed: ${response.filename}`);
                } else {
                    toastr.error('Export failed');
                }
            },
            error: function() {
                toastr.error('Export failed');
            }
        });
    }

    // Real-time notification handling
    if (window.Echo && typeof window.Echo.private === 'function') {
        window.Echo.private(`owner-notifications.{{ auth()->id() }}`)
            .listen('notification.created', (e) => {
                console.log('New notification received:', e.notification);

                // Play sound if enabled
                if (window.notificationSounds && window.notificationSounds.isEnabled()) {
                    window.notificationSounds.playSound(
                        e.notification.notification_type,
                        e.notification.priority
                    );
                }

                // Show toast notification
                if (typeof toastr !== 'undefined') {
                    toastr.info(e.notification.message, e.notification.title, {
                        timeOut: 5000,
                        onclick: function() {
                            loadNotificationDetails(e.notification.id);
                        }
                    });
                }

                // Refresh stats and list
                refreshStats();
                setTimeout(() => location.reload(), 1000);
            })
            .listen('notification.updated', (e) => {
                console.log('Notification updated:', e.notification);
                refreshStats();
            });
    }
});
</script>

{{-- Include notification sound system --}}
<script src="{{ asset('js/notification-sounds.js') }}"></script>
@stop