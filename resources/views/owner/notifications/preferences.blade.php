@extends('owner.layouts.app')

@section('title', 'Notification Preferences')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notification Preferences</h1>
            <p class="text-muted">Customize how and when you receive notifications</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <form method="POST" action="{{ route('owner.notification-preferences.reset') }}" style="display: inline;">
                @csrf
                <button type="submit" class="btn btn-outline-secondary" id="resetPreferencesBtn">
                    <i class="fas fa-undo"></i> Reset to Defaults
                </button>
            </form>
            
            <button type="button" class="btn btn-outline-info" id="testNotificationBtn" onclick="$('#testNotificationModal').modal('show');">
                <i class="fas fa-bell"></i> Test Notification
            </button>
            
            <form method="POST" action="{{ route('owner.notification-preferences.update') }}" style="display: inline;">
                @csrf
                <input type="hidden" name="direct" value="1">
                <button type="submit" class="btn btn-primary" id="directSaveBtn">
                    <i class="fas fa-save"></i> Save Preferences
                </button>
            </form>
            
            <button type="button" class="btn btn-outline-warning btn-sm" id="debugBtn" style="margin-left: 10px;" onclick="alert('Debug mode: Check console for details'); console.log('Buttons:', document.querySelectorAll('button').length);">
                <i class="fas fa-bug"></i> Debug
            </button>
        </div>
    </div>
@stop

@push('css')
<style>
.preference-card {
    transition: all 0.3s ease;
}
.preference-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.channel-toggle {
    margin-bottom: 10px;
}
.global-settings {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
.notification-type-card {
    border-left: 4px solid #007bff;
}
.notification-type-card.booking { border-left-color: #28a745; }
.notification-type-card.cancellation { border-left-color: #dc3545; }
.notification-type-card.payment { border-left-color: #17a2b8; }
.notification-type-card.review { border-left-color: #ffc107; }
.notification-type-card.system { border-left-color: #6c757d; }
.notification-type-card.marketing { border-left-color: #007bff; }
.notification-type-card.alert { border-left-color: #dc3545; }
.notification-type-card.reminder { border-left-color: #ffc107; }
.notification-type-card.customer_message { border-left-color: #17a2b8; }
.notification-type-card.waiting_list { border-left-color: #007bff; }
</style>
@endpush

@section('content')
    {{-- Global Settings Card --}}
    <div class="card global-settings mb-4">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-globe mr-2"></i>
                Global Notification Settings
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool text-white" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Delivery Channels</h5>
                    <div class="form-group">
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalEmailEnabled">
                            <label class="custom-control-label" for="globalEmailEnabled">
                                <i class="fas fa-envelope mr-2"></i>Email Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSmsEnabled">
                            <label class="custom-control-label" for="globalSmsEnabled">
                                <i class="fas fa-sms mr-2"></i>SMS Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalPushEnabled">
                            <label class="custom-control-label" for="globalPushEnabled">
                                <i class="fas fa-bell mr-2"></i>Push Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSoundEnabled">
                            <label class="custom-control-label" for="globalSoundEnabled">
                                <i class="fas fa-volume-up mr-2"></i>Sound Alerts
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Timing Settings</h5>
                    <div class="form-group">
                        <label>Quiet Hours</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursStart" placeholder="Start">
                            </div>
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursEnd" placeholder="End">
                            </div>
                        </div>
                        <small class="text-muted">No notifications during these hours</small>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="globalWeekendNotifications">
                            <label class="custom-control-label" for="globalWeekendNotifications">
                                Weekend Notifications
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Digest Frequency</label>
                        <select class="form-control" id="globalDigestFrequency">
                            <option value="never">Never</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button type="button" class="btn btn-light" id="applyGlobalSettingsBtn" onclick="
                    try {
                        // Get global settings
                        const emailEnabled = document.getElementById('globalEmailEnabled').checked;
                        const smsEnabled = document.getElementById('globalSmsEnabled').checked;
                        const pushEnabled = document.getElementById('globalPushEnabled').checked;
                        
                        // Apply to all notification types
                        document.querySelectorAll('.notification-type-card').forEach(function(card) {
                            const type = card.getAttribute('data-type');
                            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);
                            
                            // Apply channel settings
                            document.getElementById('email' + typeCapitalized).checked = emailEnabled;
                            document.getElementById('sms' + typeCapitalized).checked = smsEnabled;
                            document.getElementById('push' + typeCapitalized).checked = pushEnabled;
                            
                            // Try to set in-app and auto-mark-read if they exist
                            const inAppEl = document.getElementById('inApp' + typeCapitalized);
                            if (inAppEl) inAppEl.checked = true;
                            
                            const autoMarkEl = document.getElementById('autoMarkRead' + typeCapitalized);
                            if (autoMarkEl) autoMarkEl.checked = false;
                        });
                        
                        alert('Global settings applied to all notification types!');
                    } catch (error) {
                        console.error('Error:', error);
                        alert('Error applying settings: ' + error.message);
                    }
                ">
                    <i class="fas fa-magic mr-2"></i>Apply to All Notification Types
                </button>
            </div>
        </div>
    </div>

    {{-- Individual Notification Type Preferences --}}
    <div class="row">
        @foreach($notificationTypes as $type => $label)
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card preference-card notification-type-card {{ $type }}" data-type="{{ $type }}">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            @php
                                $icons = [
                                    'booking' => 'fas fa-calendar-check',
                                    'cancellation' => 'fas fa-calendar-times',
                                    'payment' => 'fas fa-credit-card',
                                    'review' => 'fas fa-star',
                                    'system' => 'fas fa-cog',
                                    'marketing' => 'fas fa-bullhorn',
                                    'alert' => 'fas fa-exclamation-triangle',
                                    'reminder' => 'fas fa-clock',
                                    'customer_message' => 'fas fa-comment',
                                    'waiting_list' => 'fas fa-list'
                                ];
                            @endphp
                            <i class="{{ $icons[$type] ?? 'fas fa-bell' }} mr-2"></i>
                            {{ $label }}
                        </h5>
                        <div class="card-tools">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input master-toggle"
                                       id="masterToggle{{ ucfirst($type) }}"
                                       data-type="{{ $type }}">
                                <label class="custom-control-label" for="masterToggle{{ ucfirst($type) }}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {{-- Delivery Channels --}}
                        <div class="mb-3">
                            <h6>Delivery Channels</h6>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="email{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="email">
                                <label class="custom-control-label" for="email{{ ucfirst($type) }}">
                                    <i class="fas fa-envelope mr-1"></i>Email
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="sms{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="sms">
                                <label class="custom-control-label" for="sms{{ ucfirst($type) }}">
                                    <i class="fas fa-sms mr-1"></i>SMS
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="push{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="push">
                                <label class="custom-control-label" for="push{{ ucfirst($type) }}">
                                    <i class="fas fa-bell mr-1"></i>Push
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="inApp{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="in_app">
                                <label class="custom-control-label" for="inApp{{ ucfirst($type) }}">
                                    <i class="fas fa-desktop mr-1"></i>In-App
                                </label>
                            </div>
                        </div>

                        {{-- Priority Filter --}}
                        <div class="mb-3">
                            <label class="form-label">Minimum Priority</label>
                            <select class="form-control form-control-sm priority-filter" data-type="{{ $type }}">
                                <option value="">All Priorities</option>
                                <option value="low">Low and above</option>
                                <option value="normal">Normal and above</option>
                                <option value="high">High and above</option>
                                <option value="urgent">Urgent only</option>
                            </select>
                        </div>

                        {{-- Additional Settings --}}
                        <div class="mb-2">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input auto-mark-read"
                                       id="autoMarkRead{{ ucfirst($type) }}"
                                       data-type="{{ $type }}">
                                <label class="custom-control-label" for="autoMarkRead{{ ucfirst($type) }}">
                                    Auto-mark as read
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    {{-- Test Notification Modal --}}
    <div class="modal fade" id="testNotificationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form action="{{ route('owner.notification-preferences.test') }}" method="POST" id="testNotificationForm">
                    @csrf
                    <div class="modal-header">
                        <h4 class="modal-title">
                            <i class="fas fa-bell mr-2"></i>
                            Test Notification
                        </h4>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>Notification Type</label>
                            <select class="form-control" name="notification_type" id="testNotificationType">
                                @foreach($notificationTypes as $type => $label)
                                    <option value="{{ $type }}">{{ $label }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Test Channels</label>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="testEmail" name="channels[]" value="email">
                                <label class="custom-control-label" for="testEmail">Email</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="testSms" name="channels[]" value="sms">
                                <label class="custom-control-label" for="testSms">SMS</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="testPush" name="channels[]" value="push">
                                <label class="custom-control-label" for="testPush">Push Notification</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="testInApp" name="channels[]" value="in_app" checked>
                                <label class="custom-control-label" for="testInApp">In-App Notification</label>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="sendTestNotificationBtn">
                            <i class="fas fa-paper-plane mr-2"></i>Send Test
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
// Function to apply global settings to all notification types
function applyGlobalSettings() {
    console.log('Applying global settings to all notification types');
    
    try {
        // Get global settings
        const emailEnabled = $('#globalEmailEnabled').is(':checked');
        const smsEnabled = $('#globalSmsEnabled').is(':checked');
        const pushEnabled = $('#globalPushEnabled').is(':checked');
        const soundEnabled = $('#globalSoundEnabled').is(':checked');
        
        // Apply to all notification types
        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);
            
            // Apply channel settings
            $(`#email${typeCapitalized}`).prop('checked', emailEnabled);
            $(`#sms${typeCapitalized}`).prop('checked', smsEnabled);
            $(`#push${typeCapitalized}`).prop('checked', pushEnabled);
            $(`#inApp${typeCapitalized}`).prop('checked', true); // Always enabled
            
            // Apply other settings if needed
            $(`#autoMarkRead${typeCapitalized}`).prop('checked', false); // Default
        });
        
        // Show success message
        alert('Global settings applied to all notification types!');
        
    } catch (error) {
        console.error('Error applying global settings:', error);
        alert('Error applying global settings. Please try again.');
    }
}

// Simple function to submit the preferences form
function submitPreferences() {
    try {
        // Collect all preferences data
        const preferences = [];
        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

            preferences.push({
                notification_type: type,
                email_enabled: $(`#email${typeCapitalized}`).is(':checked'),
                sms_enabled: $(`#sms${typeCapitalized}`).is(':checked'),
                push_enabled: $(`#push${typeCapitalized}`).is(':checked'),
                in_app_enabled: $(`#inApp${typeCapitalized}`).is(':checked'),
                sound_enabled: true,
                priority_filter: $(`.priority-filter[data-type="${type}"]`).val() || null,
                quiet_hours_start: null,
                quiet_hours_end: null,
                weekend_notifications: true,
                digest_frequency: 'daily',
                auto_mark_read: $(`#autoMarkRead${typeCapitalized}`).is(':checked')
            });
        });

        // Set the form data
        $('#preferencesData').val(JSON.stringify(preferences));
        
        // Submit the form
        $('#preferencesForm').submit();
        
    } catch (e) {
        console.error('Error submitting preferences:', e);
        alert('Error: Could not save preferences. ' + e.message);
    }
}

window.savePrefs = function() {
    console.log('Direct save button clicked');
    try {
        // Direct AJAX call to save preferences
        const preferences = [];
        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

            preferences.push({
                notification_type: type,
                email_enabled: $(`#email${typeCapitalized}`).is(':checked'),
                sms_enabled: $(`#sms${typeCapitalized}`).is(':checked'),
                push_enabled: $(`#push${typeCapitalized}`).is(':checked'),
                in_app_enabled: $(`#inApp${typeCapitalized}`).is(':checked'),
                sound_enabled: true,
                priority_filter: $(`.priority-filter[data-type="${type}"]`).val() || null,
                quiet_hours_start: null,
                quiet_hours_end: null,
                weekend_notifications: true,
                digest_frequency: 'daily',
                auto_mark_read: $(`#autoMarkRead${typeCapitalized}`).is(':checked')
            });
        });

        const token = $('meta[name="csrf-token"]').attr('content');
        
        $.ajax({
            url: '{{ route("owner.notification-preferences.update") }}',
            type: 'POST',
            dataType: 'json',
            headers: {
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            data: JSON.stringify({ preferences: preferences }),
            contentType: 'application/json',
            success: function(response) {
                alert(response.message || 'Preferences saved successfully!');
            },
            error: function(xhr) {
                console.error('Save error:', xhr);
                alert('Error saving preferences. Check console for details.');
            }
        });
    } catch (e) {
        console.error('Error in savePrefs:', e);
        alert('Error: Could not save preferences. ' + e.message);
    }
};

window.resetPrefs = function() {
    console.log('Direct reset button clicked');
    try {
        if (confirm('Are you sure you want to reset all preferences to defaults? This action cannot be undone.')) {
            const token = $('meta[name="csrf-token"]').attr('content');
            
            $.ajax({
                url: '{{ route("owner.notification-preferences.reset") }}',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json'
                },
                success: function(response) {
                    alert(response.message || 'Preferences reset successfully!');
                    location.reload();
                },
                error: function(xhr) {
                    console.error('Reset error:', xhr);
                    alert('Error resetting preferences. Check console for details.');
                }
            });
        }
    } catch (e) {
        console.error('Error in resetPrefs:', e);
        alert('Error: Could not reset preferences. ' + e.message);
    }
};

window.testNotif = function() {
    console.log('Direct test notification button clicked');
    try {
        if (typeof $ !== 'undefined' && typeof $('#testNotificationModal').modal === 'function') {
            $('#testNotificationModal').modal('show');
        } else {
            alert('Could not show test notification modal. Bootstrap modal may not be available.');
        }
    } catch (e) {
        console.error('Error in testNotif:', e);
        alert('Error: Could not show test notification modal. ' + e.message);
    }
};

window.showDebugInfo = function() {
    console.log('Direct debug button clicked');
    const debugInfo = {
        'jQuery loaded': typeof $ !== 'undefined',
        'CSRF token': $('meta[name="csrf-token"]').attr('content') ? 'Found' : 'Not found',
        'Bootstrap modal': typeof $.fn.modal !== 'undefined' ? 'Available' : 'Not available',
        'Toastr': typeof toastr !== 'undefined' ? 'Available' : 'Not available',
        'Save button': $('#savePreferencesBtn').length > 0 ? 'Found' : 'Not found',
        'Reset button': $('#resetPreferencesBtn').length > 0 ? 'Found' : 'Not found',
        'Test button': $('#testNotificationBtn').length > 0 ? 'Found' : 'Not found'
    };
    
    console.table(debugInfo);
    alert('Debug info logged to console. Press F12 to view details.');
};

// Notification Preferences JavaScript
(function() {
    'use strict';

    // Debug logging function
    function debugLog(message, data = null) {
        console.log('[NotificationPreferences]', message, data || '');
    }

    // Show notification function
    function showNotification(message, type = 'info') {
        if (typeof toastr !== 'undefined') {
            toastr[type](message);
        } else {
            alert(message);
        }
    }

    // Button state management
    function setButtonLoading(buttonId, isLoading, loadingText, normalText) {
        const button = $('#' + buttonId);
        if (isLoading) {
            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> ' + loadingText);
        } else {
            button.prop('disabled', false).html(normalText);
        }
    }

    // Validate environment
    function validateEnvironment() {
        console.log('Validating environment');
        const issues = [];

        if (typeof $ === 'undefined') {
            issues.push('jQuery is not loaded');
            console.error('jQuery is not loaded');
        } else {
            console.log('jQuery is loaded correctly');
        }

        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        if (!csrfToken) {
            issues.push('CSRF token meta tag not found or empty');
            console.error('CSRF token meta tag not found or empty');
        } else {
            console.log('CSRF token found:', csrfToken.substring(0, 5) + '...');
        }

        const saveBtn = $('#savePreferencesBtn');
        if (saveBtn.length === 0) {
            issues.push('Save button not found');
            console.error('Save button not found');
        } else {
            console.log('Save button found with ID:', saveBtn.attr('id'));
        }

        const resetBtn = $('#resetPreferencesBtn');
        if (resetBtn.length === 0) {
            issues.push('Reset button not found');
            console.error('Reset button not found');
        } else {
            console.log('Reset button found with ID:', resetBtn.attr('id'));
        }

        if ($('#testNotificationBtn').length === 0) {
            issues.push('Test button not found');
        }

        if (issues.length > 0) {
            debugLog('Environment validation failed:', issues);
            showNotification('Page not fully loaded. Please refresh and try again.', 'error');
            return false;
        }

        debugLog('Environment validation passed');
        return true;
    }

    // Send test notification function
    function sendTestNotification() {
        debugLog('Send test notification called');

        if (!validateEnvironment()) return;

        const notificationType = $('#testNotificationType').val();
        const channels = [];

        if ($('#testEmailChannel').is(':checked')) channels.push('email');
        if ($('#testSmsChannel').is(':checked')) channels.push('sms');
        if ($('#testPushChannel').is(':checked')) channels.push('push');
        if ($('#testInAppChannel').is(':checked')) channels.push('in_app');

        if (channels.length === 0) {
            showNotification('Please select at least one notification channel', 'warning');
            return;
        }

        debugLog('Sending test notification', { type: notificationType, channels: channels });
        setButtonLoading('sendTestNotificationBtn', true, 'Sending...', '<i class="fas fa-paper-plane mr-2"></i>Send Test');

        // Get the CSRF token from the meta tag
        const token = $('meta[name="csrf-token"]').attr('content');
        
        if (!token) {
            showNotification('CSRF token not found. Please refresh the page and try again.', 'error');
            setButtonLoading('sendTestNotificationBtn', false, '', '<i class="fas fa-paper-plane mr-2"></i>Send Test');
            return;
        }

        $.ajax({
            url: '{{ route("owner.notification-preferences.test") }}',
            type: 'POST',
            dataType: 'json',
            headers: {
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            data: {
                notification_type: notificationType,
                channels: channels
            },
            success: function(response) {
                debugLog('Test notification response:', response);
                if (response.success) {
                    showNotification(response.message || 'Test notification sent successfully!', 'success');
                    $('#testNotificationModal').modal('hide');
                } else {
                    showNotification(response.message || 'Failed to send test notification', 'error');
                }
            },
            error: function(xhr) {
                debugLog('Test notification error:', xhr);
                let errorMessage = 'Failed to send test notification';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification(errorMessage, 'error');
                console.error('XHR Status:', xhr.status);
                console.error('XHR Response Text:', xhr.responseText);
            },
            complete: function() {
                setButtonLoading('sendTestNotificationBtn', false, '', '<i class="fas fa-paper-plane mr-2"></i>Send Test');
            }
        });
    }

    // Save preferences function
    function savePreferences() {
        debugLog('Save preferences called');

        if (!validateEnvironment()) return;

        const preferences = [];

        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

            preferences.push({
                notification_type: type,
                email_enabled: $(`#email${typeCapitalized}`).is(':checked'),
                sms_enabled: $(`#sms${typeCapitalized}`).is(':checked'),
                push_enabled: $(`#push${typeCapitalized}`).is(':checked'),
                in_app_enabled: $(`#inApp${typeCapitalized}`).is(':checked'),
                sound_enabled: true, // Always enabled for now
                priority_filter: $(`.priority-filter[data-type="${type}"]`).val() || null,
                quiet_hours_start: null, // Global setting for now
                quiet_hours_end: null, // Global setting for now
                weekend_notifications: true, // Global setting for now
                digest_frequency: 'daily', // Global setting for now
                auto_mark_read: $(`#autoMarkRead${typeCapitalized}`).is(':checked')
            });
        });

        debugLog('Preferences to save:', preferences);
        setButtonLoading('savePreferencesBtn', true, 'Saving...', '<i class="fas fa-save"></i> Save Preferences');

        // Get the CSRF token from the meta tag
        const token = $('meta[name="csrf-token"]').attr('content');
        
        if (!token) {
            showNotification('CSRF token not found. Please refresh the page and try again.', 'error');
            setButtonLoading('savePreferencesBtn', false, '', '<i class="fas fa-save"></i> Save Preferences');
            return;
        }

        $.ajax({
            url: '{{ route("owner.notification-preferences.update") }}',
            type: 'POST',
            dataType: 'json',
            contentType: 'application/json',
            headers: {
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            data: JSON.stringify({ preferences: preferences }),
            success: function(response) {
                debugLog('Save preferences response:', response);
                if (response.success) {
                    showNotification(response.message || 'Preferences saved successfully!', 'success');
                } else {
                    showNotification(response.message || 'Failed to save preferences', 'error');
                }
            },
            error: function(xhr) {
                debugLog('Save preferences error:', xhr);
                let errorMessage = 'Failed to save preferences';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
                }
                showNotification(errorMessage, 'error');
                console.error('XHR Status:', xhr.status);
                console.error('XHR Response Text:', xhr.responseText);
            },
            complete: function() {
                setButtonLoading('savePreferencesBtn', false, '', '<i class="fas fa-save"></i> Save Preferences');
            }
        });
    }

    // Reset preferences function
    function resetPreferences() {
        debugLog('Reset preferences called');

        if (!validateEnvironment()) return;

        if (!confirm('Are you sure you want to reset all preferences to defaults? This action cannot be undone.')) {
            return;
        }

        setButtonLoading('resetPreferencesBtn', true, 'Resetting...', '<i class="fas fa-undo"></i> Reset to Defaults');

        // Get the CSRF token from the meta tag
        const token = $('meta[name="csrf-token"]').attr('content');
        
        if (!token) {
            showNotification('CSRF token not found. Please refresh the page and try again.', 'error');
            setButtonLoading('resetPreferencesBtn', false, '', '<i class="fas fa-undo"></i> Reset to Defaults');
            return;
        }

        $.ajax({
            url: '{{ route("owner.notification-preferences.reset") }}',
            type: 'POST',
            dataType: 'json',
            headers: {
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            success: function(response) {
                debugLog('Reset preferences response:', response);
                if (response.success) {
                    showNotification(response.message || 'Preferences reset to defaults successfully!', 'success');
                    // Reload the page after a short delay to show the success message
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showNotification(response.message || 'Failed to reset preferences', 'error');
                }
            },
            error: function(xhr) {
                debugLog('Reset preferences error:', xhr);
                let errorMessage = 'Failed to reset preferences';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showNotification(errorMessage, 'error');
                console.error('XHR Status:', xhr.status);
                console.error('XHR Response Text:', xhr.responseText);
            },
            complete: function() {
                setButtonLoading('resetPreferencesBtn', false, '', '<i class="fas fa-undo"></i> Reset to Defaults');
            }
        });
    }

    // Document ready initialization
    $(document).ready(function() {
        debugLog('Document ready - initializing notification preferences');
        console.log('Document ready - initializing notification preferences');
        
        // Handle test notification form submission
        $('#testNotificationForm').on('submit', function(e) {
            console.log('Test notification form submitted');
            
            // Check if at least one channel is selected
            if (!$('input[name="channels[]"]:checked').length) {
                e.preventDefault();
                alert('Please select at least one notification channel');
                return false;
            }
            
            // Show loading state on button
            const btn = $('#sendTestNotificationBtn');
            btn.prop('disabled', true);
            btn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Sending...');
        });

        // Check if jQuery is properly loaded
        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded!');
            alert('Error: jQuery is not loaded. Please check the console for more details.');
            return;
        }

        // Check if CSRF token exists
        const csrfToken = $('meta[name="csrf-token"]').attr('content');
        console.log('CSRF token exists:', !!csrfToken);
        if (!csrfToken) {
            console.error('CSRF token not found in meta tags');
        }

        // CSRF token setup
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': csrfToken
            }
        });

        // Validate environment on load
        if (!validateEnvironment()) {
            debugLog('Environment validation failed on load');
            console.error('Environment validation failed on load');
            return;
        }

        // Button event handlers with direct binding and visual feedback
        console.log('Setting up button event handlers');
        
        // Save button
        const saveBtn = document.getElementById('savePreferencesBtn');
        console.log('Save button found:', !!saveBtn);
        if (saveBtn) {
            saveBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Save button clicked');
                debugLog('Save button clicked');
                this.classList.add('btn-success');
                this.classList.remove('btn-primary');
                setTimeout(() => {
                    this.classList.remove('btn-success');
                    this.classList.add('btn-primary');
                }, 200);
                savePreferences();
            });
        } else {
            console.error('Save button not found in the DOM');
        }

        // Reset button
        const resetBtn = document.getElementById('resetPreferencesBtn');
        console.log('Reset button found:', !!resetBtn);
        if (resetBtn) {
            resetBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Reset button clicked');
                debugLog('Reset button clicked');
                this.classList.add('btn-danger');
                this.classList.remove('btn-outline-secondary');
                setTimeout(() => {
                    this.classList.remove('btn-danger');
                    this.classList.add('btn-outline-secondary');
                }, 200);
                resetPreferences();
            });
        } else {
            console.error('Reset button not found in the DOM');
        }

        // Test notification button
        const testBtn = document.getElementById('testNotificationBtn');
        console.log('Test button found:', !!testBtn);
        if (testBtn) {
            testBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Test notification button clicked');
                debugLog('Test notification button clicked');
                this.classList.add('btn-info');
                this.classList.remove('btn-outline-info');
                setTimeout(() => {
                    this.classList.remove('btn-info');
                    this.classList.add('btn-outline-info');
                }, 200);
                if (validateEnvironment()) {
                    $('#testNotificationModal').modal('show');
                }
            });
        } else {
            console.error('Test notification button not found in the DOM');
        }

        // Send test notification button
        const sendTestBtn = document.getElementById('sendTestNotificationBtn');
        console.log('Send test button found:', !!sendTestBtn);
        if (sendTestBtn) {
            sendTestBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Send test notification button clicked');
                debugLog('Send test notification button clicked');
                sendTestNotification();
            });
        } else {
            console.error('Send test notification button not found in the DOM');
        }
        
        // Fallback to jQuery event handlers if needed
        $('#savePreferencesBtn').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('jQuery save button clicked');
            savePreferences();
        });
        
        $('#resetPreferencesBtn').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('jQuery reset button clicked');
            resetPreferences();
        });
        
        $('#testNotificationBtn').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('jQuery test button clicked');
            if (validateEnvironment()) {
                $('#testNotificationModal').modal('show');
            }
        });
        
        $('#sendTestNotificationBtn').off('click').on('click', function(e) {
            e.preventDefault();
            console.log('jQuery send test button clicked');
            sendTestNotification();
        });

        // Debug button for troubleshooting
        $('#debugBtn').off('click').on('click', function(e) {
            e.preventDefault();
            debugLog('Debug button clicked');

            const debugInfo = {
                'jQuery loaded': typeof $ !== 'undefined',
                'CSRF token': $('meta[name="csrf-token"]').attr('content'),
                'Save button exists': $('#savePreferencesBtn').length > 0,
                'Reset button exists': $('#resetPreferencesBtn').length > 0,
                'Test button exists': $('#testNotificationBtn').length > 0,
                'Toastr available': typeof toastr !== 'undefined',
                'Bootstrap modal available': typeof $.fn.modal !== 'undefined',
                'Notification cards count': $('.notification-type-card').length,
                'Current URL': window.location.href,
                'User agent': navigator.userAgent
            };

            console.table(debugInfo);

            let message = 'Debug Information:\n\n';
            for (const [key, value] of Object.entries(debugInfo)) {
                message += `${key}: ${value}\n`;
            }

            alert(message);
        });

        // Load current preferences
        loadPreferences();

        // Master toggle for each notification type
        $('.master-toggle').change(function() {
            const type = $(this).data('type');
            const enabled = $(this).is(':checked');
            const card = $(`.notification-type-card[data-type="${type}"]`);

            // Toggle all channels for this type
            card.find('.channel-toggle-input').prop('checked', enabled);

            // Enable/disable the card
            card.toggleClass('disabled', !enabled);
            card.find('input, select').not('.master-toggle').prop('disabled', !enabled);
        });

        // Apply global settings to all types
        $('#applyGlobalSettingsBtn').click(function() {
            const globalSettings = {
                email: $('#globalEmailEnabled').is(':checked'),
                sms: $('#globalSmsEnabled').is(':checked'),
                push: $('#globalPushEnabled').is(':checked'),
                sound: $('#globalSoundEnabled').is(':checked'),
                quietStart: $('#globalQuietHoursStart').val(),
                quietEnd: $('#globalQuietHoursEnd').val(),
                weekend: $('#globalWeekendNotifications').is(':checked'),
                digest: $('#globalDigestFrequency').val()
            };

            // Apply to all notification types
            $('.notification-type-card').each(function() {
                const type = $(this).data('type');
                $(this).find(`#email${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.email);
                $(this).find(`#sms${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.sms);
                $(this).find(`#push${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.push);
                $(this).find(`#inApp${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', true); // Always enable in-app
            });

            showNotification('Global settings applied to all notification types', 'success');
        });

        // Load preferences function
        function loadPreferences() {
            debugLog('Loading preferences from server data');
            @foreach($preferences as $preference)
                const type{{ ucfirst($preference->notification_type) }} = @json($preference);
                loadPreferenceData('{{ $preference->notification_type }}', type{{ ucfirst($preference->notification_type) }});
            @endforeach
        }

        function loadPreferenceData(type, data) {
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

            // Set channel toggles
            $(`#email${typeCapitalized}`).prop('checked', data.email_enabled);
            $(`#sms${typeCapitalized}`).prop('checked', data.sms_enabled);
            $(`#push${typeCapitalized}`).prop('checked', data.push_enabled);
            $(`#inApp${typeCapitalized}`).prop('checked', data.in_app_enabled);

            // Set priority filter
            $(`.priority-filter[data-type="${type}"]`).val(data.priority_filter || '');

            // Set auto-mark read
            $(`#autoMarkRead${typeCapitalized}`).prop('checked', data.auto_mark_read);

            // Set master toggle based on whether any channel is enabled
            const anyEnabled = data.email_enabled || data.sms_enabled || data.push_enabled || data.in_app_enabled;
            $(`#masterToggle${typeCapitalized}`).prop('checked', anyEnabled);

            // Trigger master toggle to set card state
            $(`#masterToggle${typeCapitalized}`).trigger('change');
        }

        debugLog('Notification preferences initialized successfully');
    });

    // Helper function to get notification type icon
    function getTypeIcon(type) {
        const icons = {
            'booking': 'fas fa-calendar-check',
            'cancellation': 'fas fa-calendar-times',
            'payment': 'fas fa-credit-card',
            'review': 'fas fa-star',
            'system': 'fas fa-cog',
            'marketing': 'fas fa-bullhorn',
            'alert': 'fas fa-exclamation-triangle',
            'reminder': 'fas fa-clock',
            'customer_message': 'fas fa-comment',
            'waiting_list': 'fas fa-list'
        };
        return icons[type] || 'fas fa-bell';
    }

})(); // End IIFE
</script>
@stop
